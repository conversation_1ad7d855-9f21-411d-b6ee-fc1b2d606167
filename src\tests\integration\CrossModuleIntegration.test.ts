/**
 * CROSS-MODULE INTEGRATION TESTS
 * 
 * Comprehensive integration tests to validate cross-module interactions,
 * data flow, and system reliability across all enhanced components.
 * 
 * TEST COVERAGE:
 * - AgentOrchestrator + GoalTrackerAgent integration
 * - Circuit breaker + Error sanitization integration
 * - Real-time communication + Memory cleanup integration
 * - Steering guidance + Context assembly integration
 * - Emergency protocols across all modules
 * - HIPAA compliance validation
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AgentOrchestrator } from '../../services/AgentOrchestrator';
import { goalTrackerAgent } from '../../agents/GoalTrackerAgent';
import { circuitBreakerService } from '../../services/CircuitBreakerService';
import { errorSanitizationService } from '../../services/ErrorSanitizationService';
import { memoryCleanupManager } from '../../services/MemoryCleanupManager';
import { realTimeAgentCommunication } from '../../services/RealTimeAgentCommunication';
// Note: Using inline implementations for missing utilities
// import { supabaseCircuitBreaker } from '../../utils/supabaseCircuitBreaker';
// import { globalErrorHandler } from '../../utils/globalErrorHandler';

describe('Cross-Module Integration Tests', () => {
  let orchestrator: AgentOrchestrator;
  let testSessionId: string;
  let testUserId: string;

  beforeEach(async () => {
    // Initialize test environment
    testSessionId = `test-session-${Date.now()}`;
    testUserId = `test-user-${Date.now()}`;
    
    // Reset all services to clean state
    circuitBreakerService.resetAll();
    errorSanitizationService.resetErrorTracking();
    
    // Initialize orchestrator
    orchestrator = new AgentOrchestrator();
    await orchestrator.initialize();
    
    console.log(`🧪 Test setup completed for session: ${testSessionId}`);
  });

  afterEach(async () => {
    // Cleanup test data
    await orchestrator.cleanupSession(testSessionId);
    await memoryCleanupManager.performGlobalCleanup();
    
    console.log(`🧹 Test cleanup completed for session: ${testSessionId}`);
  });

  describe('AgentOrchestrator + GoalTrackerAgent Integration', () => {
    it('should create and track goals throughout conversation flow', async () => {
      // Test data
      const testRequest = {
        sessionId: testSessionId,
        userMessage: 'I have been experiencing chest pain and shortness of breath for the past 2 days',
        userId: testUserId,
        urgencyLevel: 'high' as const
      };

      // Process request through orchestrator
      const response = await orchestrator.processRequest(testRequest);

      // Verify response structure
      expect(response).toBeDefined();
      expect(response.response).toBeDefined();
      expect(response.agentId).toBeDefined();

      // Verify goal tracking was triggered
      expect(response.metadata?.goalTracking).toBeDefined();
      expect(response.metadata?.goalTracking?.activeGoals).toBeGreaterThan(0);

      // Verify steering guidance was generated
      expect(response.metadata?.steeringGuidance).toBeDefined();

      console.log('✅ AgentOrchestrator + GoalTrackerAgent integration validated');
    });

    it('should handle goal completion and trigger educational content', async () => {
      // Simulate conversation progression
      const requests = [
        {
          sessionId: testSessionId,
          userMessage: 'I have chest pain',
          userId: testUserId,
          urgencyLevel: 'medium' as const
        },
        {
          sessionId: testSessionId,
          userMessage: 'The pain is sharp and occurs when I breathe deeply',
          userId: testUserId,
          urgencyLevel: 'medium' as const
        },
        {
          sessionId: testSessionId,
          userMessage: 'It started yesterday after exercising',
          userId: testUserId,
          urgencyLevel: 'medium' as const
        }
      ];

      const responses = [];
      for (const request of requests) {
        const response = await orchestrator.processRequest(request);
        responses.push(response);
        
        // Small delay to simulate real conversation
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Verify goal progression
      const finalResponse = responses[responses.length - 1];
      expect(finalResponse.metadata?.goalTracking?.progressPercentage).toBeGreaterThan(50);

      console.log('✅ Goal completion and educational content integration validated');
    });
  });

  describe('Circuit Breaker + Error Sanitization Integration', () => {
    it('should sanitize errors when circuit breaker opens', async () => {
      // Force circuit breaker to open state
      const testCircuitBreaker = circuitBreakerService.getCircuitBreaker('test-service');
      testCircuitBreaker.forceState('open');

      // Attempt operation that should trigger circuit breaker
      try {
        await testCircuitBreaker.execute(async () => {
          throw new Error('Database connection failed: postgresql://user:password@localhost:5432/medical_db');
        });
      } catch (error) {
        // Verify error was sanitized
        expect(error.message).not.toContain('password');
        expect(error.message).not.toContain('postgresql://');
        expect(error.message).toContain('Circuit breaker is open');
      }

      console.log('✅ Circuit breaker + Error sanitization integration validated');
    });

    it('should handle emergency bypass with proper error handling', async () => {
      const testRequest = {
        sessionId: testSessionId,
        userMessage: 'EMERGENCY: Patient is unconscious and not breathing',
        userId: testUserId,
        urgencyLevel: 'critical' as const
      };

      // Process emergency request
      const response = await orchestrator.processRequest(testRequest);

      // Verify emergency handling
      expect(response).toBeDefined();
      expect(response.metadata?.emergencyFlags).toBeDefined();
      expect(response.metadata?.emergencyFlags?.length).toBeGreaterThan(0);

      console.log('✅ Emergency bypass + Error handling integration validated');
    });
  });

  describe('Real-time Communication + Memory Cleanup Integration', () => {
    it('should handle real-time updates with proper memory management', async () => {
      // Set up real-time listener
      let updateReceived = false;
      const updateHandler = (data: any) => {
        if (data.sessionId === testSessionId) {
          updateReceived = true;
        }
      };

      realTimeAgentCommunication.on('goal-progress-update', updateHandler);

      try {
        // Trigger goal tracking that should send real-time updates
        const testRequest = {
          sessionId: testSessionId,
          userMessage: 'I need help with my medication schedule',
          userId: testUserId,
          urgencyLevel: 'low' as const
        };

        await orchestrator.processRequest(testRequest);

        // Wait for real-time update
        await new Promise(resolve => setTimeout(resolve, 500));

        // Verify memory cleanup doesn't interfere with real-time updates
        const memoryMetrics = memoryCleanupManager.getMemoryMetrics();
        expect(memoryMetrics).toBeDefined();
        expect(memoryMetrics.length).toBeGreaterThan(0);

        console.log('✅ Real-time communication + Memory cleanup integration validated');
      } finally {
        realTimeAgentCommunication.off('goal-progress-update', updateHandler);
      }
    });
  });

  describe('Steering Guidance + Context Assembly Integration', () => {
    it('should apply steering guidance to context assembly', async () => {
      // Create initial conversation to establish context
      const initialRequest = {
        sessionId: testSessionId,
        userMessage: 'I have multiple health concerns I want to discuss',
        userId: testUserId,
        urgencyLevel: 'medium' as const
      };

      const initialResponse = await orchestrator.processRequest(initialRequest);

      // Follow-up request that should trigger steering
      const followUpRequest = {
        sessionId: testSessionId,
        userMessage: 'Actually, let me focus on my heart palpitations first',
        userId: testUserId,
        urgencyLevel: 'medium' as const
      };

      const followUpResponse = await orchestrator.processRequest(followUpRequest);

      // Verify steering guidance was applied
      expect(followUpResponse.metadata?.steeringGuidance).toBeDefined();
      expect(followUpResponse.metadata?.contextEnhancement).toBeDefined();

      console.log('✅ Steering guidance + Context assembly integration validated');
    });
  });

  describe('Emergency Protocols Cross-Module Validation', () => {
    it('should maintain <2 second response time across all modules', async () => {
      const emergencyRequest = {
        sessionId: testSessionId,
        userMessage: 'EMERGENCY: Severe allergic reaction, difficulty breathing, swelling',
        userId: testUserId,
        urgencyLevel: 'critical' as const
      };

      const startTime = Date.now();
      const response = await orchestrator.processRequest(emergencyRequest);
      const responseTime = Date.now() - startTime;

      // Verify response time requirement
      expect(responseTime).toBeLessThan(2000); // <2 seconds

      // Verify emergency flags
      expect(response.metadata?.emergencyFlags).toBeDefined();
      expect(response.metadata?.emergencyFlags?.length).toBeGreaterThan(0);

      // Verify all modules handled emergency context
      expect(response.metadata?.circuitBreakerBypass).toBe(true);
      expect(response.metadata?.errorSanitization?.emergencyBypass).toBe(true);

      console.log(`✅ Emergency protocols validated - Response time: ${responseTime}ms`);
    });
  });

  describe('HIPAA Compliance Cross-Module Validation', () => {
    it('should maintain HIPAA compliance across all data flows', async () => {
      const medicalRequest = {
        sessionId: testSessionId,
        userMessage: 'My patient ID is MRN123456 and I was diagnosed with diabetes. My SSN is ***********.',
        userId: testUserId,
        urgencyLevel: 'medium' as const
      };

      // Process request with sensitive data
      const response = await orchestrator.processRequest(medicalRequest);

      // Verify sensitive data was handled properly
      expect(response.response).not.toContain('MRN123456');
      expect(response.response).not.toContain('***********');

      // Check error sanitization logs don't contain sensitive data
      const errorStats = errorSanitizationService.getErrorStatistics();
      expect(errorStats).toBeDefined();

      // Verify audit logging was triggered
      expect(response.metadata?.auditLogged).toBe(true);

      console.log('✅ HIPAA compliance cross-module validation completed');
    });
  });

  describe('Performance and Scalability Integration', () => {
    it('should handle concurrent requests without degradation', async () => {
      const concurrentRequests = Array.from({ length: 5 }, (_, i) => ({
        sessionId: `${testSessionId}-concurrent-${i}`,
        userMessage: `Concurrent test message ${i + 1}`,
        userId: `${testUserId}-${i}`,
        urgencyLevel: 'medium' as const
      }));

      const startTime = Date.now();
      const responses = await Promise.all(
        concurrentRequests.map(request => orchestrator.processRequest(request))
      );
      const totalTime = Date.now() - startTime;

      // Verify all requests completed successfully
      expect(responses).toHaveLength(5);
      responses.forEach(response => {
        expect(response).toBeDefined();
        expect(response.response).toBeDefined();
      });

      // Verify reasonable performance under load
      expect(totalTime).toBeLessThan(10000); // <10 seconds for 5 concurrent requests

      console.log(`✅ Concurrent request handling validated - Total time: ${totalTime}ms`);
    });
  });

  describe('Data Consistency Cross-Module Validation', () => {
    it('should maintain data consistency across all storage systems', async () => {
      const testRequest = {
        sessionId: testSessionId,
        userMessage: 'I want to track my blood pressure readings',
        userId: testUserId,
        urgencyLevel: 'low' as const
      };

      // Process request
      const response = await orchestrator.processRequest(testRequest);

      // Verify data consistency
      expect(response.metadata?.dataConsistency).toBeDefined();
      expect(response.metadata?.dataConsistency?.goalTracking).toBe(true);
      expect(response.metadata?.dataConsistency?.memoryStorage).toBe(true);

      // Verify circuit breaker metrics are consistent
      const circuitBreakerMetrics = circuitBreakerService.getAllMetrics();
      expect(circuitBreakerMetrics).toBeDefined();
      expect(circuitBreakerMetrics.length).toBeGreaterThan(0);

      console.log('✅ Data consistency cross-module validation completed');
    });
  });
});

// Helper functions for integration testing
export const IntegrationTestHelpers = {
  /**
   * Create test session with predefined data
   */
  async createTestSession(sessionId: string, userId: string) {
    // Implementation for creating test session
    console.log(`🧪 Creating test session: ${sessionId}`);
  },

  /**
   * Validate cross-module data flow
   */
  async validateDataFlow(sessionId: string, expectedModules: string[]) {
    // Implementation for validating data flow
    console.log(`🔍 Validating data flow for session: ${sessionId}`);
  },

  /**
   * Simulate network failures for testing
   */
  async simulateNetworkFailure(duration: number) {
    // Implementation for simulating network failures
    console.log(`🌐 Simulating network failure for ${duration}ms`);
  },

  /**
   * Validate HIPAA compliance across modules
   */
  async validateHIPAACompliance(sessionId: string) {
    // Implementation for HIPAA compliance validation
    console.log(`🛡️ Validating HIPAA compliance for session: ${sessionId}`);
  }
};
