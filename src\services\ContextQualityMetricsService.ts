/**
 * Context Quality Metrics Service
 * 
 * Comprehensive context quality assessment and analytics for medical AI consultations
 * including completeness scoring, utilization analytics, and quality improvement recommendations.
 */

export interface ContextQualityReport {
  overallScore: number; // 0-100
  completenessScore: number; // 0-100
  relevanceScore: number; // 0-100
  freshnessScore: number; // 0-100
  accuracyScore: number; // 0-100
  clinicalValueScore: number; // 0-100
  sectionScores: SectionQualityScore[];
  recommendations: QualityRecommendation[];
  trends: QualityTrend[];
  benchmarks: QualityBenchmark;
}

export interface SectionQualityScore {
  section: string;
  score: number; // 0-100
  weight: number; // Importance weight
  issues: QualityIssue[];
  dataPoints: number;
  missingCriticalData: string[];
  lastUpdated: string;
}

export interface QualityIssue {
  type: 'missing_data' | 'outdated_data' | 'inconsistent_data' | 'low_confidence';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  impact: string;
  recommendation: string;
  autoFixable: boolean;
}

export interface QualityRecommendation {
  priority: 'immediate' | 'high' | 'medium' | 'low';
  category: 'data_collection' | 'data_quality' | 'system_optimization' | 'user_experience';
  title: string;
  description: string;
  expectedImpact: number; // 0-100
  implementationEffort: 'low' | 'medium' | 'high';
  estimatedTimeToImplement: string;
}

export interface QualityTrend {
  metric: string;
  timeframe: '24h' | '7d' | '30d' | '90d';
  trend: 'improving' | 'stable' | 'declining';
  changePercentage: number;
  dataPoints: number[];
  timestamps: string[];
}

export interface QualityBenchmark {
  industryAverage: number;
  topPerformers: number;
  minimumAcceptable: number;
  currentPerformance: number;
  percentileRank: number;
}

export interface UtilizationAnalytics {
  contextUsagePatterns: UsagePattern[];
  tokenEfficiency: TokenEfficiencyMetrics;
  cachePerformance: CachePerformanceMetrics;
  userEngagement: UserEngagementMetrics;
  systemPerformance: SystemPerformanceMetrics;
}

export interface UsagePattern {
  contextType: string;
  frequency: number;
  averageTokens: number;
  peakUsageTimes: string[];
  userSegments: UserSegment[];
  correlations: ContextCorrelation[];
}

export interface TokenEfficiencyMetrics {
  averageTokensPerSession: number;
  tokenUtilizationRate: number; // Percentage of allocated tokens used
  wastedTokens: number;
  optimizationOpportunities: OptimizationOpportunity[];
}

export interface CachePerformanceMetrics {
  hitRate: number;
  missRate: number;
  averageResponseTime: number;
  memoryEfficiency: number;
  evictionRate: number;
}

export interface UserEngagementMetrics {
  sessionDuration: number;
  contextInteractionRate: number;
  userSatisfactionScore: number;
  completionRate: number;
}

export interface SystemPerformanceMetrics {
  averageAssemblyTime: number;
  errorRate: number;
  throughput: number;
  resourceUtilization: number;
}

export interface UserSegment {
  segment: string;
  usage: number;
  preferences: string[];
}

export interface ContextCorrelation {
  contextA: string;
  contextB: string;
  correlation: number;
  significance: number;
}

export interface OptimizationOpportunity {
  area: string;
  potentialSavings: number;
  difficulty: 'easy' | 'medium' | 'hard';
  description: string;
}

class ContextQualityMetricsService {
  private qualityHistory: Map<string, ContextQualityReport[]> = new Map();
  private utilizationData: Map<string, UtilizationAnalytics> = new Map();

  // Quality scoring weights
  private readonly QUALITY_WEIGHTS = {
    completeness: 0.25,
    relevance: 0.25,
    freshness: 0.15,
    accuracy: 0.20,
    clinicalValue: 0.15
  };

  // Section importance weights
  private readonly SECTION_WEIGHTS: Record<string, number> = {
    current_symptoms: 0.20,
    medical_history: 0.18,
    current_medications: 0.15,
    allergies: 0.12,
    emergency_alerts: 0.10,
    vital_signs: 0.08,
    regional_context: 0.07,
    cultural_context: 0.05,
    lifestyle_factors: 0.03,
    family_history: 0.02
  };

  /**
   * Assess context quality comprehensively
   */
  assessContextQuality(
    contextSections: any[],
    patientContext: any,
    sessionMetadata: any
  ): ContextQualityReport {
    // Calculate individual quality scores
    const completenessScore = this.calculateCompletenessScore(contextSections, patientContext);
    const relevanceScore = this.calculateRelevanceScore(contextSections, sessionMetadata);
    const freshnessScore = this.calculateFreshnessScore(contextSections, patientContext);
    const accuracyScore = this.calculateAccuracyScore(contextSections, patientContext);
    const clinicalValueScore = this.calculateClinicalValueScore(contextSections, sessionMetadata);

    // Calculate overall score
    const overallScore = Math.round(
      completenessScore * this.QUALITY_WEIGHTS.completeness +
      relevanceScore * this.QUALITY_WEIGHTS.relevance +
      freshnessScore * this.QUALITY_WEIGHTS.freshness +
      accuracyScore * this.QUALITY_WEIGHTS.accuracy +
      clinicalValueScore * this.QUALITY_WEIGHTS.clinicalValue
    );

    // Assess individual sections
    const sectionScores = this.assessSectionQuality(contextSections, patientContext);

    // Generate recommendations
    const recommendations = this.generateQualityRecommendations(
      { completenessScore, relevanceScore, freshnessScore, accuracyScore, clinicalValueScore },
      sectionScores
    );

    // Get quality trends
    const trends = this.getQualityTrends(sessionMetadata.userId);

    // Get benchmarks
    const benchmarks = this.getQualityBenchmarks(overallScore);

    const report: ContextQualityReport = {
      overallScore,
      completenessScore,
      relevanceScore,
      freshnessScore,
      accuracyScore,
      clinicalValueScore,
      sectionScores,
      recommendations,
      trends,
      benchmarks
    };

    // Store for trend analysis
    this.storeQualityReport(sessionMetadata.userId, report);

    return report;
  }

  /**
   * Analyze context utilization patterns
   */
  analyzeUtilization(
    sessionData: any[],
    timeframe: '24h' | '7d' | '30d' = '7d'
  ): UtilizationAnalytics {
    const contextUsagePatterns = this.analyzeUsagePatterns(sessionData);
    const tokenEfficiency = this.analyzeTokenEfficiency(sessionData);
    const cachePerformance = this.analyzeCachePerformance(sessionData);
    const userEngagement = this.analyzeUserEngagement(sessionData);
    const systemPerformance = this.analyzeSystemPerformance(sessionData);

    return {
      contextUsagePatterns,
      tokenEfficiency,
      cachePerformance,
      userEngagement,
      systemPerformance
    };
  }

  /**
   * Calculate completeness score
   */
  private calculateCompletenessScore(contextSections: any[], patientContext: any): number {
    const requiredSections = ['current_symptoms', 'medical_history', 'current_medications', 'allergies'];
    const presentSections = contextSections.filter(s => s.included).map(s => s.name);
    
    let score = 0;
    let totalWeight = 0;

    requiredSections.forEach(section => {
      const weight = this.SECTION_WEIGHTS[section] || 0.1;
      totalWeight += weight;
      
      if (presentSections.includes(section)) {
        const sectionData = contextSections.find(s => s.name === section);
        const dataCompleteness = this.assessSectionCompleteness(section, sectionData, patientContext);
        score += dataCompleteness * weight;
      }
    });

    return totalWeight > 0 ? Math.round((score / totalWeight) * 100) : 0;
  }

  /**
   * Calculate relevance score
   */
  private calculateRelevanceScore(contextSections: any[], sessionMetadata: any): number {
    let relevanceScore = 0;
    let totalWeight = 0;

    contextSections.forEach(section => {
      if (!section.included) return;

      const weight = this.SECTION_WEIGHTS[section.name] || 0.05;
      totalWeight += weight;

      // Calculate relevance based on chief complaint and urgency
      const sectionRelevance = this.calculateSectionRelevance(
        section.name,
        sessionMetadata.chiefComplaint,
        sessionMetadata.urgencyLevel
      );

      relevanceScore += sectionRelevance * weight;
    });

    return totalWeight > 0 ? Math.round((relevanceScore / totalWeight) * 100) : 0;
  }

  /**
   * Calculate freshness score
   */
  private calculateFreshnessScore(contextSections: any[], patientContext: any): number {
    let freshnessScore = 0;
    let totalSections = 0;

    contextSections.forEach(section => {
      if (!section.included) return;

      totalSections++;
      const sectionFreshness = this.calculateSectionFreshness(section.name, patientContext);
      freshnessScore += sectionFreshness;
    });

    return totalSections > 0 ? Math.round(freshnessScore / totalSections) : 0;
  }

  /**
   * Calculate accuracy score
   */
  private calculateAccuracyScore(contextSections: any[], patientContext: any): number {
    // Base accuracy score - would be enhanced with validation against external sources
    let accuracyScore = 85; // Base score

    // Check for data consistency
    const consistencyIssues = this.detectConsistencyIssues(contextSections, patientContext);
    accuracyScore -= consistencyIssues.length * 5;

    // Check for data validation
    const validationIssues = this.detectValidationIssues(contextSections);
    accuracyScore -= validationIssues.length * 3;

    return Math.max(0, Math.min(100, accuracyScore));
  }

  /**
   * Calculate clinical value score
   */
  private calculateClinicalValueScore(contextSections: any[], sessionMetadata: any): number {
    let clinicalValue = 0;
    let totalWeight = 0;

    // High clinical value sections for different urgency levels
    const highValueSections = {
      immediate: ['emergency_alerts', 'current_symptoms', 'vital_signs', 'allergies'],
      urgent: ['current_symptoms', 'medical_history', 'current_medications'],
      routine: ['medical_history', 'current_symptoms', 'lifestyle_factors']
    };

    const relevantSections = highValueSections[sessionMetadata.urgencyLevel] || highValueSections.routine;

    contextSections.forEach(section => {
      if (!section.included) return;

      const weight = this.SECTION_WEIGHTS[section.name] || 0.05;
      totalWeight += weight;

      let sectionValue = 50; // Base value
      
      // Increase value for high-value sections
      if (relevantSections.includes(section.name)) {
        sectionValue += 30;
      }

      // Increase value based on data richness
      if (section.tokenCount > 100) {
        sectionValue += 20;
      }

      clinicalValue += sectionValue * weight;
    });

    return totalWeight > 0 ? Math.round((clinicalValue / totalWeight)) : 0;
  }

  /**
   * Assess individual section quality
   */
  private assessSectionQuality(contextSections: any[], patientContext: any): SectionQualityScore[] {
    return contextSections.map(section => {
      const issues = this.identifySectionIssues(section, patientContext);
      const missingCriticalData = this.identifyMissingCriticalData(section.name, patientContext);
      
      return {
        section: section.name,
        score: section.qualityScore || 0,
        weight: this.SECTION_WEIGHTS[section.name] || 0.05,
        issues,
        dataPoints: this.countDataPoints(section),
        missingCriticalData,
        lastUpdated: new Date().toISOString()
      };
    });
  }

  /**
   * Generate quality improvement recommendations
   */
  private generateQualityRecommendations(
    scores: any,
    sectionScores: SectionQualityScore[]
  ): QualityRecommendation[] {
    const recommendations: QualityRecommendation[] = [];

    // Completeness recommendations
    if (scores.completenessScore < 70) {
      recommendations.push({
        priority: 'high',
        category: 'data_collection',
        title: 'Improve Data Completeness',
        description: 'Critical patient information is missing. Focus on collecting medical history, current medications, and allergies.',
        expectedImpact: 25,
        implementationEffort: 'medium',
        estimatedTimeToImplement: '2-3 weeks'
      });
    }

    // Freshness recommendations
    if (scores.freshnessScore < 60) {
      recommendations.push({
        priority: 'medium',
        category: 'data_quality',
        title: 'Update Outdated Information',
        description: 'Some patient data appears outdated. Implement regular data refresh mechanisms.',
        expectedImpact: 15,
        implementationEffort: 'low',
        estimatedTimeToImplement: '1 week'
      });
    }

    // Section-specific recommendations
    sectionScores.forEach(section => {
      if (section.score < 50 && section.weight > 0.1) {
        recommendations.push({
          priority: 'high',
          category: 'data_collection',
          title: `Improve ${section.section} Data Quality`,
          description: `The ${section.section} section has quality issues that impact clinical decision-making.`,
          expectedImpact: Math.round(section.weight * 100),
          implementationEffort: 'medium',
          estimatedTimeToImplement: '1-2 weeks'
        });
      }
    });

    return recommendations.sort((a, b) => {
      const priorityOrder = { immediate: 0, high: 1, medium: 2, low: 3 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });
  }

  // Helper methods for quality assessment
  private assessSectionCompleteness(section: string, sectionData: any, patientContext: any): number {
    // Implementation would assess how complete each section is
    // This is a simplified version
    if (!sectionData || !sectionData.content) return 0;
    
    const contentLength = sectionData.content.length;
    if (contentLength > 200) return 1.0;
    if (contentLength > 100) return 0.7;
    if (contentLength > 50) return 0.4;
    return 0.2;
  }

  private calculateSectionRelevance(sectionName: string, chiefComplaint: string, urgencyLevel: string): number {
    // Calculate how relevant a section is to the current consultation
    const complaint = chiefComplaint.toLowerCase();
    
    if (sectionName === 'emergency_alerts' && urgencyLevel === 'immediate') return 100;
    if (sectionName === 'current_symptoms') return 90;
    if (sectionName === 'allergies' && complaint.includes('allergic')) return 95;
    if (sectionName === 'current_medications') return 80;
    if (sectionName === 'medical_history') return 75;
    
    return 50; // Default relevance
  }

  private calculateSectionFreshness(sectionName: string, patientContext: any): number {
    // Calculate how fresh/recent the data is
    // This would typically check timestamps of data updates
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    const oneWeek = 7 * oneDay;
    const oneMonth = 30 * oneDay;
    
    // Simulate freshness based on section type
    if (sectionName === 'current_symptoms') return 95; // Usually fresh
    if (sectionName === 'vital_signs') return 80; // Moderately fresh
    if (sectionName === 'medical_history') return 60; // Less time-sensitive
    
    return 70; // Default freshness
  }

  private detectConsistencyIssues(contextSections: any[], patientContext: any): any[] {
    // Detect inconsistencies in the data
    const issues: any[] = [];
    
    // Example: Check if age is consistent with date of birth
    // Example: Check if medications are consistent with conditions
    
    return issues;
  }

  private detectValidationIssues(contextSections: any[]): any[] {
    // Detect validation issues in the data
    const issues: any[] = [];
    
    // Example: Check for invalid medication names
    // Example: Check for impossible vital signs
    
    return issues;
  }

  private identifySectionIssues(section: any, patientContext: any): QualityIssue[] {
    const issues: QualityIssue[] = [];
    
    if (!section.included) {
      issues.push({
        type: 'missing_data',
        severity: 'medium',
        description: 'Section not included in context',
        impact: 'Reduced clinical decision-making capability',
        recommendation: 'Include this section in context assembly',
        autoFixable: true
      });
    }
    
    if (section.truncated) {
      issues.push({
        type: 'missing_data',
        severity: 'low',
        description: 'Section content was truncated',
        impact: 'Some information may be missing',
        recommendation: 'Increase token allocation for this section',
        autoFixable: true
      });
    }
    
    return issues;
  }

  private identifyMissingCriticalData(sectionName: string, patientContext: any): string[] {
    const missing: string[] = [];
    
    // Check for missing critical data based on section type
    if (sectionName === 'allergies' && !patientContext.medicalHistory?.allergies?.length) {
      missing.push('allergy information');
    }
    
    if (sectionName === 'current_medications' && !patientContext.medicalHistory?.medications?.length) {
      missing.push('current medications');
    }
    
    return missing;
  }

  private countDataPoints(section: any): number {
    // Count the number of data points in a section
    if (!section.content) return 0;
    
    // Simple heuristic: count lines with meaningful content
    const lines = section.content.split('\n').filter(line => line.trim().length > 10);
    return lines.length;
  }

  private getQualityTrends(userId: string): QualityTrend[] {
    // Get quality trends for the user
    const history = this.qualityHistory.get(userId) || [];
    
    if (history.length < 2) {
      return [];
    }
    
    // Calculate trends for different metrics
    const trends: QualityTrend[] = [];
    
    const overallScores = history.map(h => h.overallScore);
    const completenessScores = history.map(h => h.completenessScore);
    
    trends.push({
      metric: 'Overall Quality',
      timeframe: '7d',
      trend: this.calculateTrend(overallScores),
      changePercentage: this.calculateChangePercentage(overallScores),
      dataPoints: overallScores,
      timestamps: history.map(h => new Date().toISOString()) // Simplified
    });
    
    return trends;
  }

  private getQualityBenchmarks(currentScore: number): QualityBenchmark {
    return {
      industryAverage: 75,
      topPerformers: 90,
      minimumAcceptable: 60,
      currentPerformance: currentScore,
      percentileRank: this.calculatePercentileRank(currentScore)
    };
  }

  private storeQualityReport(userId: string, report: ContextQualityReport): void {
    const history = this.qualityHistory.get(userId) || [];
    history.push(report);
    
    // Keep only last 30 reports
    if (history.length > 30) {
      history.splice(0, history.length - 30);
    }
    
    this.qualityHistory.set(userId, history);
  }

  // Utilization analysis methods
  private analyzeUsagePatterns(sessionData: any[]): UsagePattern[] {
    // Analyze how different context types are used
    return [
      {
        contextType: 'medical_history',
        frequency: 0.85,
        averageTokens: 250,
        peakUsageTimes: ['09:00', '14:00', '16:00'],
        userSegments: [
          { segment: 'chronic_patients', usage: 0.9, preferences: ['detailed_history'] }
        ],
        correlations: [
          { contextA: 'medical_history', contextB: 'current_medications', correlation: 0.8, significance: 0.95 }
        ]
      }
    ];
  }

  private analyzeTokenEfficiency(sessionData: any[]): TokenEfficiencyMetrics {
    return {
      averageTokensPerSession: 1200,
      tokenUtilizationRate: 75,
      wastedTokens: 300,
      optimizationOpportunities: [
        {
          area: 'Content compression',
          potentialSavings: 15,
          difficulty: 'easy',
          description: 'Remove redundant phrases and optimize formatting'
        }
      ]
    };
  }

  private analyzeCachePerformance(sessionData: any[]): CachePerformanceMetrics {
    return {
      hitRate: 65,
      missRate: 35,
      averageResponseTime: 150,
      memoryEfficiency: 80,
      evictionRate: 5
    };
  }

  private analyzeUserEngagement(sessionData: any[]): UserEngagementMetrics {
    return {
      sessionDuration: 12.5,
      contextInteractionRate: 0.7,
      userSatisfactionScore: 4.2,
      completionRate: 0.85
    };
  }

  private analyzeSystemPerformance(sessionData: any[]): SystemPerformanceMetrics {
    return {
      averageAssemblyTime: 200,
      errorRate: 0.02,
      throughput: 150,
      resourceUtilization: 65
    };
  }

  // Helper methods for trend analysis
  private calculateTrend(values: number[]): 'improving' | 'stable' | 'declining' {
    if (values.length < 2) return 'stable';
    
    const recent = values.slice(-3);
    const earlier = values.slice(-6, -3);
    
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const earlierAvg = earlier.reduce((a, b) => a + b, 0) / earlier.length;
    
    if (recentAvg > earlierAvg + 2) return 'improving';
    if (recentAvg < earlierAvg - 2) return 'declining';
    return 'stable';
  }

  private calculateChangePercentage(values: number[]): number {
    if (values.length < 2) return 0;
    
    const latest = values[values.length - 1];
    const previous = values[values.length - 2];
    
    return Math.round(((latest - previous) / previous) * 100);
  }

  private calculatePercentileRank(score: number): number {
    // Simplified percentile calculation
    if (score >= 90) return 95;
    if (score >= 80) return 80;
    if (score >= 70) return 65;
    if (score >= 60) return 45;
    return 25;
  }
}

export const contextQualityMetricsService = new ContextQualityMetricsService();
export default contextQualityMetricsService;
