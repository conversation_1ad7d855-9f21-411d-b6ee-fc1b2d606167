/**
 * STANDARDIZED ERROR HANDLING UTILITY
 * 
 * Provides consistent error handling patterns across all VoiceHealth AI services.
 * Ensures proper error logging, sanitization, and response formatting while
 * maintaining HIPAA compliance and patient safety.
 * 
 * Features:
 * - Standardized error response format
 * - Error sanitization for production
 * - HIPAA-compliant error logging
 * - Emergency protocol error handling
 * - Cultural sensitivity in error messages
 * - Performance impact tracking
 */

import { performanceValidationService } from '../services/PerformanceValidationService';

// =====================================================
// ERROR TYPE DEFINITIONS
// =====================================================

export enum ErrorCode {
  // Authentication Errors
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  MFA_REQUIRED = 'MFA_REQUIRED',

  // Validation Errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT = 'INVALID_FORMAT',
  CULTURAL_VALIDATION_FAILED = 'CULTURAL_VALIDATION_FAILED',

  // Service Errors
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  ENCRYPTION_ERROR = 'ENCRYPTION_ERROR',
  AI_SERVICE_ERROR = 'AI_SERVICE_ERROR',

  // Emergency Protocol Errors
  EMERGENCY_PROTOCOL_TIMEOUT = 'EMERGENCY_PROTOCOL_TIMEOUT',
  EMERGENCY_RESPONSE_FAILED = 'EMERGENCY_RESPONSE_FAILED',
  EMERGENCY_BYPASS_DENIED = 'EMERGENCY_BYPASS_DENIED',

  // Compliance Errors
  HIPAA_VIOLATION = 'HIPAA_VIOLATION',
  COMPLIANCE_VIOLATION = 'COMPLIANCE_VIOLATION',
  DATA_PROTECTION_VIOLATION = 'DATA_PROTECTION_VIOLATION',
  AUDIT_LOG_FAILURE = 'AUDIT_LOG_FAILURE',

  // Resource Errors
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',

  // System Errors
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface StandardError {
  code: ErrorCode;
  message: string;
  details?: any | undefined;
  timestamp: Date;
  requestId?: string | undefined;
  userId?: string | undefined;
  serviceName: string;
  methodName: string;
  severity: ErrorSeverity;
  culturalContext?: any | undefined;
  sanitizedForProduction: boolean;
  hipaaCompliant: boolean;
}

export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any | undefined;
    timestamp: string;
    requestId?: string | undefined;
  };
}

// =====================================================
// STANDARDIZED ERROR HANDLER CLASS
// =====================================================

class StandardErrorHandler {
  private static instance: StandardErrorHandler;
  private errorCounts: Map<string, number> = new Map();
  private lastErrorTime: Map<string, Date> = new Map();

  private constructor() {}

  static getInstance(): StandardErrorHandler {
    if (!StandardErrorHandler.instance) {
      StandardErrorHandler.instance = new StandardErrorHandler();
    }
    return StandardErrorHandler.instance;
  }

  /**
   * Handle and format error with standardized response
   */
  handleError(
    error: any,
    context: {
      serviceName: string;
      methodName: string;
      userId?: string;
      requestId?: string;
      culturalContext?: any;
    }
  ): ErrorResponse {
    const standardError = this.createStandardError(error, context);
    
    // Log error for monitoring
    this.logError(standardError);
    
    // Record performance impact
    this.recordErrorMetric(standardError);
    
    // Return sanitized response
    return this.createErrorResponse(standardError);
  }

  /**
   * Handle emergency protocol errors with special handling
   */
  handleEmergencyError(
    error: any,
    context: {
      serviceName: string;
      methodName: string;
      emergencyType: string;
      responseTime: number;
      userId?: string;
    }
  ): ErrorResponse {
    const standardError = this.createStandardError(error, {
      serviceName: context.serviceName,
      methodName: context.methodName,
      ...(context.userId && { userId: context.userId })
    });

    // Override severity for emergency errors
    standardError.severity = ErrorSeverity.CRITICAL;
    
    // Add emergency-specific details
    standardError.details = {
      ...standardError.details,
      emergencyType: context.emergencyType,
      responseTime: context.responseTime,
      emergencyProtocolFailed: true
    };

    // Log emergency error with high priority
    this.logEmergencyError(standardError, context);
    
    // Record emergency performance impact
    this.recordEmergencyErrorMetric(standardError, context);
    
    return this.createErrorResponse(standardError);
  }

  /**
   * Handle cultural validation errors with cultural sensitivity
   */
  handleCulturalError(
    error: any,
    context: {
      serviceName: string;
      methodName: string;
      culturalContext: any;
      content?: string;
      userId?: string;
    }
  ): ErrorResponse {
    const standardError = this.createStandardError(error, context);
    
    // Add cultural-specific error handling
    if (context.culturalContext) {
      standardError.culturalContext = {
        cultureCode: context.culturalContext.cultureCode,
        country: context.culturalContext.country,
        languagePreference: context.culturalContext.languagePreference
      };
      
      // Localize error message if possible
      standardError.message = this.localizeCulturalErrorMessage(
        standardError.message,
        context.culturalContext
      );
    }
    
    this.logError(standardError);
    this.recordErrorMetric(standardError);
    
    return this.createErrorResponse(standardError);
  }

  /**
   * Create standardized error object
   */
  private createStandardError(
    error: any,
    context: {
      serviceName: string;
      methodName: string;
      userId?: string;
      requestId?: string;
      culturalContext?: any;
    }
  ): StandardError {
    const errorCode = this.determineErrorCode(error);
    const severity = this.determineSeverity(error, errorCode);
    
    return {
      code: errorCode,
      message: this.sanitizeErrorMessage(error.message || 'An error occurred'),
      details: this.sanitizeErrorDetails(error, context),
      timestamp: new Date(),
      requestId: context.requestId || this.generateRequestId(),
      serviceName: context.serviceName,
      methodName: context.methodName,
      severity,
      culturalContext: context.culturalContext,
      sanitizedForProduction: process.env.NODE_ENV === 'production',
      hipaaCompliant: true,
      ...(context.userId && { userId: context.userId })
    };
  }

  /**
   * Determine appropriate error code from error object
   */
  private determineErrorCode(error: any): ErrorCode {
    if (error.code && Object.values(ErrorCode).includes(error.code)) {
      return error.code;
    }
    
    const message = error.message?.toLowerCase() || '';
    
    // Authentication errors
    if (message.includes('authentication') || message.includes('unauthorized')) {
      return ErrorCode.AUTHENTICATION_REQUIRED;
    }
    if (message.includes('permission') || message.includes('forbidden')) {
      return ErrorCode.INSUFFICIENT_PERMISSIONS;
    }
    if (message.includes('session') || message.includes('expired')) {
      return ErrorCode.SESSION_EXPIRED;
    }
    
    // Validation errors
    if (message.includes('validation') || message.includes('invalid')) {
      return ErrorCode.VALIDATION_ERROR;
    }
    if (message.includes('required') || message.includes('missing')) {
      return ErrorCode.MISSING_REQUIRED_FIELD;
    }
    
    // Service errors
    if (message.includes('database') || message.includes('db')) {
      return ErrorCode.DATABASE_ERROR;
    }
    if (message.includes('network') || message.includes('connection')) {
      return ErrorCode.NETWORK_ERROR;
    }
    if (message.includes('timeout')) {
      return ErrorCode.TIMEOUT_ERROR;
    }
    if (message.includes('encryption') || message.includes('decrypt')) {
      return ErrorCode.ENCRYPTION_ERROR;
    }
    
    // Emergency errors
    if (message.includes('emergency')) {
      return ErrorCode.EMERGENCY_RESPONSE_FAILED;
    }
    
    // Cultural errors
    if (message.includes('cultural') || message.includes('bias')) {
      return ErrorCode.CULTURAL_VALIDATION_FAILED;
    }
    
    // Default to internal error
    return ErrorCode.INTERNAL_ERROR;
  }

  /**
   * Determine error severity
   */
  private determineSeverity(error: any, errorCode: ErrorCode): ErrorSeverity {
    // Critical errors
    if ([
      ErrorCode.EMERGENCY_PROTOCOL_TIMEOUT,
      ErrorCode.EMERGENCY_RESPONSE_FAILED,
      ErrorCode.HIPAA_VIOLATION,
      ErrorCode.DATA_PROTECTION_VIOLATION
    ].includes(errorCode)) {
      return ErrorSeverity.CRITICAL;
    }
    
    // High severity errors
    if ([
      ErrorCode.AUTHENTICATION_REQUIRED,
      ErrorCode.DATABASE_ERROR,
      ErrorCode.ENCRYPTION_ERROR,
      ErrorCode.SERVICE_UNAVAILABLE
    ].includes(errorCode)) {
      return ErrorSeverity.HIGH;
    }
    
    // Medium severity errors
    if ([
      ErrorCode.VALIDATION_ERROR,
      ErrorCode.CULTURAL_VALIDATION_FAILED,
      ErrorCode.RATE_LIMIT_EXCEEDED
    ].includes(errorCode)) {
      return ErrorSeverity.MEDIUM;
    }
    
    // Default to low severity
    return ErrorSeverity.LOW;
  }

  /**
   * Sanitize error message for production
   */
  private sanitizeErrorMessage(message: string): string {
    if (process.env.NODE_ENV !== 'production') {
      return message;
    }
    
    // Remove sensitive information patterns
    const sensitivePatterns = [
      /password/gi,
      /token/gi,
      /key/gi,
      /secret/gi,
      /api[_-]?key/gi,
      /auth[_-]?token/gi,
      /bearer/gi,
      /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, // Credit card patterns
      /\b\d{3}-\d{2}-\d{4}\b/g, // SSN patterns
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g // Email patterns
    ];
    
    let sanitized = message;
    sensitivePatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });
    
    return sanitized;
  }

  /**
   * Sanitize error details for production
   */
  private sanitizeErrorDetails(error: any, context: any): any {
    if (process.env.NODE_ENV !== 'production') {
      return {
        originalError: error.name,
        stack: error.stack,
        context: context
      };
    }
    
    // Return minimal details for production
    return {
      errorType: error.name || 'Error',
      service: context.serviceName,
      method: context.methodName
    };
  }

  /**
   * Create standardized error response
   */
  private createErrorResponse(standardError: StandardError): ErrorResponse {
    return {
      success: false,
      error: {
        code: standardError.code,
        message: standardError.message,
        details: standardError.details,
        timestamp: standardError.timestamp.toISOString(),
        ...(standardError.requestId && { requestId: standardError.requestId })
      }
    };
  }

  /**
   * Log error with appropriate level
   */
  private logError(standardError: StandardError): void {
    const logLevel = this.getLogLevel(standardError.severity);
    const logMessage = `${standardError.serviceName}.${standardError.methodName}: ${standardError.message}`;
    
    console[logLevel](`[${standardError.severity.toUpperCase()}] ${logMessage}`, {
      code: standardError.code,
      requestId: standardError.requestId,
      userId: standardError.userId,
      timestamp: standardError.timestamp,
      details: standardError.details
    });
    
    // Track error frequency
    const errorKey = `${standardError.serviceName}.${standardError.methodName}.${standardError.code}`;
    this.errorCounts.set(errorKey, (this.errorCounts.get(errorKey) || 0) + 1);
    this.lastErrorTime.set(errorKey, standardError.timestamp);
  }

  /**
   * Log emergency error with special handling
   */
  private logEmergencyError(
    standardError: StandardError,
    context: { emergencyType: string; responseTime: number }
  ): void {
    console.error(`🚨 EMERGENCY ERROR: ${standardError.serviceName}.${standardError.methodName}`, {
      emergencyType: context.emergencyType,
      responseTime: context.responseTime,
      code: standardError.code,
      message: standardError.message,
      requestId: standardError.requestId,
      timestamp: standardError.timestamp
    });
  }

  /**
   * Record error metric for monitoring
   */
  private recordErrorMetric(standardError: StandardError): void {
    try {
      performanceValidationService.recordMetric({
        operation: `error_${standardError.code.toLowerCase()}`,
        responseTime: 0,
        success: false,
        timestamp: standardError.timestamp,
        target: 0, // Error operations don't have performance targets
        category: 'error',
        metadata: {
          serviceName: standardError.serviceName,
          methodName: standardError.methodName,
          errorCode: standardError.code,
          severity: standardError.severity,
          userId: standardError.userId,
          requestId: standardError.requestId
        }
      });
    } catch (error) {
      console.error('Failed to record error metric:', error);
    }
  }

  /**
   * Record emergency error metric
   */
  private recordEmergencyErrorMetric(
    standardError: StandardError,
    context: { emergencyType: string; responseTime: number }
  ): void {
    try {
      performanceValidationService.recordMetric({
        operation: 'emergency_protocol_error',
        responseTime: context.responseTime,
        success: false,
        timestamp: standardError.timestamp,
        category: 'emergency',
        target: 2000,
        metadata: {
          emergencyType: context.emergencyType,
          errorCode: standardError.code,
          severity: standardError.severity,
          serviceName: standardError.serviceName,
          methodName: standardError.methodName
        }
      });
    } catch (error) {
      console.error('Failed to record emergency error metric:', error);
    }
  }

  /**
   * Localize cultural error message
   */
  private localizeCulturalErrorMessage(message: string, culturalContext: any): string {
    // Basic localization - in production, this would use a proper i18n system
    const cultureCode = culturalContext.cultureCode;
    
    if (cultureCode === 'akan' && culturalContext.languagePreference === 'tw') {
      // Twi localization (simplified example)
      if (message.includes('validation failed')) {
        return 'Amanneɛ ho nhwehwɛmu antumi ankɔ so';
      }
    }
    
    // Default to English
    return message;
  }

  /**
   * Get appropriate log level for severity
   */
  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' | 'log' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.LOW:
        return 'info';
      default:
        return 'log';
    }
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get error statistics for monitoring
   */
  getErrorStatistics(): any {
    return {
      errorCounts: Object.fromEntries(this.errorCounts),
      lastErrorTimes: Object.fromEntries(this.lastErrorTime),
      totalErrors: Array.from(this.errorCounts.values()).reduce((sum, count) => sum + count, 0)
    };
  }
}

// =====================================================
// EXPORTED FUNCTIONS
// =====================================================

export const standardErrorHandler = StandardErrorHandler.getInstance();

/**
 * Convenience function for handling standard errors
 */
export function handleServiceError(
  error: any,
  serviceName: string,
  methodName: string,
  userId?: string,
  requestId?: string
): ErrorResponse {
  return standardErrorHandler.handleError(error, {
    serviceName,
    methodName,
    ...(userId && { userId }),
    ...(requestId && { requestId })
  });
}

/**
 * Convenience function for handling emergency errors
 */
export function handleEmergencyError(
  error: any,
  serviceName: string,
  methodName: string,
  emergencyType: string,
  responseTime: number,
  userId?: string
): ErrorResponse {
  return standardErrorHandler.handleEmergencyError(error, {
    serviceName,
    methodName,
    emergencyType,
    responseTime,
    userId
  });
}

/**
 * Convenience function for handling cultural errors
 */
export function handleCulturalError(
  error: any,
  serviceName: string,
  methodName: string,
  culturalContext: any,
  content?: string,
  userId?: string
): ErrorResponse {
  return standardErrorHandler.handleCulturalError(error, {
    serviceName,
    methodName,
    culturalContext,
    content,
    userId
  });
}

export default standardErrorHandler;
