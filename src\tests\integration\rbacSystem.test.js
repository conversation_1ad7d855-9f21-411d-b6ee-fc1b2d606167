/**
 * COMPREHENSIVE INTEGRATION TESTS FOR RBAC SYSTEM
 * 
 * This test suite provides comprehensive integration testing for the RBAC system
 * with focus on:
 * - Role-based access control for medical data
 * - Permission validation across user roles
 * - Emergency access protocols
 * - Healthcare provider permissions
 * - Patient data privacy protection
 * - Admin oversight capabilities
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Patients can only access their own data
 * - Providers can access assigned patient data
 * - Emergency access must be properly logged
 * - Admin access must be audited
 */

// Vitest globals are available via vitest.config.js globals: true
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import rbacService from '../../services/rbacService';

// Mock dependencies
vi.mock('../../utils/supabaseClient', () => ({
  supabase: global.testHelpers.createMockSupabaseClient()
}));

vi.mock('../../utils/auditLogger', () => ({
  default: global.testHelpers.createMockAuditLogger()
}));

describe('RBAC System Integration Tests', () => {
  let mockSupabase;
  let mockAuditLogger;

  beforeEach(() => {
    vi.clearAllMocks();
    mockSupabase = global.testHelpers.createMockSupabaseClient();
    mockAuditLogger = global.testHelpers.createMockAuditLogger();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Patient Role Access Control', () => {
    it('should allow patients to access their own medical data', async () => {
      // Arrange
      const patient = global.mockUser;
      const patientData = global.mockMedicalData.condition;

      // Act
      const hasPermission = await rbacService.hasPermission(
        patient,
        'read:own_medical_data',
        {
          resourceOwnerId: patient.id,
          patientId: patient.id
        }
      );

      // Assert
      expect(hasPermission).toBe(true);
    });

    it('should prevent patients from accessing other patients data', async () => {
      // Arrange
      const patient = global.mockUser;
      const otherPatientId = 'other-patient-id-12345';

      // Act
      const hasPermission = await rbacService.hasPermission(
        patient,
        'read:own_medical_data',
        {
          resourceOwnerId: otherPatientId,
          patientId: otherPatientId
        }
      );

      // Assert
      expect(hasPermission).toBe(false);
      expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
        'unauthorized_access_attempt',
        'medium',
        expect.objectContaining({
          user_id: patient.id,
          attempted_resource: otherPatientId,
          reason: 'patient_accessing_other_data'
        })
      );
    });

    it('should allow patients to update their own profile information', async () => {
      // Arrange
      const patient = global.mockUser;

      // Act
      const hasPermission = await rbacService.hasPermission(
        patient,
        'write:own_profile',
        {
          resourceOwnerId: patient.id
        }
      );

      // Assert
      expect(hasPermission).toBe(true);
    });

    it('should prevent patients from accessing administrative functions', async () => {
      // Arrange
      const patient = global.mockUser;

      // Act
      const hasPermission = await rbacService.hasPermission(
        patient,
        'manage:users'
      );

      // Assert
      expect(hasPermission).toBe(false);
    });
  });

  describe('Healthcare Provider Role Access Control', () => {
    it('should allow providers to access assigned patient data', async () => {
      // Arrange
      const provider = global.mockProvider;
      const patientId = global.mockUser.id;
      
      // Mock provider-patient assignment
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: {
          provider_id: provider.id,
          patient_id: patientId,
          active: true
        },
        error: null
      });

      // Act
      const hasPermission = await rbacService.hasPermission(
        provider,
        'read:patient_medical_data',
        {
          patientId: patientId
        }
      );

      // Assert
      expect(hasPermission).toBe(true);
    });

    it('should prevent providers from accessing unassigned patient data', async () => {
      // Arrange
      const provider = global.mockProvider;
      const unassignedPatientId = 'unassigned-patient-id-12345';
      
      // Mock no provider-patient assignment
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' } // No rows returned
      });

      // Act
      const hasPermission = await rbacService.hasPermission(
        provider,
        'read:patient_medical_data',
        {
          patientId: unassignedPatientId
        }
      );

      // Assert
      expect(hasPermission).toBe(false);
      expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
        'unauthorized_access_attempt',
        'high',
        expect.objectContaining({
          user_id: provider.id,
          attempted_resource: unassignedPatientId,
          reason: 'provider_accessing_unassigned_patient'
        })
      );
    });

    it('should allow providers to create medical records for assigned patients', async () => {
      // Arrange
      const provider = global.mockProvider;
      const patientId = global.mockUser.id;
      
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: {
          provider_id: provider.id,
          patient_id: patientId,
          active: true
        },
        error: null
      });

      // Act
      const hasPermission = await rbacService.hasPermission(
        provider,
        'write:patient_medical_data',
        {
          patientId: patientId
        }
      );

      // Assert
      expect(hasPermission).toBe(true);
    });

    it('should allow providers to request emergency access', async () => {
      // Arrange
      const provider = global.mockProvider;
      const emergencyPatientId = 'emergency-patient-id-12345';
      const justification = 'Patient brought to ER with chest pain, need immediate access to medical history';

      // Act
      const result = await rbacService.requestEmergencyAccess(
        provider.id,
        emergencyPatientId,
        justification,
        {
          urgency: 'critical',
          location: 'Emergency Department'
        }
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.accessGranted).toBe(true); // Auto-approved for providers
      expect(mockAuditLogger.logEmergencyAccess).toHaveBeenCalledWith(
        provider.id,
        emergencyPatientId,
        justification,
        expect.objectContaining({
          urgency: 'critical',
          auto_approved: true,
          expires_at: expect.any(String)
        })
      );
    });
  });

  describe('Admin Role Access Control', () => {
    it('should allow admins to access all patient data with justification', async () => {
      // Arrange
      const admin = global.mockAdmin;
      const patientId = global.mockUser.id;

      // Act
      const hasPermission = await rbacService.hasPermission(
        admin,
        'read:all_data',
        {
          patientId: patientId,
          justification: 'System audit and compliance review'
        }
      );

      // Assert
      expect(hasPermission).toBe(true);
      expect(mockAuditLogger.logMedicalDataAccess).toHaveBeenCalledWith(
        'admin_access',
        'patient_data',
        patientId,
        expect.objectContaining({
          admin_id: admin.id,
          justification: 'System audit and compliance review',
          requires_review: true
        })
      );
    });

    it('should allow admins to manage user accounts', async () => {
      // Arrange
      const admin = global.mockAdmin;

      // Act
      const hasPermission = await rbacService.hasPermission(
        admin,
        'manage:users'
      );

      // Assert
      expect(hasPermission).toBe(true);
    });

    it('should allow admins to override emergency access decisions', async () => {
      // Arrange
      const admin = global.mockAdmin;
      const emergencyRequestId = 'emergency-request-id-12345';

      // Act
      const result = await rbacService.overrideEmergencyAccess(
        admin.id,
        emergencyRequestId,
        true,
        'Approved due to critical patient condition'
      );

      // Assert
      expect(result.success).toBe(true);
      expect(mockAuditLogger.logEmergencyAccessReview).toHaveBeenCalledWith(
        emergencyRequestId,
        true,
        admin.id,
        expect.objectContaining({
          override: true,
          review_notes: 'Approved due to critical patient condition'
        })
      );
    });

    it('should require justification for admin access to patient data', async () => {
      // Arrange
      const admin = global.mockAdmin;
      const patientId = global.mockUser.id;

      // Act
      const hasPermission = await rbacService.hasPermission(
        admin,
        'read:all_data',
        {
          patientId: patientId
          // Missing justification
        }
      );

      // Assert
      expect(hasPermission).toBe(false);
    });
  });

  describe('Emergency Access Protocols', () => {
    it('should grant immediate emergency access for critical situations', async () => {
      // Arrange
      const provider = global.mockProvider;
      const emergencyPatientId = 'emergency-patient-id-12345';
      const justification = 'Patient in cardiac arrest, need immediate access to allergies and medications';

      // Act
      const result = await rbacService.requestEmergencyAccess(
        provider.id,
        emergencyPatientId,
        justification,
        {
          urgency: 'emergency',
          location: 'Emergency Department',
          witnessId: 'witness-nurse-id-12345'
        }
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.accessGranted).toBe(true);
      expect(result.expiresAt).toBeDefined();
      expect(mockAuditLogger.logEmergencyAccess).toHaveBeenCalledWith(
        provider.id,
        emergencyPatientId,
        justification,
        expect.objectContaining({
          urgency: 'emergency',
          immediate_access: true,
          requires_post_review: true
        })
      );
    });

    it('should automatically expire emergency access after time limit', async () => {
      // Arrange
      const provider = global.mockProvider;
      const emergencyPatientId = 'emergency-patient-id-12345';
      
      // Mock expired emergency access
      const expiredAccess = {
        id: 'emergency-access-id-12345',
        user_id: provider.id,
        patient_id: emergencyPatientId,
        expires_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        active: true
      };

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: expiredAccess,
        error: null
      });

      // Act
      const hasAccess = await rbacService.checkEmergencyAccess(
        provider.id,
        emergencyPatientId
      );

      // Assert
      expect(hasAccess).toBe(false);
      expect(mockSupabase.from().update).toHaveBeenCalledWith({
        active: false,
        expired_at: expect.any(String)
      });
    });

    it('should require post-emergency access review', async () => {
      // Arrange
      const emergencyAccessId = 'emergency-access-id-12345';
      const reviewerId = global.mockAdmin.id;

      // Act
      const result = await rbacService.reviewEmergencyAccess(
        emergencyAccessId,
        reviewerId,
        {
          approved: true,
          notes: 'Emergency access was appropriate given patient condition',
          compliance_verified: true
        }
      );

      // Assert
      expect(result.success).toBe(true);
      expect(mockAuditLogger.logEmergencyAccessReview).toHaveBeenCalledWith(
        emergencyAccessId,
        true,
        reviewerId,
        expect.objectContaining({
          post_emergency_review: true,
          compliance_verified: true
        })
      );
    });
  });

  describe('Permission Context Validation', () => {
    it('should validate resource ownership context', async () => {
      // Arrange
      const patient = global.mockUser;
      const ownedResourceId = patient.id;
      const unownedResourceId = 'other-user-resource-id';

      // Act
      const hasOwnedAccess = await rbacService.hasPermission(
        patient,
        'read:own_medical_data',
        { resourceOwnerId: ownedResourceId }
      );

      const hasUnownedAccess = await rbacService.hasPermission(
        patient,
        'read:own_medical_data',
        { resourceOwnerId: unownedResourceId }
      );

      // Assert
      expect(hasOwnedAccess).toBe(true);
      expect(hasUnownedAccess).toBe(false);
    });

    it('should validate patient assignment context for providers', async () => {
      // Arrange
      const provider = global.mockProvider;
      const assignedPatientId = global.mockUser.id;
      const unassignedPatientId = 'unassigned-patient-id';

      // Mock assigned patient
      mockSupabase.from().select().eq().single
        .mockResolvedValueOnce({
          data: { provider_id: provider.id, patient_id: assignedPatientId, active: true },
          error: null
        })
        .mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116' }
        });

      // Act
      const hasAssignedAccess = await rbacService.hasPermission(
        provider,
        'read:patient_medical_data',
        { patientId: assignedPatientId }
      );

      const hasUnassignedAccess = await rbacService.hasPermission(
        provider,
        'read:patient_medical_data',
        { patientId: unassignedPatientId }
      );

      // Assert
      expect(hasAssignedAccess).toBe(true);
      expect(hasUnassignedAccess).toBe(false);
    });

    it('should validate urgency context for emergency access', async () => {
      // Arrange
      const provider = global.mockProvider;
      const patientId = 'emergency-patient-id';

      // Act - Critical urgency should be auto-approved
      const criticalResult = await rbacService.requestEmergencyAccess(
        provider.id,
        patientId,
        'Critical patient condition',
        { urgency: 'critical' }
      );

      // Act - Routine urgency should require approval
      const routineResult = await rbacService.requestEmergencyAccess(
        provider.id,
        patientId,
        'Routine emergency access',
        { urgency: 'routine' }
      );

      // Assert
      expect(criticalResult.accessGranted).toBe(true);
      expect(routineResult.accessGranted).toBe(false);
      expect(routineResult.requiresApproval).toBe(true);
    });
  });

  describe('Security and Audit Integration', () => {
    it('should log all permission checks for audit trail', async () => {
      // Arrange
      const user = global.mockUser;
      const permission = 'read:own_medical_data';

      // Act
      await rbacService.hasPermission(user, permission);

      // Assert
      expect(mockAuditLogger.logMedicalDataAccess).toHaveBeenCalledWith(
        'permission_check',
        'rbac_system',
        expect.any(String),
        expect.objectContaining({
          user_id: user.id,
          permission_requested: permission,
          result: expect.any(Boolean)
        })
      );
    });

    it('should detect and log suspicious permission patterns', async () => {
      // Arrange
      const user = global.mockUser;
      const suspiciousPermissions = [
        'read:all_data',
        'manage:users',
        'emergency_override',
        'delete:audit_logs'
      ];

      // Act
      for (const permission of suspiciousPermissions) {
        await rbacService.hasPermission(user, permission);
      }

      // Assert
      expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
        'suspicious_permission_pattern',
        'high',
        expect.objectContaining({
          user_id: user.id,
          permissions_attempted: suspiciousPermissions,
          pattern_detected: 'privilege_escalation_attempt'
        })
      );
    });

    it('should integrate with rate limiting for permission checks', async () => {
      // Arrange
      const user = global.mockUser;
      const permission = 'read:own_medical_data';

      // Mock rate limiter
      const mockRateLimiter = global.testHelpers.createMockRateLimiter();
      mockRateLimiter.checkLimit.mockResolvedValue({ allowed: false, remaining: 0 });

      // Act
      const result = await rbacService.hasPermission(user, permission);

      // Assert
      expect(result).toBe(false);
      expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
        'rate_limit_exceeded',
        'medium',
        expect.objectContaining({
          user_id: user.id,
          resource_type: 'permission_check',
          action_blocked: true
        })
      );
    });

    it('should handle concurrent permission checks efficiently', async () => {
      // Arrange
      const users = [global.mockUser, global.mockProvider, global.mockAdmin];
      const permissions = ['read:own_medical_data', 'read:patient_medical_data', 'read:all_data'];

      const permissionChecks = users.flatMap(user =>
        permissions.map(permission =>
          rbacService.hasPermission(user, permission)
        )
      );

      // Act
      const startTime = performance.now();
      const results = await Promise.all(permissionChecks);
      const endTime = performance.now();

      // Assert
      expect(results.length).toBe(9); // 3 users × 3 permissions
      expect(endTime - startTime).toBeLessThan(2000); // Should complete within 2 seconds
      expect(results.every(result => typeof result === 'boolean')).toBe(true);
    });
  });
});
