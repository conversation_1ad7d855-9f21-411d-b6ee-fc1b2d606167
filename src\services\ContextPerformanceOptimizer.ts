/**
 * Context Performance Optimizer
 * 
 * Advanced performance optimization for context assembly including intelligent caching,
 * token optimization, and memory management for medical AI consultations.
 */

export interface PerformanceMetrics {
  assemblyTime: number;
  cacheHitRate: number;
  tokenEfficiency: number;
  memoryUsage: number;
  optimizationSavings: number;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  expiresAt: number;
  size: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface CacheConfig {
  maxSize: number; // Maximum cache size in MB
  defaultTTL: number; // Default time-to-live in milliseconds
  maxEntries: number; // Maximum number of entries
  compressionEnabled: boolean;
  persistToDisk: boolean;
}

export interface OptimizationStrategy {
  name: string;
  enabled: boolean;
  priority: number;
  estimatedSavings: number; // Percentage
  qualityImpact: number; // -100 to 100
}

class ContextPerformanceOptimizer {
  private cache = new Map<string, CacheEntry<any>>();
  private performanceMetrics: PerformanceMetrics = {
    assemblyTime: 0,
    cacheHitRate: 0,
    tokenEfficiency: 0,
    memoryUsage: 0,
    optimizationSavings: 0
  };

  private readonly DEFAULT_CONFIG: CacheConfig = {
    maxSize: 50, // 50MB
    defaultTTL: 30 * 60 * 1000, // 30 minutes
    maxEntries: 1000,
    compressionEnabled: true,
    persistToDisk: false
  };

  private config: CacheConfig;

  private readonly OPTIMIZATION_STRATEGIES: OptimizationStrategy[] = [
    {
      name: 'Intelligent Caching',
      enabled: true,
      priority: 1,
      estimatedSavings: 40,
      qualityImpact: 0
    },
    {
      name: 'Token Compression',
      enabled: true,
      priority: 2,
      estimatedSavings: 25,
      qualityImpact: -5
    },
    {
      name: 'Context Deduplication',
      enabled: true,
      priority: 3,
      estimatedSavings: 15,
      qualityImpact: 0
    },
    {
      name: 'Lazy Loading',
      enabled: true,
      priority: 4,
      estimatedSavings: 20,
      qualityImpact: -2
    },
    {
      name: 'Predictive Prefetching',
      enabled: false,
      priority: 5,
      estimatedSavings: 30,
      qualityImpact: 5
    }
  ];

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...this.DEFAULT_CONFIG, ...config };
    this.initializePerformanceMonitoring();
  }

  /**
   * Enhanced caching with intelligent eviction
   */
  async getCached<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check expiration
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    // Update access metrics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    // Update cache hit rate
    this.updateCacheHitRate(true);
    
    return entry.data;
  }

  /**
   * Set cache with intelligent prioritization
   */
  async setCached<T>(
    key: string, 
    data: T, 
    options: {
      ttl?: number;
      priority?: 'low' | 'medium' | 'high' | 'critical';
      compress?: boolean;
    } = {}
  ): Promise<void> {
    const {
      ttl = this.config.defaultTTL,
      priority = 'medium',
      compress = this.config.compressionEnabled
    } = options;

    // Calculate data size
    const size = this.calculateDataSize(data);
    
    // Check if we need to evict entries
    await this.ensureCacheSpace(size);

    // Compress data if enabled
    const processedData = compress ? await this.compressData(data) : data;

    const entry: CacheEntry<T> = {
      data: processedData,
      timestamp: Date.now(),
      accessCount: 0,
      lastAccessed: Date.now(),
      expiresAt: Date.now() + ttl,
      size,
      priority
    };

    this.cache.set(key, entry);
    this.updateCacheHitRate(false);
  }

  /**
   * Optimize context assembly performance
   */
  async optimizeContextAssembly<T>(
    assemblyFunction: () => Promise<T>,
    cacheKey: string,
    options: {
      enableCaching?: boolean;
      enableCompression?: boolean;
      enableProfiling?: boolean;
    } = {}
  ): Promise<T> {
    const startTime = Date.now();
    const {
      enableCaching = true,
      enableCompression = true,
      enableProfiling = true
    } = options;

    // Try cache first
    if (enableCaching) {
      const cached = await this.getCached<T>(cacheKey);
      if (cached) {
        if (enableProfiling) {
          this.recordPerformanceMetric('assemblyTime', Date.now() - startTime);
        }
        return cached;
      }
    }

    // Execute assembly function
    const result = await assemblyFunction();

    // Cache result
    if (enableCaching) {
      await this.setCached(cacheKey, result, {
        compress: enableCompression,
        priority: 'high'
      });
    }

    // Record performance metrics
    if (enableProfiling) {
      this.recordPerformanceMetric('assemblyTime', Date.now() - startTime);
    }

    return result;
  }

  /**
   * Token optimization strategies
   */
  optimizeTokenUsage(content: string, maxTokens: number): {
    optimizedContent: string;
    tokensSaved: number;
    strategies: string[];
  } {
    let optimizedContent = content;
    let tokensSaved = 0;
    const strategies: string[] = [];

    // Strategy 1: Remove redundant whitespace
    const beforeWhitespace = optimizedContent.length;
    optimizedContent = optimizedContent.replace(/\s+/g, ' ').trim();
    const whitespaceTokensSaved = Math.ceil((beforeWhitespace - optimizedContent.length) / 4);
    if (whitespaceTokensSaved > 0) {
      tokensSaved += whitespaceTokensSaved;
      strategies.push('Whitespace optimization');
    }

    // Strategy 2: Abbreviate common medical terms
    const medicalAbbreviations = {
      'blood pressure': 'BP',
      'heart rate': 'HR',
      'respiratory rate': 'RR',
      'temperature': 'temp',
      'history of': 'h/o',
      'patient reports': 'pt reports',
      'symptoms include': 'sx include'
    };

    const beforeAbbreviation = optimizedContent.length;
    Object.entries(medicalAbbreviations).forEach(([full, abbrev]) => {
      optimizedContent = optimizedContent.replace(new RegExp(full, 'gi'), abbrev);
    });
    const abbreviationTokensSaved = Math.ceil((beforeAbbreviation - optimizedContent.length) / 4);
    if (abbreviationTokensSaved > 0) {
      tokensSaved += abbreviationTokensSaved;
      strategies.push('Medical abbreviations');
    }

    // Strategy 3: Remove redundant phrases
    const redundantPhrases = [
      'the patient states that',
      'it should be noted that',
      'please be aware that',
      'it is important to mention'
    ];

    const beforeRedundancy = optimizedContent.length;
    redundantPhrases.forEach(phrase => {
      optimizedContent = optimizedContent.replace(new RegExp(phrase, 'gi'), '');
    });
    const redundancyTokensSaved = Math.ceil((beforeRedundancy - optimizedContent.length) / 4);
    if (redundancyTokensSaved > 0) {
      tokensSaved += redundancyTokensSaved;
      strategies.push('Redundancy removal');
    }

    // Strategy 4: Truncate if still over limit
    const currentTokens = Math.ceil(optimizedContent.length / 4);
    if (currentTokens > maxTokens) {
      const targetLength = maxTokens * 4 - 3; // Leave room for "..."
      optimizedContent = optimizedContent.substring(0, targetLength) + '...';
      tokensSaved += currentTokens - maxTokens;
      strategies.push('Content truncation');
    }

    return {
      optimizedContent,
      tokensSaved,
      strategies
    };
  }

  /**
   * Memory optimization
   */
  optimizeMemoryUsage(): {
    memoryFreed: number;
    entriesEvicted: number;
    strategies: string[];
  } {
    let memoryFreed = 0;
    let entriesEvicted = 0;
    const strategies: string[] = [];

    // Strategy 1: Remove expired entries
    const beforeExpired = this.cache.size;
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        memoryFreed += entry.size;
        this.cache.delete(key);
        entriesEvicted++;
      }
    }
    if (entriesEvicted > 0) {
      strategies.push(`Expired entries cleanup (${entriesEvicted} entries)`);
    }

    // Strategy 2: LRU eviction for low-priority items
    if (this.cache.size > this.config.maxEntries * 0.8) {
      const lowPriorityEntries = Array.from(this.cache.entries())
        .filter(([_, entry]) => entry.priority === 'low')
        .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

      const toEvict = Math.min(lowPriorityEntries.length, Math.floor(this.cache.size * 0.1));
      for (let i = 0; i < toEvict; i++) {
        const entryData = lowPriorityEntries[i];
        if (entryData) {
          const [key, entry] = entryData;
          memoryFreed += entry.size;
          this.cache.delete(key);
          entriesEvicted++;
        }
      }
      if (toEvict > 0) {
        strategies.push(`LRU eviction (${toEvict} low-priority entries)`);
      }
    }

    // Strategy 3: Compress large entries
    let compressionSavings = 0;
    for (const [key, entry] of this.cache.entries()) {
      if (entry.size > 1024 && !this.isCompressed(entry.data)) { // 1KB threshold
        const originalSize = entry.size;
        entry.data = this.compressData(entry.data);
        entry.size = this.calculateDataSize(entry.data);
        compressionSavings += originalSize - entry.size;
      }
    }
    if (compressionSavings > 0) {
      memoryFreed += compressionSavings;
      strategies.push(`Data compression (${Math.round(compressionSavings / 1024)}KB saved)`);
    }

    return {
      memoryFreed,
      entriesEvicted,
      strategies
    };
  }

  /**
   * Predictive prefetching
   */
  async predictivelyPrefetch(userId: string, sessionId: string): Promise<void> {
    // Analyze usage patterns to predict what context might be needed
    const patterns = this.analyzeUsagePatterns(userId);
    
    // Prefetch likely-needed context
    const prefetchTasks = patterns.map(pattern => 
      this.prefetchContext(pattern.contextType, pattern.priority)
    );
    
    await Promise.all(prefetchTasks);
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Get cache statistics
   */
  getCacheStatistics(): {
    size: number;
    entries: number;
    hitRate: number;
    memoryUsage: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    const entries = Array.from(this.cache.values());
    const totalSize = entries.reduce((sum, entry) => sum + entry.size, 0);
    const timestamps = entries.map(entry => entry.timestamp);

    return {
      size: this.cache.size,
      entries: this.cache.size,
      hitRate: this.performanceMetrics.cacheHitRate,
      memoryUsage: totalSize,
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : 0,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : 0
    };
  }

  // Private helper methods
  private initializePerformanceMonitoring(): void {
    // Set up periodic cleanup
    setInterval(() => {
      this.optimizeMemoryUsage();
    }, 5 * 60 * 1000); // Every 5 minutes

    // Set up performance metric collection
    setInterval(() => {
      this.updatePerformanceMetrics();
    }, 60 * 1000); // Every minute
  }

  private async ensureCacheSpace(requiredSize: number): Promise<void> {
    const currentSize = Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.size, 0);
    
    const maxSizeBytes = this.config.maxSize * 1024 * 1024; // Convert MB to bytes
    
    if (currentSize + requiredSize > maxSizeBytes) {
      // Evict entries using LRU strategy
      const entries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
      
      let freedSpace = 0;
      for (const [key, entry] of entries) {
        if (freedSpace >= requiredSize) break;
        
        this.cache.delete(key);
        freedSpace += entry.size;
      }
    }
  }

  private calculateDataSize(data: any): number {
    // Rough estimation of data size in bytes
    return JSON.stringify(data).length * 2; // UTF-16 encoding
  }

  private async compressData(data: any): Promise<any> {
    // Simple compression simulation - in real implementation, use actual compression
    if (typeof data === 'string') {
      return data.replace(/\s+/g, ' ').trim();
    }
    return data;
  }

  private isCompressed(data: any): boolean {
    // Check if data is already compressed
    return typeof data === 'string' && !data.includes('  '); // No double spaces
  }

  private updateCacheHitRate(isHit: boolean): void {
    // Simple moving average for cache hit rate
    const currentRate = this.performanceMetrics.cacheHitRate;
    const newRate = isHit ? 
      (currentRate * 0.9 + 100 * 0.1) : 
      (currentRate * 0.9 + 0 * 0.1);
    
    this.performanceMetrics.cacheHitRate = newRate;
  }

  private recordPerformanceMetric(metric: keyof PerformanceMetrics, value: number): void {
    // Update performance metrics with exponential moving average
    const alpha = 0.1; // Smoothing factor
    this.performanceMetrics[metric] = 
      this.performanceMetrics[metric] * (1 - alpha) + value * alpha;
  }

  private updatePerformanceMetrics(): void {
    const cacheStats = this.getCacheStatistics();
    this.performanceMetrics.memoryUsage = cacheStats.memoryUsage;
    
    // Calculate token efficiency based on optimization strategies
    const enabledStrategies = this.OPTIMIZATION_STRATEGIES.filter(s => s.enabled);
    this.performanceMetrics.tokenEfficiency = 
      enabledStrategies.reduce((sum, s) => sum + s.estimatedSavings, 0) / enabledStrategies.length;
    
    // Calculate optimization savings
    this.performanceMetrics.optimizationSavings = 
      enabledStrategies.reduce((sum, s) => sum + s.estimatedSavings, 0);
  }

  private analyzeUsagePatterns(userId: string): Array<{
    contextType: string;
    priority: number;
    frequency: number;
  }> {
    // Analyze historical usage patterns
    // This would typically involve database queries and ML analysis
    return [
      { contextType: 'medical_history', priority: 8, frequency: 0.9 },
      { contextType: 'current_medications', priority: 7, frequency: 0.8 },
      { contextType: 'regional_context', priority: 5, frequency: 0.6 }
    ];
  }

  private async prefetchContext(contextType: string, priority: number): Promise<void> {
    // Implement context prefetching logic
    console.log(`Prefetching ${contextType} with priority ${priority}`);
  }
}

export const contextPerformanceOptimizer = new ContextPerformanceOptimizer();
export default contextPerformanceOptimizer;
