/**
 * PWA SERVICE WORKER CACHE MANAGER WITH HIPAA COMPLIANCE
 * 
 * This service provides PWA service worker cache management with:
 * - HIPAA-compliant data removal and cleanup
 * - Emergency medical data prioritization
 * - Secure cache versioning and migration
 * - Automatic cleanup strategies for sensitive data
 * - Performance optimization for medical workflows
 * - Offline-first architecture for critical medical functions
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Emergency medical data must be cached with highest priority
 * - Sensitive medical data must be securely removed
 * - Cache operations must maintain audit trail
 * - Offline functionality must preserve patient safety
 * - Data integrity must be maintained across cache updates
 */

import type {
  ServiceWorkerCacheConfig,
  ServiceWorkerCacheStrategy,
  PWACacheManager,
  CacheCleanupResult,
  MedicalDataPriority
} from '../types/cache';

import auditLogger from './auditLogger';
import encryptionService from './encryptionService';

interface PWACacheEntry {
  url: string;
  response: Response;
  timestamp: number;
  priority: MedicalDataPriority;
  isEmergencyData: boolean;
  isMedicalData: boolean;
  encrypted: boolean;
  expiresAt: number;
  accessCount: number;
  lastAccessed: number;
  size: number;
}

interface CacheStorageInfo {
  name: string;
  version: string;
  size: number;
  entryCount: number;
  emergencyDataCount: number;
  medicalDataCount: number;
  lastCleanup: number;
}

class PWAServiceWorkerCacheManager implements PWACacheManager {
  readonly version: string;
  readonly strategies: ServiceWorkerCacheStrategy[];
  readonly emergencyFallbacks: Record<string, string>;
  readonly offlinePages: string[];
  readonly criticalResources: string[];
  readonly updateNotification: boolean;
  readonly backgroundSync: boolean;
  readonly periodicSync: boolean;

  private readonly config: ServiceWorkerCacheConfig;
  private readonly cacheRegistry: Map<string, CacheStorageInfo>;
  private readonly emergencyRoutes: Set<string>;
  private readonly medicalDataPatterns: RegExp[];
  private cleanupInProgress: boolean;

  constructor(config: Partial<ServiceWorkerCacheConfig> = {}) {
    this.config = {
      cacheName: 'voicehealth-ai-v1',
      version: '1.0.0',
      staticAssets: [
        '/',
        '/manifest.json',
        '/offline.html',
        '/emergency-access',
        '/static/css/main.css',
        '/static/js/main.js'
      ],
      dynamicCaching: true,
      networkFirst: [
        '/api/medical/*',
        '/api/emergency/*',
        '/api/auth/*'
      ],
      cacheFirst: [
        '/static/*',
        '/assets/*',
        '/icons/*'
      ],
      emergencyRoutes: [
        '/emergency-access',
        '/emergency-contacts',
        '/critical-alerts'
      ],
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      maxEntries: 1000,
      purgeOnUpdate: true,
      ...config
    };

    this.version = this.config.version;
    this.emergencyFallbacks = {
      '/api/medical/conditions': '/offline-medical-data.json',
      '/api/medical/medications': '/offline-medications.json',
      '/api/emergency/contacts': '/offline-emergency-contacts.json'
    };
    this.offlinePages = ['/offline.html', '/emergency-offline.html'];
    this.criticalResources = [...this.config.staticAssets, ...this.config.emergencyRoutes];
    this.updateNotification = true;
    this.backgroundSync = true;
    this.periodicSync = true;

    this.strategies = this.initializeStrategies();
    this.cacheRegistry = new Map();
    this.emergencyRoutes = new Set(this.config.emergencyRoutes);
    this.medicalDataPatterns = [
      /\/api\/medical\/.*/,
      /\/api\/patient\/.*/,
      /\/api\/provider\/.*/,
      /\/api\/emergency\/.*/
    ];
    this.cleanupInProgress = false;

    this.initializeServiceWorker();
  }

  /**
   * Initialize service worker with cache strategies
   */
  private initializeServiceWorker(): void {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw-medical.js', {
        scope: '/',
        updateViaCache: 'none'
      }).then(registration => {
        this.handleServiceWorkerRegistration(registration);
      }).catch(error => {
        console.error('Service Worker registration failed:', error);
        auditLogger.logSecurityEvent(
          'service_worker_registration_failed',
          'high',
          { error: error.message }
        );
      });
    }
  }

  /**
   * Handle service worker registration and updates
   */
  private async handleServiceWorkerRegistration(registration: ServiceWorkerRegistration): Promise<void> {
    // Handle service worker updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            this.handleServiceWorkerUpdate();
          }
        });
      }
    });

    // Listen for messages from service worker
    navigator.serviceWorker.addEventListener('message', event => {
      this.handleServiceWorkerMessage(event.data);
    });

    // Perform initial cache setup
    await this.setupInitialCache();
  }

  /**
   * Setup initial cache with critical medical resources
   */
  private async setupInitialCache(): Promise<void> {
    try {
      const cache = await caches.open(this.config.cacheName);
      
      // Cache critical resources first
      await this.cacheEmergencyResources(cache);
      
      // Cache static assets
      await this.cacheStaticAssets(cache);
      
      // Setup cache registry
      await this.updateCacheRegistry();

      await auditLogger.logSecurityEvent(
        'pwa_cache_initialized',
        'low',
        {
          cache_name: this.config.cacheName,
          version: this.version,
          critical_resources: this.criticalResources.length,
          emergency_routes: this.emergencyRoutes.size
        }
      );
    } catch (error) {
      console.error('Failed to setup initial cache:', error);
      await auditLogger.logSecurityEvent(
        'pwa_cache_setup_failed',
        'high',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      );
    }
  }

  /**
   * Cache emergency medical resources with highest priority
   */
  private async cacheEmergencyResources(cache: Cache): Promise<void> {
    const emergencyResources = [
      ...this.config.emergencyRoutes,
      '/offline-emergency-data.json',
      '/emergency-protocols.json',
      '/critical-medical-info.json'
    ];

    for (const resource of emergencyResources) {
      try {
        const response = await fetch(resource);
        if (response.ok) {
          await cache.put(resource, response.clone());
          
          await auditLogger.logMedicalDataAccess(
            'emergency_cache_store',
            'emergency_data',
            resource,
            {
              priority: 'emergency',
              cache_name: this.config.cacheName,
              resource_type: 'emergency_resource'
            }
          );
        }
      } catch (error) {
        console.warn(`Failed to cache emergency resource: ${resource}`, error);
      }
    }
  }

  /**
   * Cache static assets for offline functionality
   */
  private async cacheStaticAssets(cache: Cache): Promise<void> {
    try {
      await cache.addAll(this.config.staticAssets);
    } catch (error) {
      console.error('Failed to cache static assets:', error);
      
      // Try to cache assets individually
      for (const asset of this.config.staticAssets) {
        try {
          const response = await fetch(asset);
          if (response.ok) {
            await cache.put(asset, response);
          }
        } catch (assetError) {
          console.warn(`Failed to cache asset: ${asset}`, assetError);
        }
      }
    }
  }

  /**
   * Perform HIPAA-compliant cache cleanup
   */
  async performHIPAACompliantCleanup(): Promise<CacheCleanupResult> {
    if (this.cleanupInProgress) {
      return {
        success: false,
        entriesRemoved: 0,
        spaceFreed: 0,
        strategy: 'hybrid',
        duration: 0,
        emergencyDataPreserved: 0,
        errors: ['Cleanup already in progress']
      };
    }

    this.cleanupInProgress = true;
    const startTime = performance.now();
    let entriesRemoved = 0;
    let spaceFreed = 0;
    let emergencyDataPreserved = 0;
    const errors: string[] = [];

    try {
      const cacheNames = await caches.keys();
      
      for (const cacheName of cacheNames) {
        if (this.isOldCacheVersion(cacheName)) {
          // Remove old cache versions completely
          await caches.delete(cacheName);
          entriesRemoved += await this.getCacheEntryCount(cacheName);
          
          await auditLogger.logSecurityEvent(
            'old_cache_version_removed',
            'medium',
            {
              cache_name: cacheName,
              reason: 'version_cleanup',
              hipaa_compliant: true
            }
          );
        } else if (this.isCurrentCache(cacheName)) {
          // Clean up current cache selectively
          const cleanupResult = await this.cleanupCurrentCache(cacheName);
          entriesRemoved += cleanupResult.entriesRemoved;
          spaceFreed += cleanupResult.spaceFreed;
          emergencyDataPreserved += cleanupResult.emergencyDataPreserved;
          errors.push(...cleanupResult.errors || []);
        }
      }

      // Secure deletion of sensitive cached data
      await this.secureDeleteSensitiveData();

      const duration = performance.now() - startTime;

      await auditLogger.logSecurityEvent(
        'hipaa_compliant_cache_cleanup',
        'low',
        {
          entries_removed: entriesRemoved,
          space_freed: spaceFreed,
          emergency_data_preserved: emergencyDataPreserved,
          duration,
          hipaa_compliant: true
        }
      );

      return {
        success: true,
        entriesRemoved,
        spaceFreed,
        strategy: 'hybrid',
        duration,
        emergencyDataPreserved,
        ...(errors.length > 0 && { errors })
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown cleanup error';
      errors.push(errorMessage);

      await auditLogger.logSecurityEvent(
        'cache_cleanup_failed',
        'high',
        {
          error: errorMessage,
          duration: performance.now() - startTime
        }
      );

      return {
        success: false,
        entriesRemoved,
        spaceFreed,
        strategy: 'hybrid',
        duration: performance.now() - startTime,
        emergencyDataPreserved,
        errors
      };
    } finally {
      this.cleanupInProgress = false;
    }
  }

  /**
   * Clean up current cache while preserving emergency data
   */
  private async cleanupCurrentCache(cacheName: string): Promise<CacheCleanupResult> {
    const cache = await caches.open(cacheName);
    const requests = await cache.keys();
    
    let entriesRemoved = 0;
    let spaceFreed = 0;
    let emergencyDataPreserved = 0;
    const errors: string[] = [];

    for (const request of requests) {
      try {
        const url = request.url;
        
        // Never remove emergency data
        if (this.isEmergencyData(url)) {
          emergencyDataPreserved++;
          continue;
        }

        // Check if data should be removed based on age and priority
        const shouldRemove = await this.shouldRemoveFromCache(cache, request);
        
        if (shouldRemove) {
          const response = await cache.match(request);
          if (response) {
            spaceFreed += await this.estimateResponseSize(response);
          }
          
          await cache.delete(request);
          entriesRemoved++;

          // Audit log for medical data removal
          if (this.isMedicalData(url)) {
            await auditLogger.logMedicalDataAccess(
              'cache_remove',
              'medical_data',
              url,
              {
                reason: 'hipaa_compliant_cleanup',
                cache_name: cacheName,
                secure_deletion: true
              }
            );
          }
        }
      } catch (error) {
        errors.push(`Failed to process ${request.url}: ${error}`);
      }
    }

    return {
      success: true,
      entriesRemoved,
      spaceFreed,
      strategy: 'hybrid',
      duration: 0,
      emergencyDataPreserved,
      ...(errors.length > 0 && { errors })
    };
  }

  /**
   * Securely delete sensitive cached data
   */
  private async secureDeleteSensitiveData(): Promise<void> {
    // Clear any temporary caches that might contain sensitive data
    const tempCacheNames = await caches.keys();
    
    for (const cacheName of tempCacheNames) {
      if (cacheName.includes('temp') || cacheName.includes('sensitive')) {
        await caches.delete(cacheName);
        
        await auditLogger.logSecurityEvent(
          'sensitive_cache_deleted',
          'medium',
          {
            cache_name: cacheName,
            reason: 'security_cleanup',
            secure_deletion: true
          }
        );
      }
    }

    // Clear any encrypted data from memory
    if (typeof crypto !== 'undefined' && crypto.subtle) {
      // Force garbage collection if available
      if (typeof gc === 'function') {
        gc();
      }
    }
  }

  /**
   * Initialize cache strategies for different types of requests
   */
  private initializeStrategies(): ServiceWorkerCacheStrategy[] {
    return [
      {
        name: 'cache-first',
        patterns: this.config.cacheFirst,
        options: {
          cacheName: `${this.config.cacheName}-static`,
          cacheableResponse: {
            statuses: [0, 200]
          }
        }
      },
      {
        name: 'network-first',
        patterns: this.config.networkFirst,
        options: {
          cacheName: `${this.config.cacheName}-dynamic`,
          networkTimeoutSeconds: 5,
          cacheableResponse: {
            statuses: [0, 200]
          }
        }
      },
      {
        name: 'stale-while-revalidate',
        patterns: ['/api/user/*', '/api/preferences/*'],
        options: {
          cacheName: `${this.config.cacheName}-user-data`,
          cacheableResponse: {
            statuses: [0, 200]
          }
        }
      },
      {
        name: 'cache-only',
        patterns: this.config.emergencyRoutes,
        options: {
          cacheName: `${this.config.cacheName}-emergency`,
          cacheableResponse: {
            statuses: [0, 200]
          }
        }
      }
    ];
  }

  // Helper methods
  private isOldCacheVersion(cacheName: string): boolean {
    return cacheName.includes('voicehealth-ai') && !cacheName.includes(this.version);
  }

  private isCurrentCache(cacheName: string): boolean {
    return cacheName.includes(this.config.cacheName);
  }

  private isEmergencyData(url: string): boolean {
    return this.emergencyRoutes.has(new URL(url).pathname) ||
           url.includes('emergency') ||
           url.includes('critical');
  }

  private isMedicalData(url: string): boolean {
    return this.medicalDataPatterns.some(pattern => pattern.test(url));
  }

  private async shouldRemoveFromCache(cache: Cache, request: Request): Promise<boolean> {
    const response = await cache.match(request);
    if (!response) return false;

    const dateHeader = response.headers.get('date');
    if (!dateHeader) return false;

    const responseDate = new Date(dateHeader);
    const age = Date.now() - responseDate.getTime();

    return age > this.config.maxAge;
  }

  private async estimateResponseSize(response: Response): Promise<number> {
    const clone = response.clone();
    const arrayBuffer = await clone.arrayBuffer();
    return arrayBuffer.byteLength;
  }

  private async getCacheEntryCount(cacheName: string): Promise<number> {
    try {
      const cache = await caches.open(cacheName);
      const requests = await cache.keys();
      return requests.length;
    } catch {
      return 0;
    }
  }

  private async updateCacheRegistry(): Promise<void> {
    const cacheNames = await caches.keys();
    
    for (const cacheName of cacheNames) {
      if (this.isCurrentCache(cacheName)) {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        let emergencyDataCount = 0;
        let medicalDataCount = 0;
        let totalSize = 0;

        for (const request of requests) {
          const url = request.url;
          if (this.isEmergencyData(url)) emergencyDataCount++;
          if (this.isMedicalData(url)) medicalDataCount++;
          
          const response = await cache.match(request);
          if (response) {
            totalSize += await this.estimateResponseSize(response);
          }
        }

        this.cacheRegistry.set(cacheName, {
          name: cacheName,
          version: this.version,
          size: totalSize,
          entryCount: requests.length,
          emergencyDataCount,
          medicalDataCount,
          lastCleanup: Date.now()
        });
      }
    }
  }

  private handleServiceWorkerUpdate(): void {
    if (this.updateNotification) {
      // Notify user of available update
      const event = new CustomEvent('sw-update-available', {
        detail: { version: this.version }
      });
      window.dispatchEvent(event);
    }
  }

  private handleServiceWorkerMessage(data: any): void {
    switch (data.type) {
      case 'cache-updated':
        this.updateCacheRegistry();
        break;
      case 'emergency-cache-accessed':
        auditLogger.logMedicalDataAccess(
          'emergency_cache_access',
          'emergency_data',
          data.url,
          {
            priority: 'emergency',
            offline_access: true
          }
        );
        break;
    }
  }

  /**
   * Get cache statistics and health information
   */
  async getCacheStats(): Promise<{
    totalCaches: number;
    totalSize: number;
    emergencyDataCount: number;
    medicalDataCount: number;
    lastCleanup: number;
    cacheHealth: 'healthy' | 'warning' | 'critical';
  }> {
    await this.updateCacheRegistry();
    
    let totalSize = 0;
    let emergencyDataCount = 0;
    let medicalDataCount = 0;
    let lastCleanup = 0;

    for (const info of this.cacheRegistry.values()) {
      totalSize += info.size;
      emergencyDataCount += info.emergencyDataCount;
      medicalDataCount += info.medicalDataCount;
      lastCleanup = Math.max(lastCleanup, info.lastCleanup);
    }

    const cacheHealth = this.determineCacheHealth(totalSize, lastCleanup);

    return {
      totalCaches: this.cacheRegistry.size,
      totalSize,
      emergencyDataCount,
      medicalDataCount,
      lastCleanup,
      cacheHealth
    };
  }

  private determineCacheHealth(totalSize: number, lastCleanup: number): 'healthy' | 'warning' | 'critical' {
    const maxSize = 100 * 1024 * 1024; // 100MB
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days

    if (totalSize > maxSize * 0.9 || Date.now() - lastCleanup > maxAge) {
      return 'critical';
    } else if (totalSize > maxSize * 0.7 || Date.now() - lastCleanup > maxAge * 0.7) {
      return 'warning';
    }
    
    return 'healthy';
  }
}

export default PWAServiceWorkerCacheManager;
