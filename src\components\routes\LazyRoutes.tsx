/**
 * LAZY-<PERSON><PERSON><PERSON><PERSON> ROUTES FOR MEDICAL APPLICATION (SIMPLIFIED)
 *
 * Main orchestrator for lazy loading routes with focused, maintainable structure.
 * This file provides core routing functionality without duplicate declarations.
 *
 * PERFORMANCE TARGETS:
 * - Emergency routes load < 100ms
 * - Role-based routes load < 500ms
 * - Offline fallbacks available
 */

import React from 'react';
import { createLazyComponent } from '../../utils/lazyLoading';
import type { UserRole } from '../../types/auth';

// =============================================================================
// CORE ROUTE EXPORTS (Re-exported from modular files)
// =============================================================================

// Emergency Routes (Critical - Highest Priority)
export {
  EmergencyProtocols,
  EmergencyConsultation,
  CriticalVitals,
  OfflineEmergencyProtocols,
  preloadEmergencyRoutes,
  checkEmergencyRoutesAvailability
} from './EmergencyRoutes';

// Patient Routes
export {
  PatientDashboard,
  MedicalHistory,
  SymptomTracker,
  MedicationManager,
  AppointmentBooking,
  preloadPatientRoutes,
  getPatientRoutePriorities
} from './PatientRoutes';

// Provider Routes
export {
  ProviderDashboard,
  PatientManagement,
  preloadProviderRoutes,
  getProviderRoutePriorities,
  checkProviderRouteAccess
} from './ProviderRoutes';

// Auth Routes
export {
  LoginPage,
  RegisterPage,
  ForgotPassword,
  RoleBasedDashboard,
  WelcomeLanguageSelection,
  CountryRegionalSelection,
  preloadAuthRoutes,
  getDashboardForRole,
  requiresAuthentication
} from './AuthRoutes';

// =============================================================================
// ADDITIONAL SHARED COMPONENTS (Created locally)
// =============================================================================

// Additional shared components that aren't in modular files
export const MedicalCharts = createLazyComponent(
  () => Promise.resolve({
    default: () => (
      <div className="p-8 bg-gray-50 border border-gray-200 rounded">
        <h2 className="text-gray-800 font-bold">📊 Medical Charts</h2>
        <p className="text-gray-700 mt-2">Medical charts system is loading...</p>
      </div>
    )
  }),
  { priority: 'low' }
);

export const DataVisualization = createLazyComponent(
  () => Promise.resolve({
    default: () => (
      <div className="p-8 bg-gray-50 border border-gray-200 rounded">
        <h2 className="text-gray-800 font-bold">📈 Data Visualization</h2>
        <p className="text-gray-700 mt-2">Data visualization tools are loading...</p>
      </div>
    )
  }),
  { priority: 'low' }
);

export const NotificationCenter = createLazyComponent(
  () => Promise.resolve({
    default: () => (
      <div className="p-8 bg-blue-50 border border-blue-200 rounded">
        <h2 className="text-blue-800 font-bold">🔔 Notification Center</h2>
        <p className="text-blue-700 mt-2">Notification system is loading...</p>
      </div>
    )
  }),
  { priority: 'normal' }
);

export const HelpCenter = createLazyComponent(
  () => Promise.resolve({
    default: () => (
      <div className="p-8 bg-green-50 border border-green-200 rounded">
        <h2 className="text-green-800 font-bold">❓ Help Center</h2>
        <p className="text-green-700 mt-2">Help documentation is loading...</p>
      </div>
    )
  }),
  { priority: 'low' }
);

export const PrivacyPolicy = createLazyComponent(
  () => Promise.resolve({
    default: () => (
      <div className="p-8 bg-gray-50 border border-gray-200 rounded">
        <h2 className="text-gray-800 font-bold">🔒 Privacy Policy</h2>
        <p className="text-gray-700 mt-2">Privacy policy is loading...</p>
      </div>
    )
  }),
  { priority: 'low' }
);

// =============================================================================
// ROUTE MANAGEMENT UTILITIES
// =============================================================================

/**
 * Preload routes based on user role
 */
export function preloadRoleBasedRoutes(userRole: UserRole): void {
  console.log(`🚀 Preloading routes for role: ${userRole}`);

  switch (userRole) {
    case 'patient':
      preloadPatientRoutes();
      break;
    case 'healthcare_provider':
      preloadProviderRoutes();
      break;
    case 'emergency_responder':
      preloadEmergencyRoutes();
      break;
    case 'admin':
      // Admin routes would be preloaded here
      break;
    default:
      preloadAuthRoutes();
  }
}

/**
 * Get route loading priority based on user role and current route
 */
export function getRouteLoadingPriority(userRole: UserRole, routeName: string): 'low' | 'normal' | 'high' | 'emergency' {
  // Emergency routes always have emergency priority
  if (routeName.includes('emergency') || routeName.includes('critical')) {
    return 'emergency';
  }

  // Dashboard routes have high priority
  if (routeName.includes('dashboard')) {
    return 'high';
  }

  // Medical data routes have normal priority
  if (routeName.includes('medical') || routeName.includes('consultation')) {
    return 'normal';
  }

  // Everything else has low priority
  return 'low';
}

/**
 * Initialize all route modules
 */
export async function initializeRoutes(): Promise<void> {
  console.log('🔧 Initializing route modules...');

  try {
    // Check emergency routes availability first
    const emergencyAvailable = await checkEmergencyRoutesAvailability();
    if (!emergencyAvailable) {
      console.warn('⚠️ Emergency routes not fully available');
    }

    // Preload emergency routes immediately
    preloadEmergencyRoutes();

    console.log('✅ Route modules initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize route modules:', error);
  }
}

/**
 * Get all available routes by category
 */
export function getRoutesByCategory() {
  return {
    emergency: ['EmergencyProtocols', 'EmergencyConsultation', 'CriticalVitals'],
    patient: ['PatientDashboard', 'MedicalHistory', 'SymptomTracker', 'MedicationManager'],
    provider: ['ProviderDashboard', 'PatientManagement', 'ConsultationInterface', 'MedicalRecords'],
    auth: ['LoginPage', 'RegisterPage', 'RoleBasedDashboard'],
    shared: ['MedicalCharts', 'DataVisualization', 'NotificationCenter', 'HelpCenter']
  };
}

// =============================================================================
// ADDITIONAL COMPONENTS (Not in modular files)
// =============================================================================

export const ConsultationInterface = createLazyComponent(
  () => Promise.resolve({
    default: () => (
      <div className="p-8 bg-blue-50 border border-blue-200 rounded">
        <h2 className="text-blue-800 font-bold">🩺 Consultation Interface</h2>
        <p className="text-blue-700 mt-2">Consultation interface is loading...</p>
      </div>
    )
  }),
  { priority: 'high' }
);

export const MedicalRecords = createLazyComponent(
  () => Promise.resolve({
    default: () => (
      <div className="p-8 bg-green-50 border border-green-200 rounded">
        <h2 className="text-green-800 font-bold">📋 Medical Records</h2>
        <p className="text-green-700 mt-2">Medical records system is loading...</p>
      </div>
    )
  }),
  { priority: 'normal' }
);

export const DiagnosisTools = createLazyComponent(
  () => Promise.resolve({
    default: () => (
      <div className="p-8 bg-purple-50 border border-purple-200 rounded">
        <h2 className="text-purple-800 font-bold">🔬 Diagnosis Tools</h2>
        <p className="text-purple-700 mt-2">Diagnostic tools are loading...</p>
      </div>
    )
  }),
  { priority: 'normal' }
);

// =============================================================================
// ROUTE UTILITIES
// =============================================================================

/**
 * Get all available routes by category for the main orchestrator
 */
export function getAvailableRoutes() {
  return {
    emergency: ['EmergencyProtocols', 'EmergencyConsultation', 'CriticalVitals'],
    patient: ['PatientDashboard', 'MedicalHistory', 'SymptomTracker', 'MedicationManager'],
    provider: ['ProviderDashboard', 'PatientManagement', 'ConsultationInterface', 'MedicalRecords'],
    auth: ['LoginPage', 'RegisterPage', 'RoleBasedDashboard'],
    shared: ['MedicalCharts', 'DataVisualization', 'NotificationCenter', 'HelpCenter']
  };
}

/**
 * Initialize route system
 */
export async function initializeRouteSystem(): Promise<void> {
  console.log('🚀 Initializing VoiceHealth AI route system...');

  try {
    // Preload emergency routes immediately
    await preloadEmergencyRoutes();
    console.log('✅ Route system initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize route system:', error);
  }
}
