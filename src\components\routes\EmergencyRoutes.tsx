/**
 * EMERGENCY ROUTES MODULE
 * 
 * Critical emergency routes that must be preloaded for immediate access.
 * These routes have the highest priority and include fallback mechanisms.
 */

import React from 'react';
import { createLazyComponent } from '../../utils/lazyLoading';

// =============================================================================
// EMERGENCY ROUTES (PRELOADED FOR CRITICAL MEDICAL OPERATIONS)
// =============================================================================

export const EmergencyProtocols = createLazyComponent(
  () => import('../emergency/EmergencyProtocols').catch(() => ({
    default: () => (
      <div className="p-8 bg-red-50 border border-red-200 rounded">
        <h2 className="text-red-800 font-bold">🚨 Emergency Protocols</h2>
        <p className="text-red-700 mt-2">Emergency protocols system is loading...</p>
        <div className="mt-4 space-y-2">
          <p className="text-red-600 font-semibold">Immediate Actions:</p>
          <ul className="text-red-600 text-sm space-y-1 ml-4">
            <li>• Call emergency services: 911 (US), 999 (UK), 112 (EU)</li>
            <li>• If chest pain: Chew aspirin if not allergic</li>
            <li>• If breathing difficulty: Sit upright, stay calm</li>
            <li>• If severe bleeding: Apply direct pressure</li>
            <li>• If unconscious: Check breathing, call for help</li>
          </ul>
        </div>
      </div>
    )
  })),
  { 
    priority: 'emergency',
    preload: true,
    errorFallback: ({ error, resetErrorBoundary }) => (
      <div className="p-8 bg-red-100 border border-red-300 rounded">
        <h2 className="text-red-800 font-bold">🚨 EMERGENCY SYSTEM ERROR</h2>
        <p className="text-red-700 mt-2">Critical emergency protocols failed to load.</p>
        <p className="text-red-600 text-sm mt-1">Error: {error.message}</p>
        <div className="mt-4 space-y-2">
          <p className="text-red-600 font-semibold">Emergency Contact Numbers:</p>
          <ul className="text-red-600 text-sm space-y-1 ml-4">
            <li>• Emergency Services: 911 / 999 / 112</li>
            <li>• Poison Control: **************</li>
            <li>• Crisis Hotline: 988</li>
          </ul>
        </div>
        <button 
          onClick={resetErrorBoundary}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry Emergency System
        </button>
      </div>
    )
  }
);

export const EmergencyConsultation = createLazyComponent(
  () => import('../consultation/EmergencyConsultation').catch(() => ({
    default: () => (
      <div className="p-8 bg-red-50 border border-red-200 rounded">
        <h2 className="text-red-800 font-bold">🚨 Emergency Consultation</h2>
        <p className="text-red-700 mt-2">Emergency consultation interface is loading...</p>
        <div className="mt-4 p-4 bg-red-100 rounded">
          <p className="text-red-800 font-semibold">If this is a medical emergency:</p>
          <p className="text-red-700 mt-1">Call emergency services immediately</p>
          <p className="text-red-600 text-sm mt-2">Do not wait for this system to load</p>
        </div>
      </div>
    )
  })),
  { 
    priority: 'emergency',
    preload: true,
    errorFallback: ({ error, resetErrorBoundary }) => (
      <div className="p-8 bg-red-100 border border-red-300 rounded">
        <h2 className="text-red-800 font-bold">🚨 EMERGENCY CONSULTATION ERROR</h2>
        <p className="text-red-700 mt-2">Emergency consultation system failed to load.</p>
        <p className="text-red-600 text-sm mt-1">Error: {error.message}</p>
        <div className="mt-4 p-4 bg-red-200 rounded">
          <p className="text-red-800 font-bold">CALL EMERGENCY SERVICES NOW</p>
          <p className="text-red-700">911 / 999 / 112</p>
        </div>
        <button 
          onClick={resetErrorBoundary}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry Emergency Consultation
        </button>
      </div>
    )
  }
);

export const CriticalVitals = createLazyComponent(
  () => import('../medical/CriticalVitals').catch(() => ({
    default: () => (
      <div className="p-8 bg-red-50 border border-red-200 rounded">
        <h2 className="text-red-800 font-bold">🩺 Critical Vitals Monitor</h2>
        <p className="text-red-700 mt-2">Vital signs monitoring system is loading...</p>
        <div className="mt-4 grid grid-cols-2 gap-4">
          <div className="p-3 bg-red-100 rounded">
            <p className="text-red-800 font-semibold">Normal Ranges:</p>
            <ul className="text-red-700 text-sm mt-1 space-y-1">
              <li>• Heart Rate: 60-100 bpm</li>
              <li>• Blood Pressure: &lt;120/80</li>
              <li>• Temperature: 97-99°F</li>
              <li>• Oxygen: &gt;95%</li>
            </ul>
          </div>
          <div className="p-3 bg-red-100 rounded">
            <p className="text-red-800 font-semibold">Emergency Signs:</p>
            <ul className="text-red-700 text-sm mt-1 space-y-1">
              <li>• Heart Rate: &lt;50 or &gt;120</li>
              <li>• BP: &gt;180/110</li>
              <li>• Temp: &gt;103°F</li>
              <li>• Oxygen: &lt;90%</li>
            </ul>
          </div>
        </div>
      </div>
    )
  })),
  { 
    priority: 'emergency',
    preload: true,
    errorFallback: ({ error, resetErrorBoundary }) => (
      <div className="p-8 bg-red-100 border border-red-300 rounded">
        <h2 className="text-red-800 font-bold">🩺 VITAL SIGNS MONITOR ERROR</h2>
        <p className="text-red-700 mt-2">Critical vitals monitoring failed to load.</p>
        <p className="text-red-600 text-sm mt-1">Error: {error.message}</p>
        <div className="mt-4 p-4 bg-red-200 rounded">
          <p className="text-red-800 font-bold">Monitor vitals manually:</p>
          <ul className="text-red-700 text-sm mt-1 space-y-1">
            <li>• Check pulse at wrist or neck</li>
            <li>• Monitor breathing rate</li>
            <li>• Check skin color and temperature</li>
            <li>• Watch for signs of distress</li>
          </ul>
        </div>
        <button 
          onClick={resetErrorBoundary}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry Vitals Monitor
        </button>
      </div>
    )
  }
);

// =============================================================================
// OFFLINE EMERGENCY COMPONENTS
// =============================================================================

export const OfflineEmergencyProtocols = createLazyComponent(
  () => import('../offline/OfflineEmergencyProtocols').catch(() => ({
    default: () => (
      <div className="p-8 bg-yellow-50 border border-yellow-200 rounded">
        <h2 className="text-yellow-800 font-bold">📱 Offline Emergency Protocols</h2>
        <p className="text-yellow-700 mt-2">Basic emergency guidance available offline</p>
        <div className="mt-4 space-y-4">
          <div className="p-3 bg-yellow-100 rounded">
            <h3 className="text-yellow-800 font-semibold">Cardiac Emergency:</h3>
            <p className="text-yellow-700 text-sm">Call 911, give aspirin if conscious, start CPR if unconscious</p>
          </div>
          <div className="p-3 bg-yellow-100 rounded">
            <h3 className="text-yellow-800 font-semibold">Breathing Emergency:</h3>
            <p className="text-yellow-700 text-sm">Call 911, help patient sit upright, check for obstructions</p>
          </div>
          <div className="p-3 bg-yellow-100 rounded">
            <h3 className="text-yellow-800 font-semibold">Severe Bleeding:</h3>
            <p className="text-yellow-700 text-sm">Apply direct pressure, elevate if possible, call 911</p>
          </div>
        </div>
      </div>
    )
  })),
  { 
    priority: 'emergency',
    preload: true
  }
);

// =============================================================================
// EMERGENCY ROUTE UTILITIES
// =============================================================================

/**
 * Preload all emergency routes immediately
 */
export function preloadEmergencyRoutes(): void {
  console.log('🚨 Preloading emergency routes...');
  
  // Trigger lazy loading for all emergency components
  const emergencyComponents = [
    EmergencyProtocols,
    EmergencyConsultation,
    CriticalVitals,
    OfflineEmergencyProtocols
  ];

  emergencyComponents.forEach((Component, index) => {
    try {
      // Create element to trigger lazy loading
      React.createElement(Component);
      console.log(`✅ Emergency component ${index + 1}/${emergencyComponents.length} preloaded`);
    } catch (error) {
      console.warn(`⚠️ Failed to preload emergency component ${index + 1}:`, error);
    }
  });
}

/**
 * Check if emergency routes are available
 */
export function checkEmergencyRoutesAvailability(): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      // Simple check to see if emergency components can be created
      React.createElement(EmergencyProtocols);
      React.createElement(EmergencyConsultation);
      React.createElement(CriticalVitals);
      resolve(true);
    } catch (error) {
      console.warn('Emergency routes availability check failed:', error);
      resolve(false);
    }
  });
}
