/**
 * RATE LIMIT CONFIGURATION AND TYPES
 * 
 * Extracted from rateLimitingService.ts for better maintainability.
 * Contains all configuration, types, and constants for rate limiting.
 */

import type { UserRole, Permission } from '../../types/auth';

export interface RateLimitConfig {
  readonly windowMs: number;
  readonly maxRequests: number;
  readonly skipSuccessfulRequests?: boolean;
  readonly skipFailedRequests?: boolean;
  readonly keyGenerator?: (req: any) => string;
  readonly onLimitReached?: (req: any, rateLimitInfo: RateLimitInfo) => void;
  readonly emergencyBypass?: EmergencyBypassConfig;
}

export interface EmergencyBypassConfig {
  readonly enabled: boolean;
  readonly emergencyRoles: UserRole[];
  readonly emergencyEndpoints: string[];
  readonly emergencyKeywords: string[];
  readonly maxEmergencyRequests: number;
  readonly emergencyWindowMs: number;
  readonly auditRequired: boolean;
}

export interface RateLimitInfo {
  readonly limit: number;
  readonly remaining: number;
  readonly resetTime: number;
  readonly retryAfter: number;
  readonly isEmergency?: boolean;
  readonly bypassReason?: string;
}

export interface SystemLoadMetrics {
  cpuUsage: number;
  memoryUsage: number;
  activeConnections: number;
  emergencyRequestsActive: number;
  lastUpdated: number;
}

export interface AdaptiveConfig {
  readonly enableAdaptive: boolean;
  readonly loadThresholds: {
    readonly low: number;
    readonly medium: number;
    readonly high: number;
    readonly critical: number;
  };
  readonly adaptiveMultipliers: {
    readonly low: number;
    readonly medium: number;
    readonly high: number;
    readonly critical: number;
  };
}

export interface RequestMetrics {
  count: number;
  windowStart: number;
  emergencyCount: number;
  lastRequest: number;
  userRole?: UserRole;
  isBlocked: boolean;
}

/**
 * Default rate limit configurations for different endpoint types
 */
export const defaultConfigs = {
  // Medical endpoints - balanced security and accessibility
  medical: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    emergencyBypass: {
      enabled: true,
      emergencyRoles: ['emergency_responder', 'admin'] as UserRole[],
      emergencyEndpoints: [
        '/api/emergency',
        '/api/consultation/emergency',
        '/api/agents/emergency',
        '/api/medical/emergency'
      ],
      emergencyKeywords: [
        'emergency', 'urgent', 'critical', 'life-threatening',
        'cardiac arrest', 'stroke', 'severe pain', 'difficulty breathing'
      ],
      maxEmergencyRequests: 1000,
      emergencyWindowMs: 60 * 1000,
      auditRequired: true
    }
  },

  // Authentication endpoints - stricter limits
  authentication: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10,
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
    emergencyBypass: {
      enabled: false,
      emergencyRoles: [] as UserRole[],
      emergencyEndpoints: [],
      emergencyKeywords: [],
      maxEmergencyRequests: 0,
      emergencyWindowMs: 0,
      auditRequired: true
    }
  },

  // General API endpoints
  general: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200,
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    emergencyBypass: {
      enabled: true,
      emergencyRoles: ['emergency_responder', 'admin'] as UserRole[],
      emergencyEndpoints: ['/api/emergency'],
      emergencyKeywords: ['emergency', 'urgent'],
      maxEmergencyRequests: 500,
      emergencyWindowMs: 60 * 1000,
      auditRequired: true
    }
  },

  // File upload endpoints
  upload: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 20,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    emergencyBypass: {
      enabled: true,
      emergencyRoles: ['emergency_responder', 'provider', 'admin'] as UserRole[],
      emergencyEndpoints: ['/api/upload/emergency'],
      emergencyKeywords: ['emergency'],
      maxEmergencyRequests: 50,
      emergencyWindowMs: 5 * 60 * 1000,
      auditRequired: true
    }
  }
};

/**
 * Default adaptive configuration
 */
export const defaultAdaptiveConfig: AdaptiveConfig = {
  enableAdaptive: true,
  loadThresholds: {
    low: 0.3,
    medium: 0.6,
    high: 0.8,
    critical: 0.95
  },
  adaptiveMultipliers: {
    low: 1.5,    // Increase limits when load is low
    medium: 1.0, // Normal limits
    high: 0.7,   // Reduce limits when load is high
    critical: 0.3 // Severely reduce limits when critical
  }
};

/**
 * Role-based rate limit multipliers
 */
export const roleMultipliers: Record<UserRole, number> = {
  admin: 5.0,
  emergency_responder: 3.0,
  healthcare_provider: 2.0,
  patient: 1.0
};

/**
 * Emergency bypass reasons
 */
export const bypassReasons = {
  EMERGENCY_ROLE: 'emergency_role',
  EMERGENCY_ENDPOINT: 'emergency_endpoint',
  EMERGENCY_KEYWORDS: 'emergency_keywords',
  EMERGENCY_HEADER: 'emergency_header',
  SYSTEM_OVERRIDE: 'system_override'
} as const;

export type BypassReason = typeof bypassReasons[keyof typeof bypassReasons];
