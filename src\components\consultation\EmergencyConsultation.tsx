/**
 * Emergency Consultation Component
 * Specialized consultation interface for emergency medical situations
 */

import React, { useState, useEffect, useRef } from 'react';

interface EmergencyConsultationProps {
  patientId?: string;
  emergencyType?: 'cardiac' | 'respiratory' | 'trauma' | 'neurological' | 'poisoning' | 'other';
  onEmergencyStop?: () => void;
  onConsultationComplete?: (summary: string) => void;
  autoRecord?: boolean;
}

interface ConsultationMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  urgency?: 'low' | 'medium' | 'high' | 'critical';
}

const EmergencyConsultation: React.FC<EmergencyConsultationProps> = ({
  patientId,
  emergencyType = 'other',
  onEmergencyStop,
  onConsultationComplete,
  autoRecord = true
}) => {
  const [messages, setMessages] = useState<ConsultationMessage[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [consultationActive, setConsultationActive] = useState(true);
  const [emergencyTimer, setEmergencyTimer] = useState(0);
  const [currentInput, setCurrentInput] = useState('');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Initialize emergency consultation
    const welcomeMessage: ConsultationMessage = {
      id: 'welcome',
      type: 'system',
      content: `Emergency consultation initiated for ${emergencyType} emergency. Please describe the situation immediately.`,
      timestamp: new Date(),
      urgency: 'critical'
    };
    
    setMessages([welcomeMessage]);

    // Start emergency timer
    timerRef.current = setInterval(() => {
      setEmergencyTimer(prev => prev + 1);
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [emergencyType]);

  useEffect(() => {
    // Auto-scroll to bottom
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleEmergencyStop = () => {
    setConsultationActive(false);
    setIsRecording(false);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    onEmergencyStop?.();
  };

  const handleStartRecording = () => {
    setIsRecording(true);
    // Mock recording start
    setTimeout(() => {
      setIsRecording(false);
      setIsProcessing(true);
      
      // Mock processing and response
      setTimeout(() => {
        const userMessage: ConsultationMessage = {
          id: `user-${Date.now()}`,
          type: 'user',
          content: currentInput || 'Patient experiencing chest pain and difficulty breathing',
          timestamp: new Date(),
          urgency: 'high'
        };

        const assistantResponse: ConsultationMessage = {
          id: `assistant-${Date.now()}`,
          type: 'assistant',
          content: getEmergencyResponse(emergencyType),
          timestamp: new Date(),
          urgency: 'critical'
        };

        setMessages(prev => [...prev, userMessage, assistantResponse]);
        setIsProcessing(false);
        setCurrentInput('');
      }, 2000);
    }, 3000);
  };

  const getEmergencyResponse = (type: string): string => {
    const responses = {
      cardiac: "IMMEDIATE ACTION REQUIRED: 1) Check pulse and breathing 2) If no pulse, begin CPR immediately 3) Call emergency services 4) Prepare AED if available 5) Continue CPR until help arrives. Do NOT leave patient unattended.",
      respiratory: "URGENT: 1) Position patient upright 2) Ensure airway is clear 3) Administer oxygen if available 4) Monitor breathing rate 5) Prepare for emergency transport. If breathing stops, begin rescue breathing immediately.",
      trauma: "CRITICAL: 1) Do NOT move patient unless in immediate danger 2) Control any visible bleeding with direct pressure 3) Check for consciousness 4) Monitor vital signs 5) Keep patient warm and calm. Call emergency services immediately.",
      neurological: "EMERGENCY: 1) Note time of symptom onset 2) Check responsiveness and pupil reaction 3) Position patient safely 4) Do NOT give food or water 5) Monitor breathing and pulse. Time is critical - transport immediately.",
      poisoning: "POISON EMERGENCY: 1) Identify the substance if possible 2) Do NOT induce vomiting unless instructed 3) Call Poison Control immediately 4) Remove patient from source 5) Monitor breathing and consciousness.",
      other: "EMERGENCY PROTOCOL: 1) Assess patient responsiveness 2) Check vital signs 3) Ensure scene safety 4) Provide appropriate first aid 5) Prepare for emergency transport. Stay calm and follow basic life support protocols."
    };
    
    return responses[type as keyof typeof responses] || responses.other;
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getUrgencyColor = (urgency?: string) => {
    switch (urgency) {
      case 'critical': return 'border-l-red-500 bg-red-50';
      case 'high': return 'border-l-orange-500 bg-orange-50';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50';
      case 'low': return 'border-l-blue-500 bg-blue-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  return (
    <div className="emergency-consultation h-full flex flex-col bg-white">
      {/* Emergency Header */}
      <div className="bg-red-600 text-white p-4 flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-white rounded-full animate-pulse mr-3"></div>
          <div>
            <h1 className="text-xl font-bold">EMERGENCY CONSULTATION</h1>
            <p className="text-sm opacity-90">Type: {emergencyType.toUpperCase()}</p>
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-2xl font-mono font-bold">{formatTime(emergencyTimer)}</div>
          <div className="text-xs opacity-90">Emergency Timer</div>
        </div>
      </div>

      {/* Emergency Stop Button */}
      <div className="bg-red-100 border-b border-red-200 p-3 text-center">
        <button
          onClick={handleEmergencyStop}
          className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-6 rounded-full transition-colors"
        >
          🛑 EMERGENCY STOP
        </button>
        <p className="text-xs text-red-600 mt-1">
          Press to immediately stop consultation and alert emergency services
        </p>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`message border-l-4 p-3 rounded-r-lg ${getUrgencyColor(message.urgency)} ${
              message.type === 'user' ? 'ml-8' : message.type === 'system' ? 'mx-4' : 'mr-8'
            }`}
          >
            <div className="flex items-center justify-between mb-1">
              <span className="font-semibold text-sm">
                {message.type === 'user' ? 'Healthcare Provider' : 
                 message.type === 'assistant' ? 'Emergency AI Assistant' : 'System'}
              </span>
              <span className="text-xs text-gray-500">
                {message.timestamp.toLocaleTimeString()}
              </span>
            </div>
            <p className="text-gray-800">{message.content}</p>
            {message.urgency && (
              <span className={`inline-block mt-2 px-2 py-1 rounded text-xs font-medium ${
                message.urgency === 'critical' ? 'bg-red-200 text-red-800' :
                message.urgency === 'high' ? 'bg-orange-200 text-orange-800' :
                message.urgency === 'medium' ? 'bg-yellow-200 text-yellow-800' :
                'bg-blue-200 text-blue-800'
              }`}>
                {message.urgency.toUpperCase()} PRIORITY
              </span>
            )}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      {consultationActive && (
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex items-center space-x-3">
            <textarea
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              placeholder="Describe the emergency situation in detail..."
              className="flex-1 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
              rows={2}
              disabled={isRecording || isProcessing}
            />
            
            <div className="flex flex-col space-y-2">
              <button
                onClick={handleStartRecording}
                disabled={isRecording || isProcessing || !consultationActive}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  isRecording 
                    ? 'bg-red-600 text-white animate-pulse' 
                    : isProcessing
                    ? 'bg-yellow-500 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {isRecording ? '🎤 Recording...' : 
                 isProcessing ? '⏳ Processing...' : 
                 '🎤 Record'}
              </button>
              
              <button
                onClick={() => {
                  if (currentInput.trim()) {
                    handleStartRecording();
                  }
                }}
                disabled={!currentInput.trim() || isRecording || isProcessing}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
              >
                Send
              </button>
            </div>
          </div>
          
          <div className="mt-2 text-xs text-gray-600 text-center">
            Emergency consultation is being recorded for medical documentation
          </div>
        </div>
      )}

      {!consultationActive && (
        <div className="border-t border-gray-200 p-4 bg-red-50 text-center">
          <p className="text-red-700 font-medium">Emergency consultation stopped</p>
          <p className="text-sm text-red-600 mt-1">
            Total duration: {formatTime(emergencyTimer)}
          </p>
        </div>
      )}
    </div>
  );
};

export default EmergencyConsultation;
