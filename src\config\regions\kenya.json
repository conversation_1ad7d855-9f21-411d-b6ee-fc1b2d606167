{"country": {"code": "KE", "name": "Kenya", "region": "East Africa", "currency": "KES", "languages": ["en", "sw", "ki", "lu", "ka"], "timezone": "EAT"}, "healthcare": {"system": "Universal Health Coverage (UHC)", "emergencyNumber": "999", "commonConditions": ["Malaria", "HIV/AIDS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hypertension", "Diabetes", "Respiratory infections", "Diarrheal diseases", "Malnutrition"], "endemicDiseases": ["Rift Valley fever", "Dengue fever", "Chikungunya", "Leishmaniasis", "Trypanosomiasis", "Schistosomiasis"], "seasonalPatterns": {"rainySeasonDiseases": ["Malaria", "Cholera", "Rift Valley fever"], "drySeasonDiseases": ["Meningitis", "Respiratory infections"], "yearRound": ["HIV/AIDS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hypertension"]}}, "cultural": {"healthBeliefs": {"traditionalMedicine": "widely_accepted", "spiritualHealing": "common", "familyInvolvement": "very_high", "genderConsiderations": "high"}, "communicationStyle": {"directness": "moderate", "respectForElders": "very_high", "hierarchical": "high", "groupDecisionMaking": "very_common"}, "dietaryConsiderations": ["Maize-based diet (ugali)", "High vegetable consumption", "Limited meat consumption", "Tea culture prevalent", "Seasonal fruit availability"]}, "regulations": {"dataProtection": {"law": "Data Protection Act 2019", "requirements": ["explicit_consent", "data_minimization", "security", "breach_notification"], "crossBorderTransfer": "adequacy_required"}, "medicalDevice": {"authority": "Pharmacy and Poisons Board (PPB)", "classification": "risk_based", "requirements": ["registration", "quality_management", "post_market_surveillance"]}, "telemedicine": {"status": "regulated", "requirements": ["medical_license", "patient_consent", "data_security"], "restrictions": ["controlled_substances", "surgery_consultation"]}}, "infrastructure": {"internetPenetration": 0.87, "mobileSubscription": 1.09, "electricityAccess": 0.75, "urbanization": 0.28, "literacyRate": 0.82}, "localization": {"dateFormat": "dd/mm/yyyy", "timeFormat": "12h", "numberFormat": "1,234.56", "measurementSystem": "metric", "preferredLanguages": ["sw", "en", "ki"]}, "emergency": {"protocols": {"responseTime": "< 20 minutes urban, < 45 minutes rural", "escalationLevels": ["dispensary", "health_center", "sub_county_hospital", "county_hospital", "national_referral"], "culturalConsiderations": ["clan_elder_consultation", "traditional_healer_involvement", "community_support"]}, "contacts": {"ambulance": "999", "police": "999", "fire": "999", "redCross": "1199"}}}