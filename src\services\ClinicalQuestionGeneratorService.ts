/**
 * Clinical Question Generator Service
 * 
 * Provides intelligent, protocol-based question generation for medical consultations
 * with urgency-based prioritization and clinical decision support.
 */

import { SOAPAssessment } from './DiagnosticFrameworkService';

export interface ClinicalQuestion {
  id: string;
  text: string;
  category: QuestionCategory;
  priority: QuestionPriority;
  urgencyLevel: UrgencyLevel;
  clinicalRationale: string;
  expectedAnswerType: AnswerType;
  followUpQuestions?: string[];
  redFlags?: string[];
  skipConditions?: string[];
}

export type QuestionCategory = 
  | 'chief_complaint' 
  | 'history_present_illness' 
  | 'review_systems' 
  | 'past_medical_history'
  | 'medications' 
  | 'allergies' 
  | 'social_history' 
  | 'family_history'
  | 'physical_exam' 
  | 'vital_signs' 
  | 'diagnostic_reasoning' 
  | 'treatment_planning';

export type QuestionPriority = 'critical' | 'high' | 'medium' | 'low' | 'optional';
export type UrgencyLevel = 'immediate' | 'urgent' | 'routine' | 'follow_up';
export type AnswerType = 'yes_no' | 'scale' | 'descriptive' | 'multiple_choice' | 'numeric';

export interface QuestionGenerationContext {
  chiefComplaint: string;
  currentPhase: string;
  urgencyLevel: UrgencyLevel | string;
  patientAge?: number;
  patientGender?: string;
  knownConditions?: string[];
  currentMedications?: string[];
  conversationHistory: string[];
  timeConstraints?: number; // minutes available
}

export interface QuestionSet {
  primaryQuestions: ClinicalQuestion[];
  followUpQuestions: ClinicalQuestion[];
  urgentQuestions: ClinicalQuestion[];
  estimatedTime: number; // minutes to complete
  clinicalGuidance: string;
}

class ClinicalQuestionGeneratorService {
  
  // Clinical protocols for different chief complaints
  private readonly CLINICAL_PROTOCOLS = {
    'chest_pain': {
      critical: [
        'Are you experiencing chest pain right now?',
        'Is the pain crushing, squeezing, or pressure-like?',
        'Does the pain radiate to your arm, jaw, or back?',
        'Are you having trouble breathing?',
        'Do you feel nauseous or are you sweating?'
      ],
      high: [
        'When did the chest pain start?',
        'What were you doing when it started?',
        'On a scale of 1-10, how severe is the pain?',
        'Have you taken any medications for this pain?'
      ]
    },
    'shortness_of_breath': {
      critical: [
        'Are you having difficulty breathing right now?',
        'Can you speak in full sentences?',
        'Do you have chest pain with the breathing difficulty?',
        'Are your lips or fingernails blue?'
      ],
      high: [
        'When did the breathing difficulty start?',
        'Is it worse when lying down or with activity?',
        'Do you have a cough or wheezing?'
      ]
    },
    'abdominal_pain': {
      critical: [
        'Where exactly is the pain located?',
        'On a scale of 1-10, how severe is the pain?',
        'Is the pain constant or does it come and go?',
        'Have you vomited or had any bleeding?'
      ],
      high: [
        'When did the pain start?',
        'What makes the pain better or worse?',
        'Have you had a fever?',
        'When was your last bowel movement?'
      ]
    }
  };

  // OLDCARTS framework questions
  private readonly OLDCARTS_QUESTIONS = {
    onset: [
      'When did this symptom first start?',
      'Did it start suddenly or gradually?',
      'What were you doing when it started?'
    ],
    location: [
      'Where exactly do you feel this symptom?',
      'Can you point to the specific area?',
      'Does it stay in one place or move around?'
    ],
    duration: [
      'How long does this symptom last when it occurs?',
      'Is it constant or does it come and go?',
      'How often does it happen?'
    ],
    character: [
      'How would you describe this symptom?',
      'What does it feel like? (sharp, dull, burning, aching)',
      'Is there anything unique about how it feels?'
    ],
    aggravating: [
      'What makes this symptom worse?',
      'Does movement, eating, or breathing affect it?',
      'Are there specific triggers you\'ve noticed?'
    ],
    relieving: [
      'What makes this symptom better?',
      'Have you tried any treatments or medications?',
      'Does rest or position change help?'
    ],
    timing: [
      'Is there a pattern to when this occurs?',
      'Is it worse at certain times of day?',
      'How has it changed since it started?'
    ],
    severity: [
      'On a scale of 1-10, how severe is this symptom?',
      'How does this compare to other pain you\'ve experienced?',
      'Is it interfering with your daily activities?'
    ]
  };

  // Review of systems questions by body system
  private readonly REVIEW_OF_SYSTEMS = {
    constitutional: [
      'Have you had any fever, chills, or night sweats?',
      'Have you noticed any weight loss or gain?',
      'How has your energy level been?'
    ],
    cardiovascular: [
      'Have you had any chest pain or palpitations?',
      'Do you get short of breath with activity?',
      'Have you noticed any swelling in your legs?'
    ],
    respiratory: [
      'Do you have a cough or shortness of breath?',
      'Have you coughed up any blood or unusual sputum?',
      'Do you wheeze or have trouble breathing at night?'
    ],
    gastrointestinal: [
      'Have you had any nausea, vomiting, or diarrhea?',
      'Any changes in your appetite or bowel habits?',
      'Have you noticed any blood in your stool?'
    ],
    neurological: [
      'Have you had any headaches, dizziness, or weakness?',
      'Any numbness, tingling, or vision changes?',
      'Have you had any seizures or loss of consciousness?'
    ]
  };

  /**
   * Generate questions based on clinical context
   */
  generateQuestions(context: QuestionGenerationContext): QuestionSet {
    const questions: ClinicalQuestion[] = [];
    
    // Determine clinical protocol based on chief complaint
    const protocol = this.identifyProtocol(context.chiefComplaint);
    
    // Generate protocol-specific questions
    if (protocol) {
      questions.push(...this.generateProtocolQuestions(protocol, context));
    }
    
    // Generate OLDCARTS questions for symptom exploration
    questions.push(...this.generateOLDCARTSQuestions(context));
    
    // Generate review of systems questions
    questions.push(...this.generateReviewOfSystemsQuestions(context));
    
    // Prioritize questions based on urgency and context
    const prioritizedQuestions = this.prioritizeQuestions(questions, context);
    
    return {
      primaryQuestions: prioritizedQuestions.slice(0, 5),
      followUpQuestions: prioritizedQuestions.slice(5, 10),
      urgentQuestions: prioritizedQuestions.filter(q => q.priority === 'critical'),
      estimatedTime: this.estimateQuestionTime(prioritizedQuestions),
      clinicalGuidance: this.generateClinicalGuidance(context, prioritizedQuestions)
    };
  }

  /**
   * Generate urgency-specific questions
   */
  generateUrgencyBasedQuestions(
    urgencyLevel: UrgencyLevel, 
    context: QuestionGenerationContext
  ): ClinicalQuestion[] {
    switch (urgencyLevel) {
      case 'immediate':
        return this.generateImmediateQuestions(context);
      case 'urgent':
        return this.generateUrgentQuestions(context);
      case 'routine':
        return this.generateRoutineQuestions(context);
      case 'follow_up':
        return this.generateFollowUpQuestions(context);
      default:
        return [];
    }
  }

  /**
   * Generate immediate/critical questions
   */
  private generateImmediateQuestions(context: QuestionGenerationContext): ClinicalQuestion[] {
    return [
      {
        id: 'immediate_1',
        text: 'Are you experiencing this symptom right now?',
        category: 'chief_complaint',
        priority: 'critical',
        urgencyLevel: 'immediate',
        clinicalRationale: 'Assess current symptom status for immediate intervention',
        expectedAnswerType: 'yes_no'
      },
      {
        id: 'immediate_2',
        text: 'On a scale of 1-10, how severe is your symptom right now?',
        category: 'chief_complaint',
        priority: 'critical',
        urgencyLevel: 'immediate',
        clinicalRationale: 'Quantify severity for triage decisions',
        expectedAnswerType: 'scale'
      },
      {
        id: 'immediate_3',
        text: 'Do you feel you need emergency medical care right now?',
        category: 'chief_complaint',
        priority: 'critical',
        urgencyLevel: 'immediate',
        clinicalRationale: 'Patient self-assessment of urgency',
        expectedAnswerType: 'yes_no'
      }
    ];
  }

  /**
   * Generate questions for specific clinical protocols
   */
  private generateProtocolQuestions(protocol: string, context: QuestionGenerationContext): ClinicalQuestion[] {
    const protocolQuestions = this.CLINICAL_PROTOCOLS[protocol];
    if (!protocolQuestions) return [];

    const questions: ClinicalQuestion[] = [];
    
    // Add critical questions first
    protocolQuestions.critical?.forEach((text, index) => {
      questions.push({
        id: `${protocol}_critical_${index}`,
        text,
        category: 'chief_complaint',
        priority: 'critical',
        urgencyLevel: (context.urgencyLevel as UrgencyLevel) || 'routine',
        clinicalRationale: `Critical assessment for ${protocol}`,
        expectedAnswerType: 'descriptive'
      });
    });

    // Add high priority questions
    protocolQuestions.high?.forEach((text, index) => {
      questions.push({
        id: `${protocol}_high_${index}`,
        text,
        category: 'history_present_illness',
        priority: 'high',
        urgencyLevel: (context.urgencyLevel as UrgencyLevel) || 'routine',
        clinicalRationale: `Important history for ${protocol}`,
        expectedAnswerType: 'descriptive'
      });
    });

    return questions;
  }

  /**
   * Generate OLDCARTS-based questions
   */
  private generateOLDCARTSQuestions(context: QuestionGenerationContext): ClinicalQuestion[] {
    const questions: ClinicalQuestion[] = [];
    
    Object.entries(this.OLDCARTS_QUESTIONS).forEach(([category, questionTexts]) => {
      questionTexts.forEach((text, index) => {
        questions.push({
          id: `oldcarts_${category}_${index}`,
          text,
          category: 'history_present_illness',
          priority: category === 'severity' ? 'high' : 'medium',
          urgencyLevel: 'routine',
          clinicalRationale: `OLDCARTS ${category} assessment`,
          expectedAnswerType: category === 'severity' ? 'scale' : 'descriptive'
        });
      });
    });

    return questions;
  }

  /**
   * Generate review of systems questions
   */
  private generateReviewOfSystemsQuestions(context: QuestionGenerationContext): ClinicalQuestion[] {
    const questions: ClinicalQuestion[] = [];
    
    // Select relevant systems based on chief complaint
    const relevantSystems = this.selectRelevantSystems(context.chiefComplaint);
    
    relevantSystems.forEach(system => {
      const systemQuestions = this.REVIEW_OF_SYSTEMS[system] || [];
      systemQuestions.forEach((text, index) => {
        questions.push({
          id: `ros_${system}_${index}`,
          text,
          category: 'review_systems',
          priority: 'medium',
          urgencyLevel: 'routine',
          clinicalRationale: `Review of ${system} system`,
          expectedAnswerType: 'yes_no'
        });
      });
    });

    return questions;
  }

  /**
   * Identify clinical protocol based on chief complaint
   */
  private identifyProtocol(chiefComplaint: string): string | null {
    const complaint = chiefComplaint.toLowerCase();
    
    if (complaint.includes('chest pain') || complaint.includes('chest pressure')) {
      return 'chest_pain';
    }
    if (complaint.includes('shortness of breath') || complaint.includes('difficulty breathing')) {
      return 'shortness_of_breath';
    }
    if (complaint.includes('abdominal pain') || complaint.includes('stomach pain')) {
      return 'abdominal_pain';
    }
    
    return null;
  }

  /**
   * Select relevant body systems for review based on chief complaint
   */
  private selectRelevantSystems(chiefComplaint: string): string[] {
    const complaint = chiefComplaint.toLowerCase();
    const systems: string[] = ['constitutional']; // Always include constitutional
    
    if (complaint.includes('chest') || complaint.includes('heart')) {
      systems.push('cardiovascular', 'respiratory');
    }
    if (complaint.includes('breath') || complaint.includes('cough')) {
      systems.push('respiratory', 'cardiovascular');
    }
    if (complaint.includes('stomach') || complaint.includes('abdominal')) {
      systems.push('gastrointestinal');
    }
    if (complaint.includes('headache') || complaint.includes('dizziness')) {
      systems.push('neurological');
    }
    
    return systems;
  }

  /**
   * Prioritize questions based on context
   */
  private prioritizeQuestions(questions: ClinicalQuestion[], context: QuestionGenerationContext): ClinicalQuestion[] {
    return questions.sort((a, b) => {
      // Priority order: critical > high > medium > low > optional
      const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3, optional: 4 };
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      
      if (priorityDiff !== 0) return priorityDiff;
      
      // If same priority, sort by urgency level
      const urgencyOrder = { immediate: 0, urgent: 1, routine: 2, follow_up: 3 };
      return urgencyOrder[a.urgencyLevel] - urgencyOrder[b.urgencyLevel];
    });
  }

  /**
   * Estimate time needed for questions
   */
  private estimateQuestionTime(questions: ClinicalQuestion[]): number {
    // Estimate 1-2 minutes per question depending on complexity
    return questions.reduce((total, question) => {
      switch (question.expectedAnswerType) {
        case 'yes_no': return total + 0.5;
        case 'scale': return total + 1;
        case 'multiple_choice': return total + 1;
        case 'numeric': return total + 1;
        case 'descriptive': return total + 2;
        default: return total + 1.5;
      }
    }, 0);
  }

  /**
   * Generate clinical guidance for question set
   */
  private generateClinicalGuidance(context: QuestionGenerationContext, questions: ClinicalQuestion[]): string {
    const criticalCount = questions.filter(q => q.priority === 'critical').length;
    const urgentCount = questions.filter(q => q.urgencyLevel === 'immediate' || q.urgencyLevel === 'urgent').length;
    
    if (criticalCount > 0) {
      return `Focus on ${criticalCount} critical questions first. Consider immediate intervention if red flags present.`;
    }
    if (urgentCount > 0) {
      return `Address ${urgentCount} urgent questions promptly. Monitor for symptom progression.`;
    }
    return 'Proceed with systematic history taking. No immediate concerns identified.';
  }

  // Additional helper methods for other urgency levels
  private generateUrgentQuestions(context: QuestionGenerationContext): ClinicalQuestion[] { return []; }
  private generateRoutineQuestions(context: QuestionGenerationContext): ClinicalQuestion[] { return []; }
  private generateFollowUpQuestions(context: QuestionGenerationContext): ClinicalQuestion[] { return []; }
}

export const clinicalQuestionGeneratorService = new ClinicalQuestionGeneratorService();
export default clinicalQuestionGeneratorService;
