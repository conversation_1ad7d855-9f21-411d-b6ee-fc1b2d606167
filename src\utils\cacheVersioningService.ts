/**
 * CACHE VERSIONING AND MIGRATION SERVICE
 * 
 * This service provides cache versioning and migration capabilities with:
 * - Medical data integrity preservation during migrations
 * - Emergency data protection and rollback mechanisms
 * - HIPAA-compliant data transformation and validation
 * - Automatic migration planning and execution
 * - Rollback capabilities for failed migrations
 * - Audit logging for all migration operations
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Emergency medical data must never be lost during migrations
 * - Data integrity must be verified before and after migrations
 * - All migration operations must be auditable
 * - Rollback mechanisms must preserve patient safety
 * - Migration failures must not compromise system availability
 */

import type {
  CacheVersionInfo,
  CacheMigrationPlan,
  CacheMigrationStep,
  CacheEntry,
  MedicalDataPriority
} from '../types/cache';

import auditLogger from './auditLogger';
import encryptionService from './encryptionService';
import intelligentCacheManager from './intelligentCacheManager';

interface MigrationContext {
  readonly fromVersion: string;
  readonly toVersion: string;
  readonly startTime: number;
  readonly emergencyDataCount: number;
  readonly medicalDataCount: number;
  readonly totalEntries: number;
  readonly backupCreated: boolean;
  readonly dryRun: boolean;
}

interface MigrationResult {
  readonly success: boolean;
  readonly migratedEntries: number;
  readonly failedEntries: number;
  readonly emergencyDataPreserved: number;
  readonly duration: number;
  readonly errors: string[];
  readonly rollbackRequired: boolean;
  readonly dataIntegrityVerified: boolean;
}

interface DataIntegrityCheck {
  readonly checkId: string;
  readonly description: string;
  readonly validator: (entry: CacheEntry) => Promise<boolean>;
  readonly critical: boolean;
  readonly emergencyDataOnly: boolean;
}

class CacheVersioningService {
  private readonly currentVersion: string;
  private readonly migrationHistory: Map<string, MigrationResult>;
  private readonly integrityChecks: Map<string, DataIntegrityCheck>;
  private readonly emergencyBackup: Map<string, CacheEntry>;
  private migrationInProgress: boolean;

  constructor() {
    this.currentVersion = this.generateVersion();
    this.migrationHistory = new Map();
    this.integrityChecks = new Map();
    this.emergencyBackup = new Map();
    this.migrationInProgress = false;

    this.initializeIntegrityChecks();
  }

  /**
   * Get current cache version
   */
  getCurrentVersion(): string {
    return this.currentVersion;
  }

  /**
   * Check if migration is needed
   */
  async checkMigrationNeeded(targetVersion: string): Promise<{
    needed: boolean;
    fromVersion: string;
    toVersion: string;
    migrationPlan?: CacheMigrationPlan;
    emergencyDataAffected: boolean;
  }> {
    const fromVersion = await this.detectCurrentCacheVersion();
    
    if (fromVersion === targetVersion) {
      return {
        needed: false,
        fromVersion,
        toVersion: targetVersion,
        emergencyDataAffected: false
      };
    }

    const migrationPlan = await this.createMigrationPlan(fromVersion, targetVersion);
    const emergencyDataAffected = await this.checkEmergencyDataAffected(migrationPlan);

    return {
      needed: true,
      fromVersion,
      toVersion: targetVersion,
      migrationPlan,
      emergencyDataAffected
    };
  }

  /**
   * Execute cache migration with safety checks
   */
  async executeMigration(
    migrationPlan: CacheMigrationPlan,
    options: {
      dryRun?: boolean;
      createBackup?: boolean;
      skipNonCritical?: boolean;
    } = {}
  ): Promise<MigrationResult> {
    if (this.migrationInProgress) {
      throw new Error('Migration already in progress');
    }

    const { dryRun = false, createBackup = true, skipNonCritical = false } = options;
    
    this.migrationInProgress = true;
    const startTime = performance.now();
    
    try {
      // Create migration context
      const context = await this.createMigrationContext(migrationPlan, dryRun);
      
      // Create emergency backup
      if (createBackup) {
        await this.createEmergencyBackup();
      }

      // Pre-migration integrity checks
      const preCheckResult = await this.runIntegrityChecks('pre-migration');
      if (!preCheckResult.passed) {
        throw new Error(`Pre-migration integrity checks failed: ${preCheckResult.errors.join(', ')}`);
      }

      // Execute migration steps
      const migrationResult = await this.executeMigrationSteps(
        migrationPlan.migrationSteps,
        context,
        skipNonCritical
      );

      // Post-migration integrity checks
      const postCheckResult = await this.runIntegrityChecks('post-migration');
      if (!postCheckResult.passed) {
        // Rollback if integrity checks fail
        await this.rollbackMigration(migrationPlan);
        throw new Error(`Post-migration integrity checks failed: ${postCheckResult.errors.join(', ')}`);
      }

      const duration = performance.now() - startTime;

      // Log successful migration
      await auditLogger.logSecurityEvent(
        'cache_migration_completed',
        'medium',
        {
          from_version: migrationPlan.fromVersion,
          to_version: migrationPlan.toVersion,
          migrated_entries: migrationResult.migratedEntries,
          duration,
          dry_run: dryRun,
          emergency_data_preserved: migrationResult.emergencyDataPreserved
        }
      );

      // Store migration history
      this.migrationHistory.set(migrationPlan.toVersion, migrationResult);

      return {
        ...migrationResult,
        duration,
        dataIntegrityVerified: true
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown migration error';
      const duration = performance.now() - startTime;

      await auditLogger.logSecurityEvent(
        'cache_migration_failed',
        'high',
        {
          from_version: migrationPlan.fromVersion,
          to_version: migrationPlan.toVersion,
          error: errorMessage,
          duration,
          dry_run: dryRun
        }
      );

      return {
        success: false,
        migratedEntries: 0,
        failedEntries: 0,
        emergencyDataPreserved: this.emergencyBackup.size,
        duration,
        errors: [errorMessage],
        rollbackRequired: true,
        dataIntegrityVerified: false
      };

    } finally {
      this.migrationInProgress = false;
    }
  }

  /**
   * Rollback migration to previous version
   */
  async rollbackMigration(migrationPlan: CacheMigrationPlan): Promise<MigrationResult> {
    const startTime = performance.now();
    
    try {
      // Execute rollback steps
      const rollbackResult = await this.executeMigrationSteps(
        migrationPlan.rollbackPlan,
        await this.createMigrationContext(migrationPlan, false),
        false
      );

      // Restore emergency backup if available
      if (this.emergencyBackup.size > 0) {
        await this.restoreEmergencyBackup();
      }

      // Verify rollback integrity
      const integrityResult = await this.runIntegrityChecks('post-rollback');
      
      const duration = performance.now() - startTime;

      await auditLogger.logSecurityEvent(
        'cache_migration_rollback',
        'high',
        {
          from_version: migrationPlan.toVersion,
          to_version: migrationPlan.fromVersion,
          rollback_entries: rollbackResult.migratedEntries,
          duration,
          integrity_verified: integrityResult.passed
        }
      );

      return {
        ...rollbackResult,
        duration,
        dataIntegrityVerified: integrityResult.passed
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown rollback error';
      
      await auditLogger.logSecurityEvent(
        'cache_migration_rollback_failed',
        'critical',
        {
          error: errorMessage,
          emergency_backup_available: this.emergencyBackup.size > 0
        }
      );

      throw new Error(`Rollback failed: ${errorMessage}`);
    }
  }

  /**
   * Create migration plan between versions
   */
  private async createMigrationPlan(fromVersion: string, toVersion: string): Promise<CacheMigrationPlan> {
    const migrationSteps: CacheMigrationStep[] = [];
    const rollbackSteps: CacheMigrationStep[] = [];

    // Determine migration steps based on version differences
    const versionDiff = this.compareVersions(fromVersion, toVersion);
    
    if (versionDiff.major > 0) {
      // Major version change - comprehensive migration
      migrationSteps.push(
        {
          stepId: 'backup-emergency-data',
          description: 'Backup all emergency medical data',
          operation: 'move',
          targetKeys: ['emergency:*'],
          rollbackOperation: 'restore-emergency-backup'
        },
        {
          stepId: 'transform-medical-data',
          description: 'Transform medical data to new format',
          operation: 'transform',
          targetKeys: ['medical:*'],
          transformFunction: 'transformMedicalDataV2',
          rollbackOperation: 'transform-medical-data-v1'
        },
        {
          stepId: 'validate-data-integrity',
          description: 'Validate all medical data integrity',
          operation: 'validate',
          validationRules: ['medical-data-integrity', 'emergency-data-completeness'],
          rollbackOperation: 'skip'
        }
      );
    } else if (versionDiff.minor > 0) {
      // Minor version change - selective migration
      migrationSteps.push(
        {
          stepId: 'update-cache-metadata',
          description: 'Update cache entry metadata',
          operation: 'transform',
          transformFunction: 'updateCacheMetadata',
          rollbackOperation: 'revert-cache-metadata'
        }
      );
    }

    // Create corresponding rollback steps
    rollbackSteps.push(...migrationSteps.map(step => ({
      ...step,
      stepId: `rollback-${step.stepId}`,
      description: `Rollback: ${step.description}`,
      operation: step.rollbackOperation as any || 'delete',
      rollbackOperation: step.operation
    })).reverse());

    return {
      fromVersion,
      toVersion,
      migrationSteps,
      rollbackPlan: rollbackSteps,
      dataIntegrityChecks: ['medical-data-integrity', 'emergency-data-completeness', 'encryption-integrity'],
      emergencyDataHandling: 'preserve'
    };
  }

  /**
   * Execute individual migration steps
   */
  private async executeMigrationSteps(
    steps: CacheMigrationStep[],
    context: MigrationContext,
    skipNonCritical: boolean
  ): Promise<MigrationResult> {
    let migratedEntries = 0;
    let failedEntries = 0;
    let emergencyDataPreserved = 0;
    const errors: string[] = [];

    for (const step of steps) {
      try {
        const stepResult = await this.executeStep(step, context, skipNonCritical);
        migratedEntries += stepResult.processed;
        emergencyDataPreserved += stepResult.emergencyPreserved;
        
        if (stepResult.errors.length > 0) {
          errors.push(...stepResult.errors);
          failedEntries += stepResult.failed;
        }

        await auditLogger.logSecurityEvent(
          'migration_step_completed',
          'low',
          {
            step_id: step.stepId,
            operation: step.operation,
            processed: stepResult.processed,
            failed: stepResult.failed,
            emergency_preserved: stepResult.emergencyPreserved
          }
        );

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown step error';
        errors.push(`Step ${step.stepId}: ${errorMessage}`);
        failedEntries++;

        await auditLogger.logSecurityEvent(
          'migration_step_failed',
          'medium',
          {
            step_id: step.stepId,
            error: errorMessage
          }
        );
      }
    }

    return {
      success: errors.length === 0,
      migratedEntries,
      failedEntries,
      emergencyDataPreserved,
      duration: 0, // Will be set by caller
      errors,
      rollbackRequired: failedEntries > 0,
      dataIntegrityVerified: false // Will be checked by caller
    };
  }

  /**
   * Execute individual migration step
   */
  private async executeStep(
    step: CacheMigrationStep,
    context: MigrationContext,
    skipNonCritical: boolean
  ): Promise<{
    processed: number;
    failed: number;
    emergencyPreserved: number;
    errors: string[];
  }> {
    // Implementation would depend on the specific step operation
    // This is a simplified version
    
    switch (step.operation) {
      case 'transform':
        return await this.executeTransformStep(step, context);
      case 'move':
        return await this.executeMoveStep(step, context);
      case 'delete':
        return await this.executeDeleteStep(step, context, skipNonCritical);
      case 'validate':
        return await this.executeValidateStep(step, context);
      default:
        throw new Error(`Unknown migration operation: ${step.operation}`);
    }
  }

  /**
   * Initialize data integrity checks
   */
  private initializeIntegrityChecks(): void {
    this.integrityChecks.set('medical-data-integrity', {
      checkId: 'medical-data-integrity',
      description: 'Verify medical data structure and completeness',
      validator: async (entry: CacheEntry) => {
        if (!entry.dataType?.includes('medical')) return true;
        
        // Check if medical data has required fields
        const data = entry.data;
        return data && typeof data === 'object' && 
               data.id && data.timestamp && data.patientId;
      },
      critical: true,
      emergencyDataOnly: false
    });

    this.integrityChecks.set('emergency-data-completeness', {
      checkId: 'emergency-data-completeness',
      description: 'Verify emergency data is complete and accessible',
      validator: async (entry: CacheEntry) => {
        if (!entry.isEmergencyData) return true;
        
        // Emergency data must be unencrypted and immediately accessible
        return entry.data !== null && entry.data !== undefined;
      },
      critical: true,
      emergencyDataOnly: true
    });

    this.integrityChecks.set('encryption-integrity', {
      checkId: 'encryption-integrity',
      description: 'Verify encrypted data can be decrypted',
      validator: async (entry: CacheEntry) => {
        if (!entry.encrypted) return true;
        
        // Try to decrypt the data
        try {
          const sessionToken = this.getSessionToken();
          if (!sessionToken) return false;
          
          const result = await encryptionService.decryptMedicalData(entry.data, sessionToken);
          return result !== null && result !== undefined;
        } catch {
          return false;
        }
      },
      critical: true,
      emergencyDataOnly: false
    });
  }

  // Helper methods (simplified implementations)
  private async detectCurrentCacheVersion(): Promise<string> {
    // Detect version from cache metadata
    return 'v1.0.0'; // Placeholder
  }

  private compareVersions(v1: string, v2: string): { major: number; minor: number; patch: number } {
    // Simple version comparison
    return { major: 1, minor: 0, patch: 0 }; // Placeholder
  }

  private async createMigrationContext(plan: CacheMigrationPlan, dryRun: boolean): Promise<MigrationContext> {
    return {
      fromVersion: plan.fromVersion,
      toVersion: plan.toVersion,
      startTime: Date.now(),
      emergencyDataCount: 0,
      medicalDataCount: 0,
      totalEntries: 0,
      backupCreated: false,
      dryRun
    };
  }

  private async checkEmergencyDataAffected(plan: CacheMigrationPlan): Promise<boolean> {
    return plan.emergencyDataHandling !== 'preserve';
  }

  private async createEmergencyBackup(): Promise<void> {
    // Create backup of emergency data
    this.emergencyBackup.clear();
    // Implementation would backup emergency data
  }

  private async restoreEmergencyBackup(): Promise<void> {
    // Restore emergency data from backup
    for (const [key, entry] of this.emergencyBackup.entries()) {
      await intelligentCacheManager.set(key, entry.data, {
        priority: entry.priority,
        isEmergencyData: entry.isEmergencyData
      });
    }
  }

  private async runIntegrityChecks(phase: string): Promise<{ passed: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    for (const check of this.integrityChecks.values()) {
      try {
        // Run check against cache entries
        // This is a simplified implementation
        const passed = true; // Placeholder
        
        if (!passed) {
          errors.push(`Integrity check failed: ${check.description}`);
        }
      } catch (error) {
        errors.push(`Integrity check error: ${check.checkId} - ${error}`);
      }
    }

    return { passed: errors.length === 0, errors };
  }

  private async executeTransformStep(step: CacheMigrationStep, context: MigrationContext) {
    // Placeholder implementation
    return { processed: 0, failed: 0, emergencyPreserved: 0, errors: [] };
  }

  private async executeMoveStep(step: CacheMigrationStep, context: MigrationContext) {
    // Placeholder implementation
    return { processed: 0, failed: 0, emergencyPreserved: 0, errors: [] };
  }

  private async executeDeleteStep(step: CacheMigrationStep, context: MigrationContext, skipNonCritical: boolean) {
    // Placeholder implementation
    return { processed: 0, failed: 0, emergencyPreserved: 0, errors: [] };
  }

  private async executeValidateStep(step: CacheMigrationStep, context: MigrationContext) {
    // Placeholder implementation
    return { processed: 0, failed: 0, emergencyPreserved: 0, errors: [] };
  }

  private generateVersion(): string {
    return `v${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private getSessionToken(): string | null {
    return localStorage.getItem('session_token');
  }
}

const cacheVersioningService = new CacheVersioningService();
export default cacheVersioningService;
export { CacheVersioningService };
