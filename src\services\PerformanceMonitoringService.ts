/**
 * PERFORMANCE MONITORING SERVICE
 * 
 * Comprehensive performance monitoring and metrics collection for all
 * newly added features including real-time dashboards and alerting.
 * 
 * FEATURES:
 * - Real-time performance metrics collection
 * - Feature-specific monitoring (circuit breakers, memory cleanup, etc.)
 * - Performance alerting and thresholds
 * - Historical performance tracking
 * - Dashboard data aggregation
 * - Emergency performance protocols
 */

import auditLogger from '../utils/auditLogger';

export interface PerformanceMetric {
  id: string;
  feature: string;
  operation: string;
  duration: number;
  timestamp: string;
  success: boolean;
  metadata?: Record<string, any>;
  sessionId?: string;
  userId?: string;
}

export interface PerformanceThreshold {
  feature: string;
  operation: string;
  warningThresholdMs: number;
  criticalThresholdMs: number;
  emergencyThresholdMs: number;
  enabled: boolean;
}

export interface PerformanceAlert {
  id: string;
  level: 'warning' | 'critical' | 'emergency';
  feature: string;
  operation: string;
  currentValue: number;
  threshold: number;
  timestamp: string;
  resolved: boolean;
  metadata?: Record<string, any>;
}

export interface PerformanceSummary {
  feature: string;
  totalOperations: number;
  averageDuration: number;
  successRate: number;
  p95Duration: number;
  p99Duration: number;
  alertCount: number;
  lastUpdated: string;
}

export class PerformanceMonitoringService {
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private thresholds: Map<string, PerformanceThreshold> = new Map();
  private alerts: Map<string, PerformanceAlert> = new Map();
  private alertCallbacks: ((alert: PerformanceAlert) => void)[] = [];
  private isMonitoring = true;

  constructor() {
    console.log('📊 Initializing Performance Monitoring Service...');
    this.initializeDefaultThresholds();
    this.startPerformanceAggregation();
  }

  /**
   * Initialize default performance thresholds
   */
  private initializeDefaultThresholds(): void {
    const defaultThresholds: PerformanceThreshold[] = [
      // Agent Orchestrator thresholds
      {
        feature: 'agent_orchestrator',
        operation: 'process_request',
        warningThresholdMs: 1000,
        criticalThresholdMs: 1800,
        emergencyThresholdMs: 2000,
        enabled: true
      },
      {
        feature: 'agent_orchestrator',
        operation: 'steering_guidance',
        warningThresholdMs: 200,
        criticalThresholdMs: 500,
        emergencyThresholdMs: 1000,
        enabled: true
      },

      // Circuit Breaker thresholds
      {
        feature: 'circuit_breaker',
        operation: 'execute',
        warningThresholdMs: 500,
        criticalThresholdMs: 1000,
        emergencyThresholdMs: 2000,
        enabled: true
      },

      // Memory Cleanup thresholds
      {
        feature: 'memory_cleanup',
        operation: 'cleanup_session',
        warningThresholdMs: 1000,
        criticalThresholdMs: 3000,
        emergencyThresholdMs: 5000,
        enabled: true
      },

      // Real-time Communication thresholds
      {
        feature: 'real_time_communication',
        operation: 'goal_progress_update',
        warningThresholdMs: 100,
        criticalThresholdMs: 300,
        emergencyThresholdMs: 500,
        enabled: true
      },

      // Context Truncation thresholds
      {
        feature: 'context_truncation',
        operation: 'truncate_context',
        warningThresholdMs: 200,
        criticalThresholdMs: 500,
        emergencyThresholdMs: 1000,
        enabled: true
      },

      // Error Sanitization thresholds
      {
        feature: 'error_sanitization',
        operation: 'sanitize_error',
        warningThresholdMs: 50,
        criticalThresholdMs: 100,
        emergencyThresholdMs: 200,
        enabled: true
      },

      // Goal Tracking thresholds
      {
        feature: 'goal_tracking',
        operation: 'track_progress',
        warningThresholdMs: 300,
        criticalThresholdMs: 800,
        emergencyThresholdMs: 1500,
        enabled: true
      }
    ];

    defaultThresholds.forEach(threshold => {
      const key = `${threshold.feature}_${threshold.operation}`;
      this.thresholds.set(key, threshold);
    });

    console.log(`✅ Initialized ${defaultThresholds.length} performance thresholds`);
  }

  /**
   * Record a performance metric
   */
  public recordMetric(
    feature: string,
    operation: string,
    duration: number,
    success: boolean = true,
    metadata?: Record<string, any>,
    sessionId?: string,
    userId?: string
  ): void {
    if (!this.isMonitoring) return;

    const metric: PerformanceMetric = {
      id: `${feature}_${operation}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      feature,
      operation,
      duration,
      timestamp: new Date().toISOString(),
      success,
      metadata,
      sessionId,
      userId
    };

    // Store metric
    const key = `${feature}_${operation}`;
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    
    const metrics = this.metrics.get(key)!;
    metrics.push(metric);

    // Keep only recent metrics (last 1000 per operation)
    if (metrics.length > 1000) {
      metrics.splice(0, metrics.length - 1000);
    }

    // Check thresholds and generate alerts
    this.checkThresholds(metric);

    // Log performance metric for audit
    this.logPerformanceMetric(metric);
  }

  /**
   * Measure and record performance of an operation
   */
  public async measurePerformance<T>(
    feature: string,
    operation: string,
    operationFn: () => Promise<T>,
    metadata?: Record<string, any>,
    sessionId?: string,
    userId?: string
  ): Promise<T> {
    const startTime = Date.now();
    let success = true;
    let result: T;

    try {
      result = await operationFn();
      return result;
    } catch (error) {
      success = false;
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      this.recordMetric(feature, operation, duration, success, metadata, sessionId, userId);
    }
  }

  /**
   * Measure synchronous operation performance
   */
  public measureSync<T>(
    feature: string,
    operation: string,
    operationFn: () => T,
    metadata?: Record<string, any>,
    sessionId?: string,
    userId?: string
  ): T {
    const startTime = Date.now();
    let success = true;
    let result: T;

    try {
      result = operationFn();
      return result;
    } catch (error) {
      success = false;
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      this.recordMetric(feature, operation, duration, success, metadata, sessionId, userId);
    }
  }

  /**
   * Check performance thresholds and generate alerts
   */
  private checkThresholds(metric: PerformanceMetric): void {
    const key = `${metric.feature}_${metric.operation}`;
    const threshold = this.thresholds.get(key);

    if (!threshold || !threshold.enabled) return;

    let alertLevel: 'warning' | 'critical' | 'emergency' | null = null;
    let thresholdValue = 0;

    if (metric.duration >= threshold.emergencyThresholdMs) {
      alertLevel = 'emergency';
      thresholdValue = threshold.emergencyThresholdMs;
    } else if (metric.duration >= threshold.criticalThresholdMs) {
      alertLevel = 'critical';
      thresholdValue = threshold.criticalThresholdMs;
    } else if (metric.duration >= threshold.warningThresholdMs) {
      alertLevel = 'warning';
      thresholdValue = threshold.warningThresholdMs;
    }

    if (alertLevel) {
      this.generateAlert(alertLevel, metric, thresholdValue);
    }
  }

  /**
   * Generate performance alert
   */
  private generateAlert(
    level: 'warning' | 'critical' | 'emergency',
    metric: PerformanceMetric,
    threshold: number
  ): void {
    const alert: PerformanceAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      level,
      feature: metric.feature,
      operation: metric.operation,
      currentValue: metric.duration,
      threshold,
      timestamp: new Date().toISOString(),
      resolved: false,
      metadata: {
        metricId: metric.id,
        sessionId: metric.sessionId,
        userId: metric.userId,
        ...metric.metadata
      }
    };

    this.alerts.set(alert.id, alert);

    // Log alert
    console.warn(`⚠️ Performance alert [${level.toUpperCase()}]: ${metric.feature}.${metric.operation} took ${metric.duration}ms (threshold: ${threshold}ms)`);

    // Notify alert callbacks
    this.alertCallbacks.forEach(callback => {
      try {
        callback(alert);
      } catch (error) {
        console.error('❌ Error in performance alert callback:', error);
      }
    });

    // Handle emergency alerts
    if (level === 'emergency') {
      this.handleEmergencyAlert(alert);
    }
  }

  /**
   * Handle emergency performance alerts
   */
  private async handleEmergencyAlert(alert: PerformanceAlert): Promise<void> {
    console.error(`🚨 EMERGENCY PERFORMANCE ALERT: ${alert.feature}.${alert.operation} exceeded ${alert.threshold}ms (actual: ${alert.currentValue}ms)`);

    try {
      // Log emergency alert for audit
      await auditLogger.logEmergencyEvent({
        type: 'performance_emergency',
        feature: alert.feature,
        operation: alert.operation,
        duration: alert.currentValue,
        threshold: alert.threshold,
        alertId: alert.id,
        timestamp: alert.timestamp
      });

      // Could trigger additional emergency protocols here
      // such as scaling resources, alerting administrators, etc.

    } catch (error) {
      console.error('❌ Failed to handle emergency performance alert:', error);
    }
  }

  /**
   * Get performance summary for a feature
   */
  public getPerformanceSummary(feature: string): PerformanceSummary[] {
    const summaries: PerformanceSummary[] = [];

    for (const [key, metrics] of this.metrics.entries()) {
      if (key.startsWith(feature)) {
        const [featureName, operation] = key.split('_', 2);
        
        if (metrics.length === 0) continue;

        const durations = metrics.map(m => m.duration).sort((a, b) => a - b);
        const successfulMetrics = metrics.filter(m => m.success);
        const alertsForOperation = Array.from(this.alerts.values())
          .filter(a => a.feature === featureName && a.operation === operation && !a.resolved);

        const summary: PerformanceSummary = {
          feature: featureName,
          totalOperations: metrics.length,
          averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
          successRate: successfulMetrics.length / metrics.length,
          p95Duration: durations[Math.floor(durations.length * 0.95)] || 0,
          p99Duration: durations[Math.floor(durations.length * 0.99)] || 0,
          alertCount: alertsForOperation.length,
          lastUpdated: new Date().toISOString()
        };

        summaries.push(summary);
      }
    }

    return summaries;
  }

  /**
   * Get all performance summaries
   */
  public getAllPerformanceSummaries(): PerformanceSummary[] {
    const features = new Set<string>();
    
    for (const key of this.metrics.keys()) {
      const feature = key.split('_')[0];
      features.add(feature);
    }

    const allSummaries: PerformanceSummary[] = [];
    
    for (const feature of features) {
      allSummaries.push(...this.getPerformanceSummary(feature));
    }

    return allSummaries;
  }

  /**
   * Get active alerts
   */
  public getActiveAlerts(): PerformanceAlert[] {
    return Array.from(this.alerts.values()).filter(alert => !alert.resolved);
  }

  /**
   * Resolve an alert
   */
  public resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId);
    if (alert) {
      alert.resolved = true;
      console.log(`✅ Performance alert resolved: ${alertId}`);
      return true;
    }
    return false;
  }

  /**
   * Register alert callback
   */
  public onAlert(callback: (alert: PerformanceAlert) => void): void {
    this.alertCallbacks.push(callback);
  }

  /**
   * Update performance threshold
   */
  public updateThreshold(
    feature: string,
    operation: string,
    threshold: Partial<PerformanceThreshold>
  ): void {
    const key = `${feature}_${operation}`;
    const existing = this.thresholds.get(key);
    
    if (existing) {
      this.thresholds.set(key, { ...existing, ...threshold });
      console.log(`📊 Updated performance threshold for ${feature}.${operation}`);
    }
  }

  /**
   * Start performance aggregation
   */
  private startPerformanceAggregation(): void {
    // Aggregate and clean up old metrics every 5 minutes
    setInterval(() => {
      this.aggregateMetrics();
      this.cleanupOldData();
    }, 5 * 60 * 1000);

    console.log('📊 Performance aggregation started');
  }

  /**
   * Aggregate metrics for reporting
   */
  private aggregateMetrics(): void {
    // This could implement more sophisticated aggregation
    // For now, just log summary statistics
    const summaries = this.getAllPerformanceSummaries();
    console.log(`📊 Performance summary: ${summaries.length} features monitored`);
  }

  /**
   * Clean up old performance data
   */
  private cleanupOldData(): void {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago

    // Clean up old alerts
    for (const [alertId, alert] of this.alerts.entries()) {
      if (alert.resolved && new Date(alert.timestamp).getTime() < cutoffTime) {
        this.alerts.delete(alertId);
      }
    }

    console.log('🧹 Performance monitoring data cleanup completed');
  }

  /**
   * Log performance metric for audit
   */
  private async logPerformanceMetric(metric: PerformanceMetric): Promise<void> {
    try {
      // Only log metrics that exceed warning thresholds to reduce noise
      const key = `${metric.feature}_${metric.operation}`;
      const threshold = this.thresholds.get(key);
      
      if (threshold && metric.duration >= threshold.warningThresholdMs) {
        await auditLogger.logPerformanceMetric({
          feature: metric.feature,
          operation: metric.operation,
          duration: metric.duration,
          success: metric.success,
          sessionId: metric.sessionId,
          userId: metric.userId,
          timestamp: metric.timestamp
        });
      }
    } catch (error) {
      console.error('❌ Failed to log performance metric:', error);
    }
  }

  /**
   * Enable/disable monitoring
   */
  public setMonitoring(enabled: boolean): void {
    this.isMonitoring = enabled;
    console.log(`📊 Performance monitoring ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get monitoring status
   */
  public isMonitoringEnabled(): boolean {
    return this.isMonitoring;
  }
}

// Export singleton instance
export const performanceMonitoringService = new PerformanceMonitoringService();
export default performanceMonitoringService;
