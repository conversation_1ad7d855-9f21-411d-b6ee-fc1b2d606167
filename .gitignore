# Dependencies
node_modules/
package-lock.json
yarn.lock
pnpm-lock.yaml

# Environment variables (CRITICAL - DO NOT COMMIT)
.env
.env.local
.env.production
.env.development
.env.staging
*.env

# Build outputs
/build
/dist
/out

# IDE and editor files
.vscode/
.idea/
.DS_Store
*.swp
*.swo
*~

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Temporary folders
tmp/
temp/

# OS generated files
Thumbs.db
ehthumbs.db

# Medical/Healthcare specific (HIPAA Compliance)
patient-data/
medical-records/
phi-data/
*.phi
*.medical

# API keys and secrets (CRITICAL)
secrets/
keys/
*.key
*.pem
*.p12
*.pfx

# Supabase
.supabase/

# Vite
vite.config.*s.*
