/**
 * COMPREHENSIVE CRYPTO API INTEGRATION TESTS
 *
 * Tests for AES-256-GCM encryption functionality, medical data security,
 * and emergency protocol compatibility with < 2 second response times.
 */

// Vitest globals are available via vitest.d.ts
import encryptionService from '../utils/encryptionService';

// Mock crypto API with all required methods
const mockCrypto = {
  getRandomValues: vi.fn((arr) => {
    for (let i = 0; i < arr.length; i++) {
      arr[i] = Math.floor(Math.random() * 256);
    }
    return arr;
  }),
  subtle: {
    digest: vi.fn(),
    importKey: vi.fn(),
    deriveKey: vi.fn(),
    deriveBits: vi.fn(),
    encrypt: vi.fn(),
    decrypt: vi.fn()
  }
};

// Mock medical data samples
const mockMedicalData = {
  patientInfo: {
    id: 'patient-12345',
    name: '<PERSON>',
    age: 45,
    medicalHistory: ['hypertension', 'diabetes'],
    currentSymptoms: 'chest pain, shortness of breath'
  },
  transcriptionData: {
    text: 'Patient reports chest pain and difficulty breathing',
    confidence: 0.95,
    duration: 30.5,
    language: 'en',
    timestamp: '2024-01-01T12:00:00.000Z'
  },
  audioMetadata: {
    duration: 30.5,
    sampleRate: 44100,
    channels: 1,
    format: 'webm',
    size: 245760
  }
};

describe('Crypto API Integration Tests', () => {
  let mockKey: CryptoKey;
  let mockEncryptedData: ArrayBuffer;

  beforeEach(() => {
    // Setup crypto mocking using proper technique
    vi.stubGlobal('crypto', mockCrypto);

    // Setup mock responses
    mockKey = { type: 'secret', algorithm: { name: 'AES-GCM' } } as CryptoKey;
    mockEncryptedData = new ArrayBuffer(64);

    mockCrypto.subtle.importKey.mockResolvedValue(mockKey);
    mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);
    mockCrypto.subtle.deriveBits.mockResolvedValue(new ArrayBuffer(32));
    mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedData);
    mockCrypto.subtle.decrypt.mockResolvedValue(new ArrayBuffer(32));
    mockCrypto.subtle.digest.mockResolvedValue(new ArrayBuffer(32));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('AES-256-GCM Encryption Functionality', () => {
    it('should encrypt medical data with AES-256-GCM', async () => {
      const sessionToken = 'test-session-token-12345';
      
      const result = await encryptionService.encryptMedicalData(
        mockMedicalData.patientInfo, 
        sessionToken
      );

      expect(result.encrypted).toBe(true);
      expect(result.timestamp).toBeDefined();

      // Type guard to ensure we have encrypted data
      if (result.encrypted) {
        expect(result.algorithm).toBe('AES-GCM');
        expect(result.keyLength).toBe(256);
        expect(result.data).toBeDefined();
        expect(result.iv).toBeDefined();
        expect(result.salt).toBeDefined();
      }
      
      // Verify crypto API calls
      expect(mockCrypto.subtle.deriveKey).toHaveBeenCalled();
      expect(mockCrypto.subtle.encrypt).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'AES-GCM',
          iv: expect.any(Uint8Array),
          tagLength: 128
        }),
        mockKey,
        expect.any(Uint8Array)
      );
    });

    it('should decrypt medical data successfully', async () => {
      const sessionToken = 'test-session-token-12345';
      
      // First encrypt the data
      const encryptedResult = await encryptionService.encryptMedicalData(
        mockMedicalData.transcriptionData, 
        sessionToken
      );

      // Mock decrypt to return original data
      const originalData = JSON.stringify(mockMedicalData.transcriptionData);
      mockCrypto.subtle.decrypt.mockResolvedValue(
        new TextEncoder().encode(originalData).buffer
      );

      // Then decrypt it
      const decryptedData = await encryptionService.decryptMedicalData(
        encryptedResult, 
        sessionToken
      );

      expect(decryptedData).toEqual(mockMedicalData.transcriptionData);
      expect(mockCrypto.subtle.decrypt).toHaveBeenCalled();
    });

    it('should handle encryption roundtrip correctly', async () => {
      const sessionToken = 'test-session-token-12345';
      const originalData = mockMedicalData.audioMetadata;
      
      // Mock the full roundtrip
      const originalDataString = JSON.stringify(originalData);
      mockCrypto.subtle.decrypt.mockResolvedValue(
        new TextEncoder().encode(originalDataString).buffer
      );

      // Encrypt then decrypt
      const encrypted = await encryptionService.encryptMedicalData(originalData, sessionToken);
      const decrypted = await encryptionService.decryptMedicalData(encrypted, sessionToken);

      expect(decrypted).toEqual(originalData);
    });
  });

  describe('Emergency Protocol Compatibility', () => {
    it('should maintain < 2 second response time for emergency encryption', async () => {
      const startTime = performance.now();
      const emergencySessionToken = 'EMERGENCY_SESSION_TOKEN';
      
      await encryptionService.encryptMedicalData(
        mockMedicalData.patientInfo, 
        emergencySessionToken
      );
      
      const responseTime = performance.now() - startTime;
      expect(responseTime).toBeLessThan(2000); // < 2 seconds
    });

    it('should handle emergency decryption within time limits', async () => {
      const emergencySessionToken = 'EMERGENCY_SESSION_TOKEN';
      
      // Setup encrypted data
      const encryptedData = await encryptionService.encryptMedicalData(
        mockMedicalData.transcriptionData, 
        emergencySessionToken
      );

      // Mock decrypt response
      mockCrypto.subtle.decrypt.mockResolvedValue(
        new TextEncoder().encode(JSON.stringify(mockMedicalData.transcriptionData)).buffer
      );

      const startTime = performance.now();
      await encryptionService.decryptMedicalData(encryptedData, emergencySessionToken);
      const responseTime = performance.now() - startTime;
      
      expect(responseTime).toBeLessThan(2000); // < 2 seconds
    });

    it('should gracefully handle crypto API unavailability in emergencies', async () => {
      // Mock crypto API as unavailable
      vi.stubGlobal('crypto', undefined);

      expect(encryptionService.isAvailable()).toBe(false);
      
      // Should not throw error, just return unencrypted data
      const result = await encryptionService.encryptMedicalData(
        mockMedicalData.patientInfo,
        'emergency-token'
      );
      
      expect(result.encrypted).toBe(false);

      // Type guard for unencrypted data with error
      if (!result.encrypted && 'error' in result) {
        expect(result.error).toContain('encryption not available');
      }
    });
  });

  describe('Security Validation', () => {
    it('should generate unique IVs for each encryption', async () => {
      const sessionToken = 'test-session-token';
      
      const result1 = await encryptionService.encryptMedicalData(
        mockMedicalData.patientInfo, 
        sessionToken
      );
      
      const result2 = await encryptionService.encryptMedicalData(
        mockMedicalData.patientInfo, 
        sessionToken
      );

      // Type guards to ensure we have encrypted data
      if (result1.encrypted && result2.encrypted) {
        expect(result1.iv).not.toBe(result2.iv);
        expect(result1.salt).not.toBe(result2.salt);
      }
    });

    it('should use proper key derivation with PBKDF2', async () => {
      const sessionToken = 'test-session-token';
      
      await encryptionService.encryptMedicalData(
        mockMedicalData.transcriptionData, 
        sessionToken
      );

      expect(mockCrypto.subtle.deriveKey).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'PBKDF2',
          salt: expect.any(Uint8Array),
          iterations: 100000,
          hash: 'SHA-256'
        }),
        expect.any(Object),
        expect.objectContaining({
          name: 'AES-GCM',
          length: 256
        }),
        false,
        ['encrypt', 'decrypt']
      );
    });

    it('should validate data integrity with authentication tags', async () => {
      const sessionToken = 'test-session-token';
      
      await encryptionService.encryptMedicalData(
        mockMedicalData.audioMetadata, 
        sessionToken
      );

      expect(mockCrypto.subtle.encrypt).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'AES-GCM',
          tagLength: 128 // 128-bit authentication tag
        }),
        expect.any(Object),
        expect.any(Uint8Array)
      );
    });
  });

  describe('Error Handling and Fallbacks', () => {
    it('should handle encryption failures gracefully', async () => {
      mockCrypto.subtle.encrypt.mockRejectedValue(new Error('Encryption failed'));
      
      const result = await encryptionService.encryptMedicalData(
        mockMedicalData.patientInfo, 
        'test-token'
      );

      expect(result.encrypted).toBe(false);

      // Type guard for unencrypted data with error
      if (!result.encrypted && 'error' in result) {
        expect(result.error).toContain('Failed to encrypt medical data');
      }
    });

    it('should handle decryption failures gracefully', async () => {
      const encryptedData = {
        encrypted: true as const,
        data: 'invalid-encrypted-data',
        iv: 'test-iv',
        salt: 'test-salt',
        algorithm: 'AES-GCM' as const,
        keyLength: 256 as const,
        timestamp: Date.now(),
        dataType: 'Object'
      };

      mockCrypto.subtle.decrypt.mockRejectedValue(new Error('Decryption failed'));
      
      await expect(
        encryptionService.decryptMedicalData(encryptedData, 'test-token')
      ).rejects.toThrow('Failed to decrypt medical data');
    });

    it('should validate input data before encryption', async () => {
      await expect(
        encryptionService.encryptMedicalData(null as any, 'test-token')
      ).rejects.toThrow('Data is required for encryption');

      await expect(
        encryptionService.encryptMedicalData({}, '')
      ).rejects.toThrow('Session token is required for encryption');
    });
  });
});
