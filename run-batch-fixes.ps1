# Master PowerShell Script: Run All TypeScript Error Batch Fixes
# Target: ~400 out of 773 errors (52% reduction)
# Strategy: Run safest scripts first, validate after each step

param(
    [switch]$DryRun,
    [switch]$SkipBackup
)

Write-Host "🚀 VoiceHealth AI TypeScript Batch Error Fix Script" -ForegroundColor Magenta
Write-Host "Target: ~400 errors out of 773 total errors" -ForegroundColor White
Write-Host "Strategy: Incremental fixes with validation" -ForegroundColor White

if ($DryRun) {
    Write-Host "⚠️  DRY RUN MODE - No files will be modified" -ForegroundColor Yellow
}

# Step 0: Create backup (unless skipped)
if (-not $SkipBackup -and -not $DryRun) {
    Write-Host "`n📦 Creating Git Backup..." -ForegroundColor Cyan
    try {
        git add -A
        git commit -m "Backup before batch TypeScript error fixes - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
        Write-Host "✅ Backup created successfully" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Git backup failed, but continuing..." -ForegroundColor Yellow
    }
}

# Function to run build and count errors
function Test-BuildErrors {
    Write-Host "`n🔍 Running TypeScript build check..." -ForegroundColor Cyan
    try {
        $buildOutput = npm run build 2>&1
        $errorLines = $buildOutput | Where-Object { $_ -match "error TS\d+" }
        $errorCount = $errorLines.Count
        
        if ($errorCount -eq 0) {
            Write-Host "✅ Build successful - No TypeScript errors!" -ForegroundColor Green
            return 0
        } else {
            Write-Host "📊 Build completed with $errorCount TypeScript errors" -ForegroundColor Yellow
            return $errorCount
        }
    } catch {
        Write-Host "❌ Build check failed" -ForegroundColor Red
        return -1
    }
}

# Initial error count
Write-Host "`n📊 Initial Error Assessment..." -ForegroundColor Magenta
$initialErrors = 773  # Known from previous build
Write-Host "Starting with: $initialErrors TypeScript errors" -ForegroundColor White

$currentErrors = $initialErrors
$totalFixed = 0

# Step 1: Fix Regional Configuration Errors (Safest, High Impact)
Write-Host "`n🎯 Step 1: Regional Configuration Property Access Fixes" -ForegroundColor Magenta
Write-Host "Target: 21 errors in regional configuration tests" -ForegroundColor White

if (-not $DryRun) {
    & .\fix-regional-config-errors.ps1
    $newErrors = Test-BuildErrors
    if ($newErrors -ge 0 -and $newErrors -lt $currentErrors) {
        $fixed = $currentErrors - $newErrors
        $totalFixed += $fixed
        $currentErrors = $newErrors
        Write-Host "✅ Step 1 Success: Fixed $fixed errors" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Step 1: No significant error reduction detected" -ForegroundColor Yellow
    }
} else {
    Write-Host "🔍 DRY RUN: Would run fix-regional-config-errors.ps1" -ForegroundColor Cyan
}

# Step 2: Fix exactOptionalPropertyTypes Violations (High Impact)
Write-Host "`n🎯 Step 2: exactOptionalPropertyTypes Fixes" -ForegroundColor Magenta
Write-Host "Target: ~100-150 errors across multiple files" -ForegroundColor White

if (-not $DryRun) {
    & .\fix-optional-property-types.ps1
    $newErrors = Test-BuildErrors
    if ($newErrors -ge 0 -and $newErrors -lt $currentErrors) {
        $fixed = $currentErrors - $newErrors
        $totalFixed += $fixed
        $currentErrors = $newErrors
        Write-Host "✅ Step 2 Success: Fixed $fixed errors" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Step 2: No significant error reduction detected" -ForegroundColor Yellow
    }
} else {
    Write-Host "🔍 DRY RUN: Would run fix-optional-property-types.ps1" -ForegroundColor Cyan
}

# Step 3: Fix Readonly Property Assignments (Medium Impact)
Write-Host "`n🎯 Step 3: Readonly Property Assignment Fixes" -ForegroundColor Magenta
Write-Host "Target: ~50-80 errors in utility files" -ForegroundColor White

if (-not $DryRun) {
    & .\fix-readonly-assignments.ps1
    $newErrors = Test-BuildErrors
    if ($newErrors -ge 0 -and $newErrors -lt $currentErrors) {
        $fixed = $currentErrors - $newErrors
        $totalFixed += $fixed
        $currentErrors = $newErrors
        Write-Host "✅ Step 3 Success: Fixed $fixed errors" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Step 3: No significant error reduction detected" -ForegroundColor Yellow
    }
} else {
    Write-Host "🔍 DRY RUN: Would run fix-readonly-assignments.ps1" -ForegroundColor Cyan
}

# Final Summary
Write-Host "`n📊 BATCH FIX SUMMARY" -ForegroundColor Magenta
Write-Host "===================" -ForegroundColor Magenta
Write-Host "Initial errors: $initialErrors" -ForegroundColor White
Write-Host "Current errors: $currentErrors" -ForegroundColor White
Write-Host "Total fixed: $totalFixed" -ForegroundColor Green
Write-Host "Success rate: $([Math]::Round(($totalFixed / $initialErrors) * 100, 1))%" -ForegroundColor Green

if ($totalFixed -gt 100) {
    Write-Host "`n🎉 EXCELLENT RESULTS!" -ForegroundColor Green
    Write-Host "Batch fixes achieved significant error reduction" -ForegroundColor Green
} elseif ($totalFixed -gt 50) {
    Write-Host "`n✅ GOOD RESULTS!" -ForegroundColor Yellow
    Write-Host "Batch fixes made meaningful progress" -ForegroundColor Yellow
} else {
    Write-Host "`n⚠️  LIMITED RESULTS" -ForegroundColor Red
    Write-Host "May need manual intervention for remaining errors" -ForegroundColor Red
}

# Next Steps
Write-Host "`n🔍 NEXT STEPS:" -ForegroundColor Magenta
if ($currentErrors -gt 0) {
    Write-Host "1. Review remaining $currentErrors errors manually" -ForegroundColor White
    Write-Host "2. Apply modular refactoring to high-error files" -ForegroundColor White
    Write-Host "3. Focus on files with 10+ errors for maximum impact" -ForegroundColor White
    Write-Host "4. Update typescript-errors-fix-plan.md with progress" -ForegroundColor White
} else {
    Write-Host "1. Run full test suite to verify functionality" -ForegroundColor White
    Write-Host "2. Test development server startup" -ForegroundColor White
    Write-Host "3. Commit successful fixes" -ForegroundColor White
    Write-Host "4. Update documentation" -ForegroundColor White
}

Write-Host "`n🚀 Batch TypeScript Error Fix Script Completed!" -ForegroundColor Magenta

# Return exit code based on success
if ($totalFixed -gt 50) {
    exit 0  # Success
} else {
    exit 1  # Limited success
}
