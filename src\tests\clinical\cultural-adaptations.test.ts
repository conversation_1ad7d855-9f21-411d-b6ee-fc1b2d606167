/**
 * CULTURAL ADAPTATIONS TESTS
 * 
 * Focused tests for cultural adaptation functionality in clinical documentation
 * including language preferences, family involvement, and traditional medicine integration.
 */

// Vitest globals are available via vitest.config.js globals: true
import { clinicalDocumentationService } from '../../services/ClinicalDocumentationService';

describe('Cultural Adaptations', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    clinicalDocumentationService.clearCaches();
  });

  describe('generateCulturalAdaptations', () => {
    it('should generate appropriate cultural adaptations', async () => {
      const noteContent = {
        chiefComplaint: 'Patient needs medication',
        assessment: {
          primaryDiagnosis: { condition: 'hypertension' }
        }
      };

      const culturalContext = {
        cultureCode: 'akan',
        familyInvolvementLevel: 'high',
        traditionalMedicineOpenness: 4,
        languagePreference: 'tw'
      };

      const result = await clinicalDocumentationService.generateCulturalAdaptations(
        noteContent,
        culturalContext
      );

      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
      
      const languageAdaptation = result.find(
        adaptation => adaptation.aspect === 'language'
      );
      expect(languageAdaptation).toBeDefined();
      
      const familyAdaptation = result.find(
        adaptation => adaptation.aspect === 'family_involvement'
      );
      expect(familyAdaptation).toBeDefined();
    });

    it('should handle missing cultural context', async () => {
      const noteContent = {
        chiefComplaint: 'Patient needs medication'
      };

      const result = await clinicalDocumentationService.generateCulturalAdaptations(
        noteContent,
        undefined
      );

      expect(result).toBeDefined();
      expect(result.length).toBe(0);
    });

    it('should adapt for high family involvement cultures', async () => {
      const noteContent = {
        chiefComplaint: 'Patient requires surgery',
        plan: {
          procedures: [
            {
              procedure: 'appendectomy',
              urgency: 'urgent'
            }
          ]
        }
      };

      const culturalContext = {
        cultureCode: 'yoruba',
        familyInvolvementLevel: 'high',
        decisionMakingStyle: 'collective'
      };

      const result = await clinicalDocumentationService.generateCulturalAdaptations(
        noteContent,
        culturalContext
      );

      const familyAdaptation = result.find(
        adaptation => adaptation.aspect === 'family_involvement'
      );
      
      expect(familyAdaptation).toBeDefined();
      expect(familyAdaptation.adaptation).toContain('family');
      expect(familyAdaptation.adaptation).toContain('decision');
    });

    it('should integrate traditional medicine considerations', async () => {
      const noteContent = {
        chiefComplaint: 'Patient using traditional herbs',
        medications: [
          {
            name: 'Traditional herbal remedy',
            type: 'traditional'
          }
        ]
      };

      const culturalContext = {
        cultureCode: 'akan',
        traditionalMedicineOpenness: 5,
        traditionalMedicineUse: 'regular'
      };

      const result = await clinicalDocumentationService.generateCulturalAdaptations(
        noteContent,
        culturalContext
      );

      const traditionalMedicineAdaptation = result.find(
        adaptation => adaptation.aspect === 'traditional_medicine'
      );
      
      expect(traditionalMedicineAdaptation).toBeDefined();
      expect(traditionalMedicineAdaptation.adaptation).toContain('traditional');
    });

    it('should provide language-specific adaptations', async () => {
      const noteContent = {
        chiefComplaint: 'Patient needs medication instructions',
        plan: {
          patientEducation: [
            {
              topic: 'medication_compliance',
              content: 'Take medication as prescribed'
            }
          ]
        }
      };

      const culturalContext = {
        cultureCode: 'hausa',
        languagePreference: 'ha',
        literacyLevel: 'basic'
      };

      const result = await clinicalDocumentationService.generateCulturalAdaptations(
        noteContent,
        culturalContext
      );

      const languageAdaptation = result.find(
        adaptation => adaptation.aspect === 'language'
      );
      
      expect(languageAdaptation).toBeDefined();
      expect(languageAdaptation.adaptation).toContain('language');
    });

    it('should adapt for religious considerations', async () => {
      const noteContent = {
        chiefComplaint: 'Patient requires blood transfusion',
        plan: {
          procedures: [
            {
              procedure: 'blood_transfusion',
              indication: 'severe_anemia'
            }
          ]
        }
      };

      const culturalContext = {
        cultureCode: 'yoruba',
        religiousBeliefs: ['traditional_yoruba'],
        religiousRestrictions: ['blood_products']
      };

      const result = await clinicalDocumentationService.generateCulturalAdaptations(
        noteContent,
        culturalContext
      );

      const religiousAdaptation = result.find(
        adaptation => adaptation.aspect === 'religious_considerations'
      );
      
      expect(religiousAdaptation).toBeDefined();
      expect(religiousAdaptation.adaptation).toContain('religious');
    });

    it('should handle dietary restrictions and preferences', async () => {
      const noteContent = {
        chiefComplaint: 'Patient needs dietary counseling',
        plan: {
          lifestyle: [
            {
              recommendation: 'dietary_changes',
              instructions: 'Follow low-sodium diet'
            }
          ]
        }
      };

      const culturalContext = {
        cultureCode: 'akan',
        dietaryRestrictions: ['pork', 'alcohol'],
        traditionalFoods: ['fufu', 'plantain', 'yam']
      };

      const result = await clinicalDocumentationService.generateCulturalAdaptations(
        noteContent,
        culturalContext
      );

      const dietaryAdaptation = result.find(
        adaptation => adaptation.aspect === 'dietary_considerations'
      );
      
      expect(dietaryAdaptation).toBeDefined();
      expect(dietaryAdaptation.adaptation).toContain('traditional');
    });

    it('should adapt communication style based on culture', async () => {
      const noteContent = {
        chiefComplaint: 'Patient needs bad news delivery',
        assessment: {
          primaryDiagnosis: {
            condition: 'cancer',
            prognosis: 'poor'
          }
        }
      };

      const culturalContext = {
        cultureCode: 'akan',
        communicationStyle: 'indirect',
        familyInvolvementLevel: 'high',
        elderRespect: 'high'
      };

      const result = await clinicalDocumentationService.generateCulturalAdaptations(
        noteContent,
        culturalContext
      );

      const communicationAdaptation = result.find(
        adaptation => adaptation.aspect === 'communication_style'
      );
      
      expect(communicationAdaptation).toBeDefined();
      expect(communicationAdaptation.adaptation).toContain('indirect');
    });

    it('should consider gender-specific cultural factors', async () => {
      const noteContent = {
        chiefComplaint: 'Female patient needs gynecological examination',
        physicalExamination: {
          plannedExaminations: ['pelvic_exam']
        }
      };

      const culturalContext = {
        cultureCode: 'hausa',
        genderConsiderations: {
          femalePatient: true,
          preferFemaleProvider: true,
          modestyConcerns: 'high'
        }
      };

      const result = await clinicalDocumentationService.generateCulturalAdaptations(
        noteContent,
        culturalContext
      );

      const genderAdaptation = result.find(
        adaptation => adaptation.aspect === 'gender_considerations'
      );
      
      expect(genderAdaptation).toBeDefined();
      expect(genderAdaptation.adaptation).toContain('female');
    });

    it('should handle multiple cultural factors simultaneously', async () => {
      const noteContent = {
        chiefComplaint: 'Complex patient with multiple cultural considerations',
        assessment: {
          primaryDiagnosis: { condition: 'diabetes' }
        },
        plan: {
          medications: [{ name: 'insulin' }],
          lifestyle: [{ recommendation: 'diet_modification' }]
        }
      };

      const culturalContext = {
        cultureCode: 'yoruba',
        familyInvolvementLevel: 'high',
        traditionalMedicineOpenness: 4,
        languagePreference: 'yo',
        religiousBeliefs: ['christianity', 'traditional_yoruba'],
        dietaryRestrictions: ['pork'],
        communicationStyle: 'indirect'
      };

      const result = await clinicalDocumentationService.generateCulturalAdaptations(
        noteContent,
        culturalContext
      );

      expect(result.length).toBeGreaterThan(3);
      
      const aspects = result.map(adaptation => adaptation.aspect);
      expect(aspects).toContain('family_involvement');
      expect(aspects).toContain('traditional_medicine');
      expect(aspects).toContain('language');
      expect(aspects).toContain('religious_considerations');
    });
  });
});
