/**
 * CLINICAL NOTE QUALITY ASSESSMENT TESTS
 * 
 * Focused tests for clinical note quality assessment functionality
 * including completeness, accuracy, clarity, and compliance scoring.
 */

// Vitest globals are available via vitest.config.js globals: true
import { clinicalDocumentationService } from '../../services/ClinicalDocumentationService';

describe('Clinical Note Quality Assessment', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    clinicalDocumentationService.clearCaches();
  });

  describe('assessNoteQuality - Complete Notes', () => {
    it('should assess quality of complete clinical note', async () => {
      const clinicalNote = {
        chiefComplaint: 'Headache and fever for 3 days',
        historyOfPresentIllness: 'Patient reports severe headache with associated fever, nausea, and photophobia.',
        pastMedicalHistory: [
          {
            condition: 'Hypertension',
            diagnosedDate: new Date('2020-01-01'),
            status: 'active'
          }
        ],
        medications: [
          {
            name: '<PERSON>sin<PERSON><PERSON>',
            dosage: '10mg',
            frequency: 'daily',
            route: 'oral',
            startDate: new Date('2020-01-01'),
            indication: 'Hypertension',
            prescribedBy: 'provider-456'
          }
        ],
        allergies: [
          {
            allergen: 'Penicillin',
            reaction: 'Rash',
            severity: 'moderate',
            onsetDate: new Date('2015-01-01')
          }
        ],
        socialHistory: {
          smokingStatus: 'never',
          alcoholUse: 'occasional',
          drugUse: 'none',
          occupation: 'teacher',
          maritalStatus: 'married',
          livingArrangement: 'with_family'
        },
        familyHistory: [
          {
            relationship: 'mother',
            condition: 'diabetes',
            ageOfOnset: 55,
            status: 'deceased'
          }
        ],
        reviewOfSystems: {
          constitutional: {
            fever: true,
            chills: false,
            fatigue: true,
            weightLoss: false,
            weightGain: false
          },
          cardiovascular: {
            chestPain: false,
            palpitations: false,
            shortnessOfBreath: false,
            orthopnea: false,
            edema: false
          },
          respiratory: {
            cough: false,
            shortnessOfBreath: false,
            wheezing: false,
            chestPain: false
          },
          neurological: {
            headache: true,
            dizziness: false,
            weakness: false,
            numbness: false,
            seizures: false
          }
        },
        physicalExamination: {
          vitalSigns: {
            bloodPressure: { systolic: 150, diastolic: 90 },
            heartRate: 88,
            temperature: 38.5,
            respiratoryRate: 18,
            oxygenSaturation: 98,
            weight: 65,
            height: 165,
            bmi: 23.9
          },
          generalAppearance: 'Patient appears ill, lying in bed with eyes closed',
          systemExaminations: {
            cardiovascular: {
              heartSounds: 'Regular rate and rhythm, no murmurs',
              pulses: 'Strong and equal bilaterally',
              edema: 'None'
            },
            respiratory: {
              breathSounds: 'Clear to auscultation bilaterally',
              respiratoryEffort: 'Normal',
              chestExpansion: 'Symmetric'
            },
            neurological: {
              mentalStatus: 'Alert and oriented x3',
              cranialNerves: 'Grossly intact',
              motorFunction: 'Normal strength throughout',
              sensoryFunction: 'Intact to light touch',
              reflexes: 'Normal and symmetric'
            }
          },
          abnormalFindings: [
            {
              system: 'neurological',
              finding: 'Photophobia present',
              significance: 'May indicate meningeal irritation'
            }
          ]
        },
        assessment: {
          primaryDiagnosis: {
            condition: 'Tension headache with possible migraine features',
            confidence: 'probable',
            evidenceLevel: 'B'
          },
          differentialDiagnoses: [
            {
              condition: 'Migraine headache',
              confidence: 'possible',
              evidenceLevel: 'C'
            },
            {
              condition: 'Meningitis',
              confidence: 'unlikely',
              evidenceLevel: 'D'
            }
          ],
          secondaryDiagnoses: [
            {
              condition: 'Hypertension',
              confidence: 'established',
              evidenceLevel: 'A'
            }
          ],
          clinicalImpression: 'Patient presents with acute headache and fever. Most likely tension headache with possible migraine features. Meningitis considered but less likely given normal neurological exam.',
          riskStratification: {
            overallRisk: 'moderate',
            specificRisks: [
              {
                risk: 'Medication overuse headache',
                probability: 'low',
                timeframe: 'chronic'
              }
            ],
            modifiableFactors: [
              'Stress management',
              'Sleep hygiene',
              'Blood pressure control'
            ],
            interventionPriorities: [
              'Pain management',
              'Blood pressure monitoring',
              'Follow-up care'
            ]
          },
          culturalFactors: [
            {
              factor: 'Family involvement in healthcare decisions',
              consideration: 'Include family in treatment planning',
              adaptation: 'Provide information to family members as appropriate'
            }
          ]
        },
        plan: {
          medications: [
            {
              name: 'Ibuprofen',
              dosage: '400mg',
              frequency: 'every 6 hours as needed',
              route: 'oral',
              indication: 'Headache pain',
              duration: '3 days',
              instructions: 'Take with food'
            }
          ],
          procedures: [],
          referrals: [],
          lifestyle: [
            {
              recommendation: 'Adequate hydration',
              rationale: 'May help with headache relief',
              instructions: 'Drink 8-10 glasses of water daily'
            },
            {
              recommendation: 'Stress management',
              rationale: 'Stress can trigger headaches',
              instructions: 'Practice relaxation techniques'
            }
          ],
          followUp: [
            {
              provider: 'Primary care physician',
              timeframe: '1 week',
              reason: 'Reassess headache and blood pressure',
              instructions: 'Return sooner if symptoms worsen'
            }
          ],
          patientEducation: [
            {
              topic: 'Headache triggers',
              content: 'Common triggers include stress, lack of sleep, certain foods',
              method: 'verbal and written'
            }
          ],
          culturalAdaptations: [
            {
              aspect: 'family_involvement',
              adaptation: 'Include family in follow-up discussions',
              rationale: 'Cultural preference for family involvement in healthcare'
            }
          ]
        },
        followUpInstructions: [
          'Return to clinic in 1 week for follow-up',
          'Return immediately if fever increases or neck stiffness develops',
          'Continue current blood pressure medication as prescribed'
        ],
        providerNotes: 'Patient educated about headache management and warning signs. Family involved in care planning as per cultural preferences.'
      };

      const result = await clinicalDocumentationService.assessNoteQuality(
        clinicalNote,
        'soap'
      );

      expect(result.completeness).toBeGreaterThan(80);
      expect(result.accuracy).toBeGreaterThan(80);
      expect(result.clarity).toBeGreaterThan(80);
      expect(result.culturalSensitivity).toBeGreaterThan(80);
      expect(result.complianceScore).toBeGreaterThan(80);
      expect(result.improvementSuggestions).toBeDefined();
    });
  });

  describe('assessNoteQuality - Incomplete Notes', () => {
    it('should identify quality issues in incomplete notes', async () => {
      const incompleteNote = {
        chiefComplaint: '',
        historyOfPresentIllness: '',
        pastMedicalHistory: [],
        medications: [],
        allergies: [],
        socialHistory: {},
        familyHistory: [],
        reviewOfSystems: {},
        physicalExamination: {
          vitalSigns: {},
          generalAppearance: '',
          systemExaminations: {},
          abnormalFindings: []
        },
        assessment: {
          primaryDiagnosis: {
            condition: '',
            confidence: 'unknown',
            evidenceLevel: 'D'
          },
          differentialDiagnoses: [],
          secondaryDiagnoses: [],
          clinicalImpression: '',
          riskStratification: {
            overallRisk: 'unknown',
            specificRisks: [],
            modifiableFactors: [],
            interventionPriorities: []
          },
          culturalFactors: []
        },
        plan: {
          medications: [],
          procedures: [],
          referrals: [],
          lifestyle: [],
          followUp: [],
          patientEducation: [],
          culturalAdaptations: []
        },
        followUpInstructions: [],
        providerNotes: ''
      };

      const result = await clinicalDocumentationService.assessNoteQuality(
        incompleteNote,
        'soap'
      );

      expect(result.completeness).toBeLessThan(50);
      expect(result.improvementSuggestions).toBeDefined();
      expect(result.improvementSuggestions.length).toBeGreaterThan(0);
    });
  });

  describe('Quality Metrics Validation', () => {
    it('should provide detailed improvement suggestions', async () => {
      const basicNote = {
        chiefComplaint: 'Headache',
        historyOfPresentIllness: 'Patient has headache',
        assessment: {
          primaryDiagnosis: {
            condition: 'headache',
            confidence: 'possible',
            evidenceLevel: 'C'
          }
        },
        plan: {
          medications: [],
          followUp: []
        }
      };

      const result = await clinicalDocumentationService.assessNoteQuality(
        basicNote,
        'soap'
      );

      expect(result.improvementSuggestions).toBeDefined();
      expect(result.improvementSuggestions.length).toBeGreaterThan(0);
      
      // Should suggest improvements for missing sections
      const suggestions = result.improvementSuggestions.map(s => s.category);
      expect(suggestions).toContain('completeness');
    });

    it('should assess cultural sensitivity appropriately', async () => {
      const culturallyAwareNote = {
        chiefComplaint: 'Patient reports traditional medicine use',
        assessment: {
          culturalFactors: [
            {
              factor: 'Traditional medicine use',
              consideration: 'Respect patient preferences',
              adaptation: 'Integrate with modern treatment'
            }
          ]
        },
        plan: {
          culturalAdaptations: [
            {
              aspect: 'treatment_approach',
              adaptation: 'Combine traditional and modern approaches',
              rationale: 'Patient preference and cultural beliefs'
            }
          ]
        }
      };

      const result = await clinicalDocumentationService.assessNoteQuality(
        culturallyAwareNote,
        'soap'
      );

      expect(result.culturalSensitivity).toBeGreaterThan(70);
    });
  });
});
