/**
 * AUDIO PERFORMANCE AND STRESS TESTS
 * 
 * Comprehensive performance testing for audio services including:
 * - Load testing with concurrent operations
 * - Memory usage monitoring
 * - Response time benchmarking
 * - Stress testing under adverse conditions
 * - Emergency response time validation
 */

// Vitest globals are available via vitest.config.js globals: true
import speechToTextService from '../services/speechToTextService';
import textToSpeechService from '../services/textToSpeechService';
import aiOrchestrator from '../services/aiOrchestrator';
import audioStorageService from '../utils/audioStorageService';
import type {
  AudioBlob,
  SpeechToTextOptions,
  TextToSpeechOptions,
  AIOrchestrationOptions,
  ChatMessage
} from '../types/audio';

// Performance monitoring utilities
interface PerformanceMetrics {
  startTime: number;
  endTime: number;
  duration: number;
  memoryBefore: number;
  memoryAfter: number;
  memoryDelta: number;
}

const measurePerformance = async <T>(
  operation: () => Promise<T>
): Promise<{ result: T; metrics: PerformanceMetrics }> => {
  const memoryBefore = (performance as any).memory?.usedJSHeapSize || 0;
  const startTime = performance.now();
  
  const result = await operation();
  
  const endTime = performance.now();
  const memoryAfter = (performance as any).memory?.usedJSHeapSize || 0;
  
  return {
    result,
    metrics: {
      startTime,
      endTime,
      duration: endTime - startTime,
      memoryBefore,
      memoryAfter,
      memoryDelta: memoryAfter - memoryBefore
    }
  };
};

// Mock implementations for performance testing
const createMockAudioBlob = (sizeKB: number): AudioBlob => {
  const data = new Uint8Array(sizeKB * 1024).fill(0x41); // Fill with 'A'
  return new Blob([data], { type: 'audio/webm' }) as AudioBlob;
};

const mockFetch = vi.fn();
const mockSupabase = {
  auth: {
    getSession: vi.fn().mockResolvedValue({
      data: { session: { access_token: 'mock-token' } }
    })
  },
  storage: {
    from: vi.fn().mockReturnValue({
      upload: vi.fn().mockResolvedValue({ data: { path: 'mock-path' }, error: null }),
      getPublicUrl: vi.fn().mockReturnValue({ data: { publicUrl: 'mock-url' } })
    })
  },
  from: vi.fn().mockReturnValue({
    insert: vi.fn().mockResolvedValue({ error: null }),
    select: vi.fn().mockReturnValue({
      eq: vi.fn().mockReturnValue({
        single: vi.fn().mockResolvedValue({ data: null, error: null })
      })
    })
  })
};

describe('Audio Performance and Stress Tests', () => {
  beforeEach(() => {
    global.fetch = mockFetch;
    vi.clearAllMocks();

    // Setup successful mock responses
    mockFetch.mockResolvedValue({
      ok: true,
      json: vi.fn().mockResolvedValue({
        success: true,
        data: {
          text: 'Mock transcription',
          confidence: 0.95,
          duration: 5.0,
          language: 'en',
          processingTime: 1000
        }
      })
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Response Time Benchmarking', () => {
    it('should complete speech-to-text within performance thresholds', async () => {
      const audioBlob = createMockAudioBlob(100); // 100KB
      const options: SpeechToTextOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token'
      };

      const { metrics } = await measurePerformance(async () => {
        return speechToTextService.transcribeAudio(audioBlob, options);
      });

      // Should complete within 5 seconds for 100KB file
      expect(metrics.duration).toBeLessThan(5000);
      console.log(`Speech-to-text duration: ${metrics.duration.toFixed(2)}ms`);
    });

    it('should complete text-to-speech within performance thresholds', async () => {
      const text = 'This is a performance test for text-to-speech synthesis with a moderate length message.';
      const options: TextToSpeechOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          success: true,
          data: {
            audioData: btoa('mock audio data'),
            audioFormat: 'audio/mpeg',
            duration: 8.0,
            voiceId: 'test-voice',
            processingTime: 1500
          }
        })
      });

      const { metrics } = await measurePerformance(async () => {
        return textToSpeechService.synthesizeSpeech(text, options);
      });

      // Should complete within 3 seconds for moderate text
      expect(metrics.duration).toBeLessThan(3000);
      console.log(`Text-to-speech duration: ${metrics.duration.toFixed(2)}ms`);
    });

    it('should complete AI orchestration within performance thresholds', async () => {
      const messages: ChatMessage[] = [
        { role: 'user', content: 'I have a headache' },
        { role: 'assistant', content: 'Can you describe the pain?' },
        { role: 'user', content: 'It\'s a throbbing pain on the left side' }
      ];

      const options: AIOrchestrationOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        messages,
        agentType: 'general-practitioner'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          success: true,
          data: {
            content: 'Based on your symptoms, this could be a tension headache...',
            agentType: 'general-practitioner',
            usage: { completion_tokens: 50, total_tokens: 100 },
            processingTime: 2000
          }
        })
      });

      const { metrics } = await measurePerformance(async () => {
        return aiOrchestrator.generateResponse(options);
      });

      // Should complete within 4 seconds for moderate conversation
      expect(metrics.duration).toBeLessThan(4000);
      console.log(`AI orchestration duration: ${metrics.duration.toFixed(2)}ms`);
    });

    it('should complete audio validation within performance thresholds', async () => {
      const audioBlob = createMockAudioBlob(500); // 500KB

      const { metrics } = await measurePerformance(async () => {
        return audioStorageService.validateAudioFile(audioBlob);
      });

      // Should complete within 2 seconds for 500KB file
      expect(metrics.duration).toBeLessThan(2000);
      console.log(`Audio validation duration: ${metrics.duration.toFixed(2)}ms`);
    });
  });

  describe('Concurrent Load Testing', () => {
    it('should handle multiple concurrent transcription requests', async () => {
      const concurrentRequests = 10;
      const audioBlob = createMockAudioBlob(50); // 50KB each
      const options: SpeechToTextOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token'
      };

      const { metrics } = await measurePerformance(async () => {
        const promises = Array.from({ length: concurrentRequests }, () =>
          speechToTextService.transcribeAudio(audioBlob, options)
        );
        return Promise.all(promises);
      });

      // Should handle 10 concurrent requests within 10 seconds
      expect(metrics.duration).toBeLessThan(10000);
      console.log(`Concurrent transcription (${concurrentRequests}): ${metrics.duration.toFixed(2)}ms`);
    });

    it('should handle multiple concurrent validation requests', async () => {
      const concurrentRequests = 20;
      const audioBlob = createMockAudioBlob(100); // 100KB each

      const { metrics } = await measurePerformance(async () => {
        const promises = Array.from({ length: concurrentRequests }, () =>
          audioStorageService.validateAudioFile(audioBlob)
        );
        return Promise.all(promises);
      });

      // Should handle 20 concurrent validations within 5 seconds
      expect(metrics.duration).toBeLessThan(5000);
      console.log(`Concurrent validation (${concurrentRequests}): ${metrics.duration.toFixed(2)}ms`);
    });

    it('should handle mixed concurrent operations', async () => {
      const audioBlob = createMockAudioBlob(75); // 75KB
      const text = 'Concurrent operation test message';
      const messages: ChatMessage[] = [
        { role: 'user', content: 'Test concurrent operations' }
      ];

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: vi.fn().mockResolvedValue({
            success: true,
            data: { text: 'Mock transcription', confidence: 0.95, duration: 3.0, language: 'en', processingTime: 800 }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: vi.fn().mockResolvedValue({
            success: true,
            data: { audioData: btoa('mock'), audioFormat: 'audio/mpeg', duration: 5.0, voiceId: 'test', processingTime: 1200 }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: vi.fn().mockResolvedValue({
            success: true,
            data: { content: 'AI response', agentType: 'gp', usage: { completion_tokens: 20, total_tokens: 40 }, processingTime: 1500 }
          })
        });

      const { metrics } = await measurePerformance(async () => {
        return Promise.all([
          speechToTextService.transcribeAudio(audioBlob, {
            sessionId: '12345678-1234-1234-1234-123456789012',
            sessionToken: 'mock-token'
          }),
          textToSpeechService.synthesizeSpeech(text, {
            sessionId: '12345678-1234-1234-1234-123456789012',
            sessionToken: 'mock-token'
          }),
          aiOrchestrator.generateResponse({
            sessionId: '12345678-1234-1234-1234-123456789012',
            messages
          }),
          audioStorageService.validateAudioFile(audioBlob)
        ]);
      });

      // Should handle mixed operations within 6 seconds
      expect(metrics.duration).toBeLessThan(6000);
      console.log(`Mixed concurrent operations: ${metrics.duration.toFixed(2)}ms`);
    });
  });

  describe('Memory Usage Monitoring', () => {
    it('should maintain reasonable memory usage during audio processing', async () => {
      const largeAudioBlob = createMockAudioBlob(5000); // 5MB

      const { metrics } = await measurePerformance(async () => {
        return audioStorageService.validateAudioFile(largeAudioBlob);
      });

      // Memory increase should be reasonable (less than 50MB)
      expect(metrics.memoryDelta).toBeLessThan(50 * 1024 * 1024);
      console.log(`Memory delta for 5MB validation: ${(metrics.memoryDelta / 1024 / 1024).toFixed(2)}MB`);
    });

    it('should clean up memory after batch operations', async () => {
      const batchSize = 50;
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;

      // Process batch of audio files
      for (let i = 0; i < batchSize; i++) {
        const audioBlob = createMockAudioBlob(100); // 100KB each
        await audioStorageService.validateAudioFile(audioBlob);
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be minimal after batch processing
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // Less than 100MB
      console.log(`Memory increase after ${batchSize} operations: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    });

    it('should handle memory pressure gracefully', async () => {
      // Simulate memory pressure with large operations
      const largeOperations = 5;
      const largeAudioBlob = createMockAudioBlob(10000); // 10MB each

      const { metrics } = await measurePerformance(async () => {
        const promises = Array.from({ length: largeOperations }, () =>
          audioStorageService.validateAudioFile(largeAudioBlob)
        );
        return Promise.all(promises);
      });

      // Should complete even under memory pressure
      expect(metrics.duration).toBeLessThan(15000); // 15 seconds max
      console.log(`Large operations under memory pressure: ${metrics.duration.toFixed(2)}ms`);
    });
  });

  describe('Emergency Response Time Validation', () => {
    it('should trigger emergency protocols within 2 seconds', async () => {
      const emergencyStartTime = performance.now();
      
      // Simulate emergency scenario
      const audioBlob = createMockAudioBlob(50);
      const emergencyOptions: SpeechToTextOptions = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'emergency-token',
        emergencyOverride: true
      };

      // Mock emergency response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          success: true,
          data: {
            text: 'Emergency medical situation',
            confidence: 0.98,
            duration: 2.0,
            language: 'en',
            processingTime: 500
          }
        })
      });

      await speechToTextService.transcribeAudio(audioBlob, emergencyOptions);
      
      const emergencyResponseTime = performance.now() - emergencyStartTime;

      // Emergency response must be under 2 seconds
      expect(emergencyResponseTime).toBeLessThan(2000);
      console.log(`Emergency response time: ${emergencyResponseTime.toFixed(2)}ms`);
    });

    it('should maintain emergency bypass under load', async () => {
      const concurrentEmergencies = 3;
      const emergencyStartTime = performance.now();

      const emergencyPromises = Array.from({ length: concurrentEmergencies }, () => {
        const audioBlob = createMockAudioBlob(25);
        return speechToTextService.transcribeAudio(audioBlob, {
          sessionId: '12345678-1234-1234-1234-123456789012',
          sessionToken: 'emergency-token',
          emergencyOverride: true
        });
      });

      await Promise.all(emergencyPromises);
      
      const totalEmergencyTime = performance.now() - emergencyStartTime;

      // Multiple emergencies should still complete quickly
      expect(totalEmergencyTime).toBeLessThan(3000);
      console.log(`Multiple emergency response time: ${totalEmergencyTime.toFixed(2)}ms`);
    });
  });

  describe('Stress Testing Under Adverse Conditions', () => {
    it('should handle network timeouts gracefully', async () => {
      const audioBlob = createMockAudioBlob(100);
      
      // Mock network timeout
      mockFetch.mockRejectedValueOnce(new Error('Network timeout'));

      const result = await speechToTextService.transcribeAudio(audioBlob, {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle service unavailability', async () => {
      const audioBlob = createMockAudioBlob(100);
      
      // Mock service unavailable
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        json: vi.fn().mockResolvedValue({
          success: false,
          error: 'Service temporarily unavailable'
        })
      });

      const result = await speechToTextService.transcribeAudio(audioBlob, {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Service');
    });

    it('should handle malformed responses', async () => {
      const audioBlob = createMockAudioBlob(100);
      
      // Mock malformed response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockRejectedValue(new Error('Invalid JSON'))
      });

      const result = await speechToTextService.transcribeAudio(audioBlob, {
        sessionId: '12345678-1234-1234-1234-123456789012',
        sessionToken: 'mock-token'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle extremely large audio files', async () => {
      const extremelyLargeBlob = createMockAudioBlob(30000); // 30MB (exceeds 25MB limit)

      const validationResult = await audioStorageService.validateAudioFile(extremelyLargeBlob);

      expect(validationResult.valid).toBe(false);
      expect(validationResult.errors).toContain(
        expect.stringContaining('too large')
      );
    });

    it('should handle rapid successive requests', async () => {
      const rapidRequests = 100;
      const audioBlob = createMockAudioBlob(10); // Small files for rapid testing

      const { metrics } = await measurePerformance(async () => {
        const promises: Promise<any>[] = [];
        
        for (let i = 0; i < rapidRequests; i++) {
          promises.push(audioStorageService.validateAudioFile(audioBlob));
        }
        
        return Promise.all(promises);
      });

      // Should handle rapid requests without significant degradation
      expect(metrics.duration).toBeLessThan(10000); // 10 seconds for 100 requests
      console.log(`Rapid requests (${rapidRequests}): ${metrics.duration.toFixed(2)}ms`);
    });
  });

  describe('Performance Regression Detection', () => {
    it('should maintain baseline performance for common operations', async () => {
      const baselineMetrics = {
        validation: 500, // 500ms baseline for validation
        transcription: 2000, // 2s baseline for transcription
        synthesis: 1500, // 1.5s baseline for synthesis
        aiResponse: 3000 // 3s baseline for AI response
      };

      // Test validation performance
      const audioBlob = createMockAudioBlob(200);
      const { metrics: validationMetrics } = await measurePerformance(async () => {
        return audioStorageService.validateAudioFile(audioBlob);
      });

      // Allow 50% variance from baseline
      expect(validationMetrics.duration).toBeLessThan(baselineMetrics.validation * 1.5);

      console.log('Performance Baseline Check:');
      console.log(`Validation: ${validationMetrics.duration.toFixed(2)}ms (baseline: ${baselineMetrics.validation}ms)`);
    });
  });
});
