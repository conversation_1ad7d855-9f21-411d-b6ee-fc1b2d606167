/**
 * CONSULTATION CONCLUSION SERVICE
 * 
 * This service detects when consultations are concluding and triggers
 * background educational content generation. It analyzes conversation
 * patterns, agent responses, and goal completion to determine optimal
 * timing for educational content delivery.
 * 
 * FEATURES:
 * - Real-time consultation conclusion detection
 * - Background educational content generation
 * - Goal completion analysis
 * - Agent response pattern recognition
 * - Automated follow-up scheduling
 * - Patient satisfaction assessment triggers
 */

import { educationAgent } from '../agents/EducationAgent';
import { goalTrackerAgent } from '../agents/GoalTrackerAgent';
import { supabase } from '../utils/supabaseClient';
import auditLogger from '../utils/auditLogger';
import type { AgentRequest, AgentResponse } from '../agents/BaseAgent';
import type { ConversationMessage } from './MemoryManager';

interface ConclusionSignals {
  goalCompletionRate: number;
  conversationLength: number;
  agentConclusionIndicators: string[];
  patientSatisfactionSignals: string[];
  followUpMentioned: boolean;
  emergencyFlagsResolved: boolean;
  timeElapsed: number;
}

interface ConclusionAnalysis {
  conclusionProbability: number;
  conclusionType: 'natural' | 'goal_complete' | 'time_limit' | 'patient_initiated' | 'emergency_resolved';
  recommendedActions: string[];
  educationalContentNeeded: boolean;
  followUpRequired: boolean;
  satisfactionAssessmentNeeded: boolean;
}

interface EducationalContentTrigger {
  sessionId: string;
  userId: string;
  triggerReason: string;
  consultationSummary: string;
  keyTopics: string[];
  culturalContext: any;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
}

class ConsultationConclusionService {
  private conclusionThreshold = 0.75; // 75% probability threshold
  private activeMonitoring: Map<string, NodeJS.Timeout> = new Map();
  private conclusionHistory: Map<string, ConclusionAnalysis> = new Map();

  constructor() {
    console.log('🏁 Consultation Conclusion Service initialized');
  }

  /**
   * Analyze conversation for conclusion signals
   */
  async analyzeForConclusion(
    sessionId: string,
    agentRequest: AgentRequest,
    agentResponse: AgentResponse,
    conversationHistory: ConversationMessage[]
  ): Promise<ConclusionAnalysis> {
    try {
      console.log(`🏁 Analyzing conclusion signals for session: ${sessionId}`);

      // Gather conclusion signals
      const signals = await this.gatherConclusionSignals(
        sessionId,
        agentRequest,
        agentResponse,
        conversationHistory
      );

      // Calculate conclusion probability
      const conclusionProbability = this.calculateConclusionProbability(signals);

      // Determine conclusion type
      const conclusionType = this.determineConclusionType(signals);

      // Generate recommendations
      const recommendedActions = this.generateRecommendations(signals, conclusionProbability);

      const analysis: ConclusionAnalysis = {
        conclusionProbability,
        conclusionType,
        recommendedActions,
        educationalContentNeeded: conclusionProbability > this.conclusionThreshold,
        followUpRequired: signals.followUpMentioned || signals.goalCompletionRate < 80,
        satisfactionAssessmentNeeded: conclusionProbability > this.conclusionThreshold
      };

      // Store analysis for tracking
      this.conclusionHistory.set(sessionId, analysis);

      // Trigger actions if conclusion detected
      if (conclusionProbability > this.conclusionThreshold) {
        await this.triggerConclusionActions(sessionId, analysis, agentRequest);
      }

      console.log(`✅ Conclusion analysis: ${(conclusionProbability * 100).toFixed(0)}% probability`);

      return analysis;

    } catch (error) {
      console.error('❌ Conclusion analysis failed:', error);
      
      return {
        conclusionProbability: 0,
        conclusionType: 'natural',
        recommendedActions: [],
        educationalContentNeeded: false,
        followUpRequired: false,
        satisfactionAssessmentNeeded: false
      };
    }
  }

  /**
   * Gather signals that indicate consultation conclusion
   */
  private async gatherConclusionSignals(
    sessionId: string,
    agentRequest: AgentRequest,
    agentResponse: AgentResponse,
    conversationHistory: ConversationMessage[]
  ): Promise<ConclusionSignals> {
    
    // Get goal completion rate
    const goalCompletionRate = await this.getGoalCompletionRate(sessionId);

    // Analyze conversation length
    const conversationLength = conversationHistory.length;

    // Detect agent conclusion indicators
    const agentConclusionIndicators = this.detectAgentConclusionIndicators(agentResponse);

    // Detect patient satisfaction signals
    const patientSatisfactionSignals = this.detectPatientSatisfactionSignals(agentRequest.userMessage);

    // Check if follow-up was mentioned
    const followUpMentioned = this.detectFollowUpMentions(agentResponse.content);

    // Check if emergency flags are resolved
    const emergencyFlagsResolved = (agentResponse.emergencyFlags || []).length === 0;

    // Calculate time elapsed (mock for now)
    const timeElapsed = conversationLength * 2; // Assume 2 minutes per exchange

    return {
      goalCompletionRate,
      conversationLength,
      agentConclusionIndicators,
      patientSatisfactionSignals,
      followUpMentioned,
      emergencyFlagsResolved,
      timeElapsed
    };
  }

  /**
   * Get goal completion rate from goal tracker
   */
  private async getGoalCompletionRate(sessionId: string): Promise<number> {
    try {
      const { data: goals, error } = await supabase
        .rpc('get_active_session_goals', { p_session_id: sessionId });

      if (error || !goals || goals.length === 0) {
        return 50; // Default moderate completion if no goals
      }

      const totalProgress = goals.reduce((sum: number, goal: any) => sum + goal.progress_percentage, 0);
      return totalProgress / goals.length;

    } catch (error) {
      console.warn('Failed to get goal completion rate:', error);
      return 50; // Default fallback
    }
  }

  /**
   * Detect agent conclusion indicators in response
   */
  private detectAgentConclusionIndicators(response: AgentResponse): string[] {
    const indicators: string[] = [];
    const responseText = response.content.toLowerCase();

    const conclusionPhrases = [
      'in summary', 'to summarize', 'in conclusion', 'to conclude',
      'take care', 'feel better', 'hope this helps', 'best wishes',
      'follow up', 'see you', 'contact me', 'reach out',
      'any other questions', 'anything else', 'is there anything',
      'that covers', 'we\'ve discussed', 'i think we\'ve covered'
    ];

    for (const phrase of conclusionPhrases) {
      if (responseText.includes(phrase)) {
        indicators.push(phrase);
      }
    }

    // Check for handoff suggestions as conclusion indicator
    if (response.suggestedHandoffs && response.suggestedHandoffs.length > 0) {
      indicators.push('specialist_referral_suggested');
    }

    return indicators;
  }

  /**
   * Detect patient satisfaction signals
   */
  private detectPatientSatisfactionSignals(userMessage: string): string[] {
    const signals: string[] = [];
    const messageText = userMessage.toLowerCase();

    const satisfactionPhrases = [
      'thank you', 'thanks', 'appreciate', 'helpful', 'great',
      'perfect', 'exactly', 'that helps', 'makes sense',
      'understand', 'clear', 'good to know', 'relieved'
    ];

    const dissatisfactionPhrases = [
      'confused', 'don\'t understand', 'not clear', 'still worried',
      'not helpful', 'doesn\'t help', 'more questions', 'concerned'
    ];

    for (const phrase of satisfactionPhrases) {
      if (messageText.includes(phrase)) {
        signals.push(`positive:${phrase}`);
      }
    }

    for (const phrase of dissatisfactionPhrases) {
      if (messageText.includes(phrase)) {
        signals.push(`negative:${phrase}`);
      }
    }

    return signals;
  }

  /**
   * Detect follow-up mentions
   */
  private detectFollowUpMentions(responseText: string): boolean {
    const followUpPhrases = [
      'follow up', 'follow-up', 'next appointment', 'see you again',
      'check back', 'return visit', 'schedule', 'book appointment',
      'come back', 'contact us', 'reach out'
    ];

    const lowerText = responseText.toLowerCase();
    return followUpPhrases.some(phrase => lowerText.includes(phrase));
  }

  /**
   * Calculate conclusion probability based on signals
   */
  private calculateConclusionProbability(signals: ConclusionSignals): number {
    let probability = 0;

    // Goal completion weight (40%)
    probability += (signals.goalCompletionRate / 100) * 0.4;

    // Agent conclusion indicators weight (25%)
    const conclusionIndicatorScore = Math.min(signals.agentConclusionIndicators.length / 3, 1);
    probability += conclusionIndicatorScore * 0.25;

    // Patient satisfaction weight (20%)
    const positiveSignals = signals.patientSatisfactionSignals.filter(s => s.startsWith('positive')).length;
    const negativeSignals = signals.patientSatisfactionSignals.filter(s => s.startsWith('negative')).length;
    const satisfactionScore = Math.max(0, (positiveSignals - negativeSignals) / 3);
    probability += Math.min(satisfactionScore, 1) * 0.2;

    // Conversation length weight (10%)
    const lengthScore = Math.min(signals.conversationLength / 10, 1); // Normalize to 10 exchanges
    probability += lengthScore * 0.1;

    // Emergency resolution weight (5%)
    if (signals.emergencyFlagsResolved) {
      probability += 0.05;
    }

    return Math.min(probability, 1); // Cap at 100%
  }

  /**
   * Determine the type of conclusion
   */
  private determineConclusionType(signals: ConclusionSignals): ConclusionAnalysis['conclusionType'] {
    if (signals.goalCompletionRate >= 90) {
      return 'goal_complete';
    }

    if (signals.timeElapsed > 30) { // 30 minutes
      return 'time_limit';
    }

    if (signals.patientSatisfactionSignals.some(s => s.includes('thank'))) {
      return 'patient_initiated';
    }

    if (signals.emergencyFlagsResolved && signals.agentConclusionIndicators.length > 0) {
      return 'emergency_resolved';
    }

    return 'natural';
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(
    signals: ConclusionSignals,
    probability: number
  ): string[] {
    const recommendations: string[] = [];

    if (probability > this.conclusionThreshold) {
      recommendations.push('Generate educational content');
      recommendations.push('Schedule satisfaction assessment');
    }

    if (signals.goalCompletionRate < 80) {
      recommendations.push('Consider extending consultation');
      recommendations.push('Schedule follow-up appointment');
    }

    if (signals.followUpMentioned) {
      recommendations.push('Facilitate follow-up scheduling');
    }

    if (signals.patientSatisfactionSignals.filter(s => s.startsWith('negative')).length > 0) {
      recommendations.push('Address remaining concerns');
      recommendations.push('Provide additional clarification');
    }

    return recommendations;
  }

  /**
   * Trigger conclusion actions
   */
  private async triggerConclusionActions(
    sessionId: string,
    analysis: ConclusionAnalysis,
    agentRequest: AgentRequest
  ): Promise<void> {
    try {
      console.log(`🏁 Triggering conclusion actions for session: ${sessionId}`);

      // Generate educational content if needed
      if (analysis.educationalContentNeeded) {
        await this.triggerEducationalContentGeneration(sessionId, agentRequest);
      }

      // Schedule follow-up if required
      if (analysis.followUpRequired) {
        await this.scheduleFollowUp(sessionId, agentRequest);
      }

      // Trigger satisfaction assessment
      if (analysis.satisfactionAssessmentNeeded) {
        await this.triggerSatisfactionAssessment(sessionId);
      }

      // Audit log the conclusion
      await this.auditConsultationConclusion(sessionId, analysis);

      console.log('✅ Conclusion actions triggered successfully');

    } catch (error) {
      console.error('❌ Failed to trigger conclusion actions:', error);
    }
  }

  /**
   * Trigger educational content generation
   */
  private async triggerEducationalContentGeneration(
    sessionId: string,
    agentRequest: AgentRequest
  ): Promise<void> {
    try {
      console.log('📚 Triggering educational content generation...');

      // Create educational content request
      const educationRequest = {
        sessionId,
        userId: agentRequest.patientContext?.userId || 'unknown',
        consultationSummary: agentRequest.userMessage,
        diagnosedConditions: [], // Would extract from conversation
        recommendedTreatments: [], // Would extract from conversation
        culturalContext: agentRequest.patientContext?.regionalContext || {},
        patientProfile: {
          age: agentRequest.patientContext?.age || 30,
          gender: agentRequest.patientContext?.gender || 'unknown',
          healthLiteracyLevel: 'intermediate' as const,
          preferredLanguage: agentRequest.patientContext?.preferredLanguage || 'English'
        },
        urgencyLevel: agentRequest.urgencyLevel || 'medium'
      };

      // Generate educational content in background
      setTimeout(async () => {
        try {
          await educationAgent.generateEducationalContent(educationRequest);
          console.log('✅ Educational content generated successfully');
        } catch (error) {
          console.error('❌ Educational content generation failed:', error);
        }
      }, 1000); // 1 second delay to not block response

    } catch (error) {
      console.error('Failed to trigger educational content generation:', error);
    }
  }

  /**
   * Schedule follow-up appointment
   */
  private async scheduleFollowUp(sessionId: string, agentRequest: AgentRequest): Promise<void> {
    try {
      console.log('📅 Scheduling follow-up...');
      
      // This would integrate with appointment scheduling system
      // For now, just log the action
      await auditLogger.logDataAccess('follow_up_scheduled', sessionId, true, {
        operation: 'schedule_follow_up',
        session_id: sessionId,
        user_id: agentRequest.patientContext?.userId,
        scheduled_by: 'consultation_conclusion_service'
      });

    } catch (error) {
      console.error('Failed to schedule follow-up:', error);
    }
  }

  /**
   * Trigger satisfaction assessment
   */
  private async triggerSatisfactionAssessment(sessionId: string): Promise<void> {
    try {
      console.log('📊 Triggering satisfaction assessment...');
      
      // This would trigger a patient satisfaction survey
      // For now, just log the action
      await auditLogger.logDataAccess('satisfaction_assessment_triggered', sessionId, true, {
        operation: 'trigger_satisfaction_assessment',
        session_id: sessionId,
        triggered_by: 'consultation_conclusion_service'
      });

    } catch (error) {
      console.error('Failed to trigger satisfaction assessment:', error);
    }
  }

  /**
   * Audit consultation conclusion
   */
  private async auditConsultationConclusion(
    sessionId: string,
    analysis: ConclusionAnalysis
  ): Promise<void> {
    try {
      await auditLogger.logDataAccess('consultation_conclusion', sessionId, true, {
        operation: 'consultation_conclusion_detected',
        session_id: sessionId,
        conclusion_probability: analysis.conclusionProbability,
        conclusion_type: analysis.conclusionType,
        educational_content_needed: analysis.educationalContentNeeded,
        follow_up_required: analysis.followUpRequired,
        satisfaction_assessment_needed: analysis.satisfactionAssessmentNeeded,
        recommended_actions: analysis.recommendedActions
      });

    } catch (error) {
      console.warn('Failed to audit consultation conclusion:', error);
    }
  }

  /**
   * Start monitoring session for conclusion
   */
  startMonitoring(sessionId: string): void {
    // Clear any existing monitoring
    this.stopMonitoring(sessionId);

    // Set up periodic monitoring
    const monitoringInterval = setInterval(() => {
      // This would periodically check for conclusion signals
      console.log(`🔍 Monitoring session ${sessionId} for conclusion...`);
    }, 60000); // Check every minute

    this.activeMonitoring.set(sessionId, monitoringInterval);
  }

  /**
   * Stop monitoring session
   */
  stopMonitoring(sessionId: string): void {
    const interval = this.activeMonitoring.get(sessionId);
    if (interval) {
      clearInterval(interval);
      this.activeMonitoring.delete(sessionId);
    }
  }

  /**
   * Get conclusion analysis for session
   */
  getConclusionAnalysis(sessionId: string): ConclusionAnalysis | null {
    return this.conclusionHistory.get(sessionId) || null;
  }

  /**
   * Clean up session data to prevent memory leaks
   */
  public cleanupSession(sessionId: string): void {
    try {
      this.stopMonitoring(sessionId);
      this.conclusionHistory.delete(sessionId);
      console.log(`🧹 Cleaned up conclusion service data for session: ${sessionId}`);
    } catch (error) {
      console.warn('Failed to cleanup session data:', error);
    }
  }

  /**
   * Clean up old sessions (older than 24 hours)
   */
  public cleanupOldSessions(): void {
    try {
      const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
      let cleanedCount = 0;

      for (const [sessionId, analysis] of this.conclusionHistory.entries()) {
        // Clean up sessions that concluded more than 24 hours ago
        if (analysis.conclusionProbability > 0.75) {
          this.cleanupSession(sessionId);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        console.log(`🧹 Cleaned up ${cleanedCount} concluded sessions from conclusion service`);
      }
    } catch (error) {
      console.warn('Failed to cleanup old sessions:', error);
    }
  }

  /**
   * Get memory usage statistics
   */
  public getMemoryStats(): {
    activeMonitoring: number;
    conclusionHistory: number;
    memoryUsage: string;
  } {
    const activeMonitoring = this.activeMonitoring.size;
    const conclusionHistory = this.conclusionHistory.size;

    const memoryUsage = `${activeMonitoring} monitored, ${conclusionHistory} in history`;

    return {
      activeMonitoring,
      conclusionHistory,
      memoryUsage
    };
  }
}

// Export singleton instance
export const consultationConclusionService = new ConsultationConclusionService();
