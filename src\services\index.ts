/**
 * SERVICES INDEX
 * 
 * Central export point for all VoiceHealth AI services including
 * core services and enhancement modules.
 */

// Export core services
export { aiOrchestrator } from './aiOrchestrator';
export { memoryManager } from './MemoryManager';
export { speechToTextService } from './speechToTextService';
export { textToSpeechService } from './textToSpeechService';
export { default as auditLogger } from '../utils/auditLogger';
export { supabase } from '../utils/supabaseClient';

// Export Phase 1 services (Critical Gap Resolution)
export { clinicalDocumentationService } from './ClinicalDocumentationService';
export { advancedRiskStratificationService } from './AdvancedRiskStratificationService';
export { culturalValidationService } from './CulturalValidationService';

// Export Phase 2 services (Integration and Testing)
export { authenticationService } from './AuthenticationService';
export { encryptionService } from './EncryptionService';
export { performanceValidationService } from './PerformanceValidationService';

// Export Phase 3 services (Production Infrastructure)
export { productionMonitoringDashboard } from './ProductionMonitoringDashboard';
export { productionMonitoringService } from './ProductionMonitoringService';
export { securityAuditService } from './SecurityAuditService';
export { regionalRolloutService } from './RegionalRolloutService';

// Export enhancement services
export { vocalAnalysisService } from './VocalAnalysisService';
export { empathyMandateService } from './EmpathyMandateService';
export { consultationConclusionService } from './ConsultationConclusionService';
export { agentOrchestrator } from './AgentOrchestrator';

// Export enhancement agents
export { goalTrackerAgent } from '../agents/GoalTrackerAgent';
export { educationAgent } from '../agents/EducationAgent';

// Export context services
export { enhancedPatientContextService } from './EnhancedPatientContextService';
export { unifiedContextAssemblyService } from './UnifiedContextAssemblyService';

// Export tools
export { RAGTool } from '../tools/RAGTool';
export { VisualAnalysisTool } from '../tools/VisualAnalysisTool';

// Export types
export type {
  EmotionalContext,
  VocalAnalysisRequest,
  VocalAnalysisResponse,
  CulturalEmotionalContext,
  VisualAnalysisRequest,
  VisualAnalysisResult,
  MedicalImageMetadata,
  SessionGoal,
  SteeringGuidance,
  GoalAnalysis,
  ConclusionSignals,
  ConclusionAnalysis,
  EducationalContent,
  EducationRequest,
  EducationalContentFeedback,
  EnhancedPatientContext,
  APIResponse,
  EnhancementAPIEndpoints,
  VocalAnalysisConfig,
  VisualAnalysisConfig,
  GoalTrackingConfig,
  EducationConfig,
  EnhancementError,
  EmotionalAnalysisError,
  VisualAnalysisError
} from '../types/enhancements';

// Export agent types
export type {
  IAgent,
  AgentRequest,
  AgentResponse,
  AgentCapability,
  AgentRole,
  PatientContext
} from '../agents/BaseAgent';
