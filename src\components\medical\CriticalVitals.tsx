/**
 * Critical Vitals Component
 * Real-time monitoring and display of critical patient vital signs
 */

import React, { useState, useEffect } from 'react';

interface VitalSign {
  id: string;
  name: string;
  value: number | string;
  unit: string;
  normalRange: {
    min: number;
    max: number;
  };
  status: 'normal' | 'warning' | 'critical';
  timestamp: Date;
  trend: 'stable' | 'increasing' | 'decreasing';
}

interface CriticalVitalsProps {
  patientId?: string;
  patientAge?: number;
  autoRefresh?: boolean;
  refreshInterval?: number; // in seconds
  onCriticalAlert?: (vital: VitalSign) => void;
  emergencyMode?: boolean;
}

const CriticalVitals: React.FC<CriticalVitalsProps> = ({
  patientId,
  patientAge = 35,
  autoRefresh = true,
  refreshInterval = 30,
  onCriticalAlert,
  emergencyMode = false
}) => {
  const [vitals, setVitals] = useState<VitalSign[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Mock vital signs data with realistic values
  const generateMockVitals = (): VitalSign[] => {
    const baseVitals = [
      {
        id: 'heart-rate',
        name: 'Heart Rate',
        value: emergencyMode ? 145 : 72 + Math.floor(Math.random() * 20),
        unit: 'bpm',
        normalRange: { min: 60, max: 100 },
        status: 'normal' as const,
        timestamp: new Date(),
        trend: 'stable' as const
      },
      {
        id: 'blood-pressure-systolic',
        name: 'Blood Pressure (Systolic)',
        value: emergencyMode ? 180 : 120 + Math.floor(Math.random() * 20),
        unit: 'mmHg',
        normalRange: { min: 90, max: 140 },
        status: 'normal' as const,
        timestamp: new Date(),
        trend: 'stable' as const
      },
      {
        id: 'blood-pressure-diastolic',
        name: 'Blood Pressure (Diastolic)',
        value: emergencyMode ? 110 : 80 + Math.floor(Math.random() * 10),
        unit: 'mmHg',
        normalRange: { min: 60, max: 90 },
        status: 'normal' as const,
        timestamp: new Date(),
        trend: 'stable' as const
      },
      {
        id: 'oxygen-saturation',
        name: 'Oxygen Saturation',
        value: emergencyMode ? 88 : 98 + Math.floor(Math.random() * 2),
        unit: '%',
        normalRange: { min: 95, max: 100 },
        status: 'normal' as const,
        timestamp: new Date(),
        trend: 'stable' as const
      },
      {
        id: 'respiratory-rate',
        name: 'Respiratory Rate',
        value: emergencyMode ? 28 : 16 + Math.floor(Math.random() * 6),
        unit: '/min',
        normalRange: { min: 12, max: 20 },
        status: 'normal' as const,
        timestamp: new Date(),
        trend: 'stable' as const
      },
      {
        id: 'temperature',
        name: 'Body Temperature',
        value: emergencyMode ? 39.2 : 36.5 + Math.random() * 1,
        unit: '°C',
        normalRange: { min: 36.1, max: 37.2 },
        status: 'normal' as const,
        timestamp: new Date(),
        trend: 'stable' as const
      }
    ];

    // Determine status based on values
    return baseVitals.map(vital => {
      const numValue = typeof vital.value === 'string' ? parseFloat(vital.value) : vital.value;
      let status: 'normal' | 'warning' | 'critical' = 'normal';
      
      if (numValue < vital.normalRange.min * 0.8 || numValue > vital.normalRange.max * 1.3) {
        status = 'critical';
      } else if (numValue < vital.normalRange.min || numValue > vital.normalRange.max) {
        status = 'warning';
      }

      return { ...vital, status };
    });
  };

  useEffect(() => {
    const updateVitals = () => {
      const newVitals = generateMockVitals();
      setVitals(newVitals);
      setLastUpdate(new Date());
      setLoading(false);

      // Check for critical alerts
      newVitals.forEach(vital => {
        if (vital.status === 'critical' && onCriticalAlert) {
          onCriticalAlert(vital);
        }
      });
    };

    // Initial load
    updateVitals();

    // Set up auto-refresh
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(updateVitals, refreshInterval * 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh, refreshInterval, emergencyMode, onCriticalAlert]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical': return 'text-red-600 bg-red-100 border-red-300';
      case 'warning': return 'text-yellow-600 bg-yellow-100 border-yellow-300';
      case 'normal': return 'text-green-600 bg-green-100 border-green-300';
      default: return 'text-gray-600 bg-gray-100 border-gray-300';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return '↗️';
      case 'decreasing': return '↘️';
      case 'stable': return '➡️';
      default: return '➡️';
    }
  };

  const formatValue = (value: number | string, unit: string) => {
    if (typeof value === 'number') {
      return unit === '°C' ? value.toFixed(1) : Math.round(value);
    }
    return value;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading vital signs...</span>
      </div>
    );
  }

  return (
    <div className="critical-vitals p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 mb-1">Critical Vitals</h2>
          <p className="text-sm text-gray-600">
            Last updated: {lastUpdate.toLocaleTimeString()}
            {autoRefresh && ` • Auto-refresh: ${refreshInterval}s`}
          </p>
        </div>
        
        {emergencyMode && (
          <div className="flex items-center px-3 py-1 bg-red-100 border border-red-300 rounded-full">
            <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></span>
            <span className="text-red-700 text-sm font-medium">EMERGENCY MODE</span>
          </div>
        )}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {vitals.map((vital) => (
          <div
            key={vital.id}
            className={`vital-card p-4 border-2 rounded-lg transition-all ${getStatusColor(vital.status)} ${
              vital.status === 'critical' ? 'animate-pulse' : ''
            }`}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-gray-800">{vital.name}</h3>
              <span className="text-lg">{getTrendIcon(vital.trend)}</span>
            </div>
            
            <div className="mb-2">
              <span className="text-2xl font-bold">
                {formatValue(vital.value, vital.unit)}
              </span>
              <span className="text-sm ml-1 text-gray-600">{vital.unit}</span>
            </div>
            
            <div className="text-xs text-gray-600 mb-2">
              Normal: {vital.normalRange.min}-{vital.normalRange.max} {vital.unit}
            </div>
            
            <div className="flex items-center justify-between">
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                vital.status === 'critical' ? 'bg-red-200 text-red-800' :
                vital.status === 'warning' ? 'bg-yellow-200 text-yellow-800' :
                'bg-green-200 text-green-800'
              }`}>
                {vital.status.toUpperCase()}
              </span>
              
              <span className="text-xs text-gray-500">
                {vital.timestamp.toLocaleTimeString()}
              </span>
            </div>
          </div>
        ))}
      </div>

      {vitals.some(v => v.status === 'critical') && (
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="font-bold text-red-800 mb-2">🚨 Critical Alert</h3>
          <p className="text-red-700 text-sm">
            One or more vital signs are in critical range. Immediate medical attention required.
          </p>
          <div className="mt-2">
            {vitals
              .filter(v => v.status === 'critical')
              .map(vital => (
                <div key={vital.id} className="text-sm text-red-600">
                  • {vital.name}: {formatValue(vital.value, vital.unit)} {vital.unit}
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CriticalVitals;
