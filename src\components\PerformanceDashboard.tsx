/**
 * PERFORMANCE MONITORING DASHBOARD
 * 
 * Real-time performance monitoring dashboard for all VoiceHealth AI features
 * including metrics visualization, alerting, and performance analytics.
 * 
 * FEATURES:
 * - Real-time performance metrics display
 * - Feature-specific performance tracking
 * - Alert management and visualization
 * - Performance trend analysis
 * - Threshold configuration
 * - Emergency performance protocols
 */

import React, { useState, useEffect } from 'react';
import {
  performanceMonitoringService,
  type PerformanceSummary,
  type PerformanceAlert
} from '../services/PerformanceMonitoringService';

const PerformanceDashboard: React.FC = () => {
  const [performanceSummaries, setPerformanceSummaries] = useState<PerformanceSummary[]>([]);
  const [activeAlerts, setActiveAlerts] = useState<PerformanceAlert[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [selectedFeature, setSelectedFeature] = useState<string>('all');

  // Update performance data every 5 seconds
  useEffect(() => {
    const updatePerformanceData = (): void => {
      try {
        const summaries = performanceMonitoringService.getAllPerformanceSummaries();
        const alerts = performanceMonitoringService.getActiveAlerts();
        
        setPerformanceSummaries(summaries);
        setActiveAlerts(alerts);
        setLastUpdate(new Date());
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to update performance data:', error instanceof Error ? error.message : String(error));
      }
    };

    // Initial update
    updatePerformanceData();

    // Set up interval
    const interval = setInterval(updatePerformanceData, 5000);

    // Set up alert listener
    const handleAlert = (alert: PerformanceAlert) => {
      setActiveAlerts(prev => [...prev, alert]);
    };

    performanceMonitoringService.onAlert(handleAlert);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const getPerformanceColor = (averageDuration: number, feature: string): string => {
    // Define thresholds based on feature type
    const thresholds: Record<string, { warning: number; critical: number }> = {
      agent_orchestrator: { warning: 1000, critical: 1800 },
      circuit_breaker: { warning: 500, critical: 1000 },
      memory_cleanup: { warning: 1000, critical: 3000 },
      real_time_communication: { warning: 100, critical: 300 },
      context_truncation: { warning: 200, critical: 500 },
      error_sanitization: { warning: 50, critical: 100 },
      goal_tracking: { warning: 300, critical: 800 }
    };

    const threshold = thresholds[feature] || { warning: 500, critical: 1000 };

    if (averageDuration >= threshold.critical) return 'text-red-600 bg-red-100';
    if (averageDuration >= threshold.warning) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  const getAlertColor = (level: 'warning' | 'critical' | 'emergency'): string => {
    switch (level) {
      case 'emergency': return 'border-red-600 bg-red-50 text-red-800';
      case 'critical': return 'border-orange-600 bg-orange-50 text-orange-800';
      case 'warning': return 'border-yellow-600 bg-yellow-50 text-yellow-800';
      default: return 'border-gray-300 bg-gray-50 text-gray-800';
    }
  };

  const resolveAlert = (alertId: string): void => {
    const success = performanceMonitoringService.resolveAlert(alertId);
    if (success) {
      setActiveAlerts(prev => prev.filter(alert => alert.id !== alertId));
    }
  };

  const filteredSummaries = selectedFeature === 'all' 
    ? performanceSummaries 
    : performanceSummaries.filter(summary => summary.feature === selectedFeature);

  const uniqueFeatures = [...new Set(performanceSummaries.map(s => s.feature))];

  if (isLoading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-lg">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Performance Dashboard</h2>
        <div className="flex items-center space-x-4">
          <select
            value={selectedFeature}
            onChange={(e) => setSelectedFeature(e.target.value)}
            className="px-3 py-1 border rounded"
          >
            <option value="all">All Features</option>
            {uniqueFeatures.map(feature => (
              <option key={feature} value={feature}>
                {feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </option>
            ))}
          </select>
          <div className="text-sm text-gray-500">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* Active Alerts */}
      {activeAlerts.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Active Alerts</h3>
          <div className="space-y-2">
            {activeAlerts.map((alert) => (
              <div 
                key={alert.id} 
                className={`p-3 rounded border-l-4 ${getAlertColor(alert.level)}`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="font-medium">
                      {alert.level.toUpperCase()}: {alert.feature}.{alert.operation}
                    </div>
                    <div className="text-sm">
                      Duration: {alert.currentValue}ms (threshold: {alert.threshold}ms)
                    </div>
                    <div className="text-xs text-gray-600">
                      {new Date(alert.timestamp).toLocaleString()}
                    </div>
                  </div>
                  <button
                    onClick={() => resolveAlert(alert.id)}
                    className="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
                  >
                    Resolve
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Summaries */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-3">Performance Metrics</h3>
        
        {filteredSummaries.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No performance data available for the selected feature
          </div>
        ) : (
          <div className="grid gap-4">
            {filteredSummaries.map((summary) => (
              <div key={`${summary.feature}`} className="border rounded-lg p-4 bg-gray-50">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-semibold text-gray-800">
                      {summary.feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </h4>
                    <div className={`inline-block px-2 py-1 rounded text-xs font-medium ${getPerformanceColor(summary.averageDuration, summary.feature)}`}>
                      Avg: {Math.round(summary.averageDuration)}ms
                    </div>
                  </div>
                  
                  {summary.alertCount > 0 && (
                    <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
                      {summary.alertCount} alerts
                    </span>
                  )}
                </div>

                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">Total Operations</div>
                    <div className="font-semibold text-gray-800">
                      {summary.totalOperations.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600">Success Rate</div>
                    <div className="font-semibold text-green-600">
                      {(summary.successRate * 100).toFixed(1)}%
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600">P95 Duration</div>
                    <div className="font-semibold text-blue-600">
                      {Math.round(summary.p95Duration)}ms
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600">P99 Duration</div>
                    <div className="font-semibold text-purple-600">
                      {Math.round(summary.p99Duration)}ms
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600">Last Updated</div>
                    <div className="font-semibold text-gray-600">
                      {new Date(summary.lastUpdated).toLocaleTimeString()}
                    </div>
                  </div>
                </div>

                {/* Performance trend indicator */}
                <div className="mt-3">
                  <div className="flex items-center space-x-2">
                    <div className="text-xs text-gray-600">Performance Status:</div>
                    {summary.averageDuration < 500 && (
                      <span className="text-green-600 text-xs">🟢 Excellent</span>
                    )}
                    {summary.averageDuration >= 500 && summary.averageDuration < 1000 && (
                      <span className="text-yellow-600 text-xs">🟡 Good</span>
                    )}
                    {summary.averageDuration >= 1000 && summary.averageDuration < 2000 && (
                      <span className="text-orange-600 text-xs">🟠 Needs Attention</span>
                    )}
                    {summary.averageDuration >= 2000 && (
                      <span className="text-red-600 text-xs">🔴 Critical</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Emergency Protocols */}
      <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-lg font-semibold text-red-800 mb-2">Emergency Performance Protocols</h3>
        <div className="text-sm text-red-700 mb-3">
          ⚠️ Emergency protocols are automatically triggered when performance exceeds critical thresholds.
          The system maintains a &lt;2 second response time requirement for all emergency medical operations.
        </div>
        <div className="flex space-x-4">
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Refresh Dashboard
          </button>
          <button
            onClick={() => {
              if (window.confirm('This will reset all performance monitoring data. Continue?')) {
                // Could implement reset functionality here
                window.location.reload();
              }
            }}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Reset Monitoring
          </button>
        </div>
      </div>

      {/* Performance Guidelines */}
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
        <h4 className="font-medium text-blue-800 mb-2">Performance Guidelines</h4>
        <div className="text-sm text-blue-700 space-y-1">
          <div>🟢 Excellent: &lt;500ms average response time</div>
          <div>🟡 Good: 500ms-1000ms average response time</div>
          <div>🟠 Needs Attention: 1000ms-2000ms average response time</div>
          <div>🔴 Critical: &gt;2000ms average response time (emergency threshold)</div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard;
