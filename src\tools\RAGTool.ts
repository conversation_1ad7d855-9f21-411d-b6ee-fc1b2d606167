/**
 * RAG TOOL FOR MEDICAL KNOWLEDGE RETRIEVAL
 * 
 * Provides agents with access to curated medical knowledge sources
 * through semantic search and vector embeddings.
 * 
 * FEATURES:
 * - Semantic search of medical documents
 * - Context-aware knowledge retrieval
 * - HIPAA-compliant document access
 * - Real-time knowledge updates
 * - Evidence-based medical information
 * - Source citation and evidence levels
 */

import { BaseTool } from './BaseTool';
import { vectorSearchService } from '../services/VectorSearchService';
import type { 
  ToolRequest, 
  ToolResponse, 
  ToolCapability, 
  ToolCitation 
} from './BaseTool';
import type { 
  VectorSearchQuery, 
  VectorSearchResult, 
  RetrievedDocument 
} from '../services/VectorSearchService';

export interface RAGQuery {
  query: string;
  maxResults?: number;
  minRelevanceScore?: number;
  documentTypes?: string[];
  specialtyFilter?: string;
  evidenceLevels?: string[];
  regionFilter?: string;
  countryFilter?: string;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
  includeContent?: boolean;
  citationFormat?: 'apa' | 'vancouver' | 'simple';
}

export interface RAGResult {
  documents: RetrievedDocument[];
  summary: string;
  totalResults: number;
  searchTime: number;
  relevanceScores: number[];
  sources: string[];
  citations: ToolCitation[];
  evidenceQuality: string;
  recommendations: string[];
}

export class RAGTool extends BaseTool {
  constructor() {
    const id = 'rag-tool-001';
    const name = 'Medical Knowledge Retrieval';
    const description = 'Retrieves relevant medical knowledge from curated sources using semantic search';
    const capabilities: ToolCapability[] = [
      'knowledge_retrieval',
      'semantic_search',
      'evidence_lookup',
      'guideline_access',
      'research_query'
    ];

    super(id, name, description, capabilities);
  }

  /**
   * Execute RAG query to retrieve relevant medical knowledge
   */
  async execute(request: ToolRequest): Promise<ToolResponse> {
    const startTime = Date.now();

    try {
      console.log(`🔍 RAG Tool executing query: ${request.query}`);

      // Validate request
      this.validateRequest(request);

      // Parse RAG-specific parameters
      const ragQuery: RAGQuery = {
        query: request.query,
        maxResults: request.parameters?.maxResults || 5,
        minRelevanceScore: request.parameters?.minRelevanceScore || 0.7,
        documentTypes: request.parameters?.documentTypes || ['guideline', 'protocol', 'research'],
        specialtyFilter: request.parameters?.specialtyFilter,
        evidenceLevels: request.parameters?.evidenceLevels,
        urgencyLevel: request.urgencyLevel || 'medium',
        includeContent: request.parameters?.includeContent !== false,
        citationFormat: request.parameters?.citationFormat || 'simple'
      };

      // Perform vector search
      const searchResults = await this.performVectorSearch(ragQuery, request);

      // Process and enhance results
      const ragResult = await this.processSearchResults(searchResults, ragQuery);

      // Generate contextual summary
      const enhancedSummary = this.generateEnhancedSummary(ragResult, ragQuery);

      const executionTime = Date.now() - startTime;
      const confidence = this.calculateConfidence(ragResult);

      // Update metrics
      this.updateMetrics(executionTime, true, confidence);

      // Log tool usage
      await this.logToolUsage(request, {
        toolId: this.id,
        toolName: this.name,
        success: true,
        confidence,
        executionTime
      }, {
        documentsRetrieved: ragResult.documents.length,
        averageRelevance: ragResult.relevanceScores.reduce((a, b) => a + b, 0) / ragResult.relevanceScores.length,
        specialtyFilter: ragQuery.specialtyFilter,
        evidenceQuality: ragResult.evidenceQuality
      });

      return this.createSuccessResponse(
        {
          ...ragResult,
          summary: enhancedSummary
        },
        confidence,
        executionTime,
        {
          queryType: 'semantic_search',
          documentsRetrieved: ragResult.documents.length,
          averageRelevance: ragResult.relevanceScores.reduce((a, b) => a + b, 0) / ragResult.relevanceScores.length,
          specialtyFilter: ragQuery.specialtyFilter,
          urgencyLevel: ragQuery.urgencyLevel,
          evidenceQuality: ragResult.evidenceQuality
        },
        ragResult.sources,
        ragResult.citations
      );

    } catch (error) {
      console.error('❌ RAG Tool execution failed:', error);
      
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.updateMetrics(executionTime, false, 0, errorMessage);

      return this.createErrorResponse(
        error instanceof Error ? error : new Error(String(error)),
        executionTime,
        {
          errorType: 'rag_search_failed',
          query: request.query
        }
      );
    }
  }

  /**
   * Check if this tool can handle the request
   */
  canHandle(request: ToolRequest): boolean {
    const knowledgeKeywords = [
      'research', 'guideline', 'protocol', 'evidence', 'study',
      'recommendation', 'treatment', 'diagnosis', 'medication',
      'clinical', 'medical', 'health', 'disease', 'condition',
      'what does', 'how to', 'best practice', 'latest', 'current'
    ];

    const query = request.query.toLowerCase();
    const hasKnowledgeKeywords = knowledgeKeywords.some(keyword => query.includes(keyword));
    const hasKnowledgeCapabilities = request.capabilities?.some(cap => 
      this.capabilities.includes(cap)
    );
    const isKnowledgeRequest = request.parameters?.toolType === 'rag';

    return hasKnowledgeKeywords || hasKnowledgeCapabilities || isKnowledgeRequest;
  }

  /**
   * Perform vector search using the search service
   */
  private async performVectorSearch(
    ragQuery: RAGQuery, 
    request: ToolRequest
  ): Promise<VectorSearchResult> {
    const searchQuery: VectorSearchQuery = {
      query: ragQuery.query,
      ...(ragQuery.maxResults !== undefined && { maxResults: ragQuery.maxResults }),
      ...(ragQuery.minRelevanceScore !== undefined && { minRelevanceScore: ragQuery.minRelevanceScore }),
      ...(ragQuery.documentTypes && { documentTypes: ragQuery.documentTypes }),
      ...(ragQuery.specialtyFilter && { specialtyFilter: ragQuery.specialtyFilter }),
      ...(ragQuery.evidenceLevels && { evidenceLevels: ragQuery.evidenceLevels }),
      ...(ragQuery.regionFilter && { regionFilter: ragQuery.regionFilter }),
      ...(ragQuery.countryFilter && { countryFilter: ragQuery.countryFilter }),
      ...(ragQuery.urgencyLevel && { urgencyLevel: ragQuery.urgencyLevel })
    };

    return await vectorSearchService.searchDocuments(
      searchQuery,
      request.agentId,
      request.sessionId
    );
  }

  /**
   * Process and enhance search results
   */
  private async processSearchResults(
    searchResults: VectorSearchResult,
    ragQuery: RAGQuery
  ): Promise<RAGResult> {
    // Filter and rank results
    let filteredDocs = searchResults.documents.filter(doc => 
      doc.relevanceScore >= ragQuery.minRelevanceScore!
    );

    // Sort by relevance score and evidence level
    filteredDocs.sort((a, b) => {
      // Primary sort: relevance score
      if (b.relevanceScore !== a.relevanceScore) {
        return b.relevanceScore - a.relevanceScore;
      }
      
      // Secondary sort: evidence level (A > B > C > D)
      const evidenceOrder = { 'A': 4, 'B': 3, 'C': 2, 'D': 1 };
      return evidenceOrder[b.evidenceLevel] - evidenceOrder[a.evidenceLevel];
    });

    // Limit results
    filteredDocs = filteredDocs.slice(0, ragQuery.maxResults);

    // Generate citations
    const citations = this.generateCitations(filteredDocs, ragQuery.citationFormat);

    // Assess evidence quality
    const evidenceQuality = this.assessEvidenceQuality(filteredDocs);

    // Generate recommendations
    const recommendations = this.generateRecommendations(filteredDocs, ragQuery);

    return {
      documents: filteredDocs,
      summary: '', // Will be generated separately
      totalResults: filteredDocs.length,
      searchTime: searchResults.searchTime,
      relevanceScores: filteredDocs.map(doc => doc.relevanceScore),
      sources: [...new Set(filteredDocs.map(doc => doc.source))],
      citations,
      evidenceQuality,
      recommendations
    };
  }

  /**
   * Generate enhanced contextual summary
   */
  private generateEnhancedSummary(result: RAGResult, query: RAGQuery): string {
    if (result.documents.length === 0) {
      return 'No relevant medical knowledge found for the specified query. Consider broadening search terms or checking alternative sources.';
    }

    const highQualityDocs = result.documents.filter(doc => 
      doc.evidenceLevel === 'A' || doc.evidenceLevel === 'B'
    );

    const specialties = [...new Set(result.documents.map(doc => doc.specialty))];
    const documentTypes = [...new Set(result.documents.map(doc => doc.documentType))];

    let summary = `Found ${result.documents.length} relevant medical documents`;
    
    if (highQualityDocs.length > 0) {
      summary += ` (${highQualityDocs.length} high-quality evidence)`;
    }
    
    if (specialties.length > 0) {
      summary += ` covering ${specialties.join(', ')}`;
    }
    
    if (documentTypes.length > 0) {
      summary += ` including ${documentTypes.join(', ')}`;
    }

    const avgRelevance = result.relevanceScores.reduce((a, b) => a + b, 0) / result.relevanceScores.length;
    summary += `. Average relevance: ${(avgRelevance * 100).toFixed(1)}%`;

    summary += `. Evidence quality: ${result.evidenceQuality}`;

    if (result.recommendations.length > 0) {
      summary += `\n\nKey recommendations:\n${result.recommendations.map(rec => `• ${rec}`).join('\n')}`;
    }

    return summary;
  }

  /**
   * Generate citations for retrieved documents
   */
  private generateCitations(
    documents: RetrievedDocument[], 
    format: string = 'simple'
  ): ToolCitation[] {
    return documents.map(doc => ({
      source: doc.source,
      title: doc.title,
      ...(doc.sourceUrl && { url: doc.sourceUrl }),
      evidenceLevel: doc.evidenceLevel,
      relevanceScore: doc.relevanceScore,
      excerpt: doc.content.substring(0, 200) + '...'
    }));
  }

  /**
   * Assess overall evidence quality
   */
  private assessEvidenceQuality(documents: RetrievedDocument[]): string {
    if (documents.length === 0) return 'No evidence';

    const evidenceScores = { 'A': 4, 'B': 3, 'C': 2, 'D': 1 };
    const avgScore = documents.reduce((sum, doc) => 
      sum + (evidenceScores[doc.evidenceLevel] || 2), 0
    ) / documents.length;

    if (avgScore >= 3.5) return 'High quality evidence';
    if (avgScore >= 2.5) return 'Moderate quality evidence';
    if (avgScore >= 1.5) return 'Limited quality evidence';
    return 'Low quality evidence';
  }

  /**
   * Generate actionable recommendations
   */
  private generateRecommendations(
    documents: RetrievedDocument[], 
    query: RAGQuery
  ): string[] {
    const recommendations: string[] = [];

    if (documents.length === 0) {
      recommendations.push('Consider consulting additional medical databases or specialist resources');
      return recommendations;
    }

    const highQualityDocs = documents.filter(doc => doc.evidenceLevel === 'A' || doc.evidenceLevel === 'B');
    if (highQualityDocs.length > 0) {
      recommendations.push(`Prioritize guidance from ${highQualityDocs.length} high-quality evidence sources`);
    }

    const recentDocs = documents.filter(doc => {
      const lastUpdated = new Date(doc.lastUpdated);
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
      return lastUpdated > oneYearAgo;
    });

    if (recentDocs.length > 0) {
      recommendations.push(`${recentDocs.length} sources contain recent updates within the last year`);
    }

    if (query.urgencyLevel === 'critical' || query.urgencyLevel === 'high') {
      recommendations.push('For urgent cases, verify recommendations with current clinical protocols');
    }

    return recommendations;
  }

  /**
   * Calculate confidence score for the RAG results
   */
  private calculateConfidence(result: RAGResult): number {
    if (result.documents.length === 0) return 0;

    const avgRelevance = result.relevanceScores.reduce((a, b) => a + b, 0) / result.relevanceScores.length;
    const highQualityRatio = result.documents.filter(doc => 
      doc.evidenceLevel === 'A' || doc.evidenceLevel === 'B'
    ).length / result.documents.length;

    const resultCountFactor = Math.min(1, result.documents.length / 3); // Prefer 3+ results

    return Math.min(1, avgRelevance * 0.5 + highQualityRatio * 0.3 + resultCountFactor * 0.2);
  }

  /**
   * Health check for RAG tool
   */
  async healthCheck(): Promise<{ healthy: boolean; details: string }> {
    try {
      const vectorSearchHealth = await vectorSearchService.healthCheck();
      
      if (!vectorSearchHealth.healthy) {
        return {
          healthy: false,
          details: `Vector search service unhealthy: ${vectorSearchHealth.details}`
        };
      }

      return {
        healthy: true,
        details: 'RAG tool operational with vector search service'
      };

    } catch (error) {
      return {
        healthy: false,
        details: `RAG tool health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}

export default RAGTool;
