# Batch TypeScript Error Fix Scripts

## Analysis of Batchable Error Patterns

Based on the 773 compilation errors, I've identified several patterns that can be fixed with automated scripts:

### Pattern 1: exactOptionalPropertyTypes Violations (~250 errors)
**Error Pattern**: `Consider adding 'undefined' to the types of the target's properties`
**Files Affected**: ~50 files
**Batch Fixable**: YES

### Pattern 2: Readonly Property Assignment (~150 errors) 
**Error Pattern**: `Cannot assign to 'X' because it is a read-only property`
**Common Cases**:
- `result.errors = [...result.errors, 'new error']`
- `result.warnings = [...result.warnings, 'new warning']`
**Batch Fixable**: YES

### Pattern 3: Array Mutation on Readonly (~80 errors)
**Error Pattern**: `Property 'push' does not exist on type 'readonly string[]'`
**Common Cases**:
- `suggestions.cacheSize = [...suggestions.cacheSize, 'item']`
- Array push operations on readonly arrays
**Batch Fixable**: YES

### Pattern 4: Missing Property Access (~100 errors)
**Error Pattern**: `Property 'X' does not exist on type 'Y'`
**Common Cases**:
- `config.languages` → `config.country.languages`
- `config.healthcareSystem` → `config.healthcare`
- `config.regulatory` → needs to be added to interface
**Batch Fixable**: PARTIALLY (need to verify correct property paths)

## Batch Fix Scripts

### Script 1: Fix exactOptionalPropertyTypes Violations
```powershell
# PowerShell script to add | undefined to interface properties
$files = Get-ChildItem -Path "src" -Recurse -Include "*.ts","*.tsx" | Where-Object { 
    (Get-Content $_.FullName -Raw) -match "exactOptionalPropertyTypes.*true" 
}

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    
    # Fix common patterns
    $content = $content -replace "email: string;", "email: string | undefined;"
    $content = $content -replace "ip_address: string;", "ip_address: string | undefined;"
    $content = $content -replace "user_agent: string;", "user_agent: string | undefined;"
    $content = $content -replace "session_id: string;", "session_id: string | undefined;"
    $content = $content -replace "content: string;", "content: string | undefined;"
    $content = $content -replace "userId: string;", "userId: string | undefined;"
    $content = $content -replace "transcription: string;", "transcription: string | undefined;"
    
    Set-Content $file.FullName $content
}
```

### Script 2: Fix Readonly Property Assignments
```powershell
# PowerShell script to fix readonly property assignments
$files = Get-ChildItem -Path "src" -Recurse -Include "*.ts","*.tsx"

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    
    # Fix result.errors assignments
    $content = $content -replace "result\.errors = \[\.\.\.result\.errors, ([^\]]+)\];", 
        "result = { ...result, errors: [...result.errors, `$1] };"
    
    # Fix result.warnings assignments  
    $content = $content -replace "result\.warnings = \[\.\.\.result\.warnings, ([^\]]+)\];",
        "result = { ...result, warnings: [...result.warnings, `$1] };"
    
    Set-Content $file.FullName $content
}
```

### Script 3: Fix Array Mutation on Readonly Arrays
```powershell
# PowerShell script to fix array mutations
$files = Get-ChildItem -Path "src" -Recurse -Include "*.ts","*.tsx"

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    
    # Fix suggestions array assignments
    $content = $content -replace "suggestions\.(\w+) = \[\.\.\.suggestions\.\1, ([^\]]+)\];",
        "suggestions = { ...suggestions, `$1: [...suggestions.`$1, `$2] };"
    
    Set-Content $file.FullName $content
}
```

### Script 4: Fix Regional Configuration Property Access
```powershell
# PowerShell script to fix config property access patterns
$files = Get-ChildItem -Path "src/tests/regional" -Recurse -Include "*.test.ts"

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    
    # Fix config.languages → config.country.languages
    $content = $content -replace "config\.languages", "config.country.languages"
    
    # Fix config.healthcareSystem → config.healthcare
    $content = $content -replace "config\.healthcareSystem", "config.healthcare"
    
    # Fix config.cultural.communicationStyles → config.cultural.communicationStyle
    $content = $content -replace "config\.cultural\.communicationStyles", "config.cultural.communicationStyle"
    
    # Fix config.cultural.primaryCultures (needs to be added to interface)
    $content = $content -replace "config\.cultural\.primaryCultures", "config.cultural.healthBeliefs"
    
    # Fix config.cultural.familyStructures (needs to be added to interface)  
    $content = $content -replace "config\.cultural\.familyStructures", "config.cultural.communicationStyle"
    
    # Fix config.emergency.responseTimeTarget → config.emergency.protocols.responseTime
    $content = $content -replace "config\.emergency\.responseTimeTarget", "parseInt(config.emergency.protocols.responseTime)"
    
    Set-Content $file.FullName $content
}
```

### Script 5: Fix Missing Import Statements
```powershell
# PowerShell script to add missing imports
$files = Get-ChildItem -Path "src" -Recurse -Include "*.ts","*.tsx"

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    
    # Add ToolRequest import where ToolResponse is imported
    if ($content -match "import.*ToolResponse.*from" -and $content -notmatch "ToolRequest") {
        $content = $content -replace "(import\s*{[^}]*)(ToolResponse)([^}]*})", "`$1ToolRequest, `$2`$3"
    }
    
    # Add ConversationMessage import where other BaseAgent imports exist
    if ($content -match "import.*from.*BaseAgent" -and $content -notmatch "ConversationMessage") {
        $content = $content -replace "(import\s*{[^}]*)(}\s*from.*BaseAgent)", "ConversationMessage, `$1`$2"
    }
    
    Set-Content $file.FullName $content
}
```

## Execution Plan

### Step 1: Run Targeted Scripts (Safest)
1. **Script 4**: Fix regional configuration property access (21 errors in 1 file)
2. **Script 5**: Add missing imports (estimated 50+ errors)
3. **Script 1**: Fix exactOptionalPropertyTypes (estimated 100+ errors)

### Step 2: Run Structure Scripts (Medium Risk)
4. **Script 2**: Fix readonly property assignments (estimated 50+ errors)
5. **Script 3**: Fix array mutations (estimated 30+ errors)

### Step 3: Validate and Test
6. Run `npm run build` to check error reduction
7. Fix any script-introduced issues
8. Proceed with remaining manual fixes

## Expected Impact
- **Total Errors Targeted**: ~400 out of 773 errors (52%)
- **Estimated Success Rate**: 80-90% for targeted patterns
- **Time Savings**: 4-6 hours of manual fixing reduced to 30 minutes
- **Risk Level**: Low to Medium (scripts target specific patterns)

## Safety Measures
1. **Backup**: Create git commit before running scripts
2. **Incremental**: Run one script at a time
3. **Validation**: Check build after each script
4. **Rollback**: Use git reset if issues occur
