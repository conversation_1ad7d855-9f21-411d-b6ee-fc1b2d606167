/**
 * COMPREHENSIVE UNIT TESTS FOR MEDICAL DATA VALIDATOR
 * 
 * This test suite provides comprehensive coverage for the medical data validator
 * with focus on:
 * - Input sanitization and validation
 * - Medical terminology validation
 * - Security against injection attacks
 * - HIPAA compliance for data handling
 * - Emergency data validation
 * - Performance optimization
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - All medical data must be properly validated
 * - Malicious input must be detected and blocked
 * - Medical terminology must be verified
 * - Emergency data must have special validation rules
 */

// Vitest globals are available via vitest.config.js globals: true
import medicalDataValidator from '../../utils/medicalDataValidator';

describe('MedicalDataValidator', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Medical Condition Validation', () => {
    describe('validateCondition', () => {
      it('should validate known medical conditions', () => {
        // Arrange
        const validConditions = [
          'Hypertension',
          'Diabetes',
          'Asthma',
          'Arthritis',
          'Depression'
        ];

        // Act & Assert
        validConditions.forEach(condition => {
          const result = medicalDataValidator.validateCondition(condition);
          expect(result.valid).toBe(true);
          expect(result.sanitized).toBe(condition);
          expect(result.isKnownCondition).toBe(true);
        });
      });

      it('should handle unknown medical conditions with warnings', () => {
        // Arrange
        const unknownCondition = 'Rare Genetic Disorder XYZ';

        // Act
        const result = medicalDataValidator.validateCondition(unknownCondition);

        // Assert
        expect(result.valid).toBe(true);
        expect(result.sanitized).toBe(unknownCondition);
        expect(result.isKnownCondition).toBe(false);
        expect(result.warning).toContain('Unknown condition');
      });

      it('should reject empty or null condition names', () => {
        // Arrange
        const invalidInputs = [null, undefined, '', '   '];

        // Act & Assert
        invalidInputs.forEach(input => {
          const result = medicalDataValidator.validateCondition(input);
          expect(result.valid).toBe(false);
          expect(result.error).toContain('required');
        });
      });

      it('should sanitize condition names with special characters', () => {
        // Arrange
        const conditionWithSpecialChars = 'Diabetes<script>alert("xss")</script>';

        // Act
        const result = medicalDataValidator.validateCondition(conditionWithSpecialChars);

        // Assert
        expect(result.valid).toBe(true);
        expect(result.sanitized).not.toContain('<script>');
        expect(result.sanitized).toBe('Diabetes');
      });

      it('should reject excessively long condition names', () => {
        // Arrange
        const longCondition = 'A'.repeat(256); // Exceeds max length

        // Act
        const result = medicalDataValidator.validateCondition(longCondition);

        // Assert
        expect(result.valid).toBe(false);
        expect(result.error).toContain('too long');
      });

      it('should handle case-insensitive condition matching', () => {
        // Arrange
        const conditionVariations = [
          'hypertension',
          'HYPERTENSION',
          'HyPeRtEnSiOn'
        ];

        // Act & Assert
        conditionVariations.forEach(condition => {
          const result = medicalDataValidator.validateCondition(condition);
          expect(result.valid).toBe(true);
          expect(result.isKnownCondition).toBe(true);
        });
      });

      it('should validate emergency conditions with special handling', () => {
        // Arrange
        const emergencyConditions = [
          'Chest Pain',
          'Stroke',
          'Heart Attack',
          'Severe Allergic Reaction'
        ];

        // Act & Assert
        emergencyConditions.forEach(condition => {
          const result = medicalDataValidator.validateCondition(condition, { isEmergency: true });
          expect(result.valid).toBe(true);
          expect(result.isEmergencyCondition).toBe(true);
          expect(result.priority).toBe('critical');
        });
      });
    });
  });

  describe('Medication Validation', () => {
    describe('validateMedication', () => {
      it('should validate known medications', () => {
        // Arrange
        const validMedications = [
          'Lisinopril',
          'Metformin',
          'Aspirin',
          'Ibuprofen',
          'Acetaminophen'
        ];

        // Act & Assert
        validMedications.forEach(medication => {
          const result = medicalDataValidator.validateMedication(medication);
          expect(result.valid).toBe(true);
          expect(result.sanitized).toBe(medication);
          expect(result.isKnownMedication).toBe(true);
        });
      });

      it('should handle unknown medications with warnings', () => {
        // Arrange
        const unknownMedication = 'Experimental Drug ABC123';

        // Act
        const result = medicalDataValidator.validateMedication(unknownMedication);

        // Assert
        expect(result.valid).toBe(true);
        expect(result.sanitized).toBe(unknownMedication);
        expect(result.isKnownMedication).toBe(false);
        expect(result.warning).toContain('Unknown medication');
      });

      it('should reject empty or null medication names', () => {
        // Arrange
        const invalidInputs = [null, undefined, '', '   '];

        // Act & Assert
        invalidInputs.forEach(input => {
          const result = medicalDataValidator.validateMedication(input);
          expect(result.valid).toBe(false);
          expect(result.error).toContain('required');
        });
      });

      it('should sanitize medication names with malicious content', () => {
        // Arrange
        const maliciousMedication = 'Aspirin"; DROP TABLE medications; --';

        // Act
        const result = medicalDataValidator.validateMedication(maliciousMedication);

        // Assert
        expect(result.valid).toBe(true);
        expect(result.sanitized).not.toContain('DROP TABLE');
        expect(result.sanitized).not.toContain('--');
      });

      it('should validate emergency medications with priority', () => {
        // Arrange
        const emergencyMedications = [
          'Epinephrine',
          'Nitroglycerin',
          'Albuterol',
          'Naloxone'
        ];

        // Act & Assert
        emergencyMedications.forEach(medication => {
          const result = medicalDataValidator.validateMedication(medication, { isEmergency: true });
          expect(result.valid).toBe(true);
          expect(result.isEmergencyMedication).toBe(true);
          expect(result.requiresMonitoring).toBe(true);
        });
      });

      it('should validate medication dosage formats', () => {
        // Arrange
        const validDosages = [
          '10mg',
          '5.5mg',
          '1 tablet',
          '2 capsules',
          '1ml',
          '0.5mg/kg'
        ];

        // Act & Assert
        validDosages.forEach(dosage => {
          const result = medicalDataValidator.validateDosage(dosage);
          expect(result.valid).toBe(true);
          expect(result.sanitized).toBe(dosage);
        });
      });

      it('should reject invalid dosage formats', () => {
        // Arrange
        const invalidDosages = [
          'take some',
          '999999999mg',
          'javascript:alert(1)',
          '<script>alert("xss")</script>'
        ];

        // Act & Assert
        invalidDosages.forEach(dosage => {
          const result = medicalDataValidator.validateDosage(dosage);
          expect(result.valid).toBe(false);
          expect(result.error).toBeDefined();
        });
      });
    });
  });

  describe('Medical Notes Validation', () => {
    describe('validateMedicalNotes', () => {
      it('should validate normal medical notes', () => {
        // Arrange
        const validNotes = 'Patient reports feeling better after medication adjustment.';

        // Act
        const result = medicalDataValidator.validateMedicalNotes(validNotes);

        // Assert
        expect(result.valid).toBe(true);
        expect(result.sanitized).toBe(validNotes);
      });

      it('should handle empty notes gracefully', () => {
        // Arrange
        const emptyNotes = [null, undefined, ''];

        // Act & Assert
        emptyNotes.forEach(notes => {
          const result = medicalDataValidator.validateMedicalNotes(notes);
          expect(result.valid).toBe(true);
          expect(result.sanitized).toBe('');
        });
      });

      it('should sanitize notes with HTML content', () => {
        // Arrange
        const notesWithHTML = 'Patient has <b>severe</b> pain in <script>alert("xss")</script> chest area.';

        // Act
        const result = medicalDataValidator.validateMedicalNotes(notesWithHTML);

        // Assert
        expect(result.valid).toBe(true);
        expect(result.sanitized).not.toContain('<script>');
        expect(result.sanitized).not.toContain('<b>');
        expect(result.sanitized).toContain('severe');
        expect(result.sanitized).toContain('chest area');
      });

      it('should reject excessively long notes', () => {
        // Arrange
        const longNotes = 'A'.repeat(5001); // Exceeds max length

        // Act
        const result = medicalDataValidator.validateMedicalNotes(longNotes);

        // Assert
        expect(result.valid).toBe(false);
        expect(result.error).toContain('too long');
      });

      it('should detect and flag potential PHI in notes', () => {
        // Arrange
        const notesWithPHI = 'Patient John Doe, SSN: ***********, called about symptoms.';

        // Act
        const result = medicalDataValidator.validateMedicalNotes(notesWithPHI);

        // Assert
        expect(result.valid).toBe(true);
        expect(result.warning).toContain('potential PHI');
        expect(result.sanitized).not.toContain('***********');
      });
    });
  });

  describe('Security and Injection Prevention', () => {
    it('should prevent SQL injection attempts', () => {
      // Arrange
      const sqlInjectionAttempts = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "UNION SELECT * FROM passwords",
        "'; INSERT INTO admin VALUES('hacker'); --"
      ];

      // Act & Assert
      sqlInjectionAttempts.forEach(attempt => {
        const result = medicalDataValidator.validateCondition(attempt);
        expect(result.valid).toBe(false);
        expect(result.error).toContain('invalid characters');
      });
    });

    it('should prevent XSS attacks', () => {
      // Arrange
      const xssAttempts = [
        '<script>alert("xss")</script>',
        'javascript:alert(1)',
        '<img src="x" onerror="alert(1)">',
        '<iframe src="javascript:alert(1)"></iframe>'
      ];

      // Act & Assert
      xssAttempts.forEach(attempt => {
        const result = medicalDataValidator.validateMedicalNotes(attempt);
        expect(result.valid).toBe(true);
        expect(result.sanitized).not.toContain('<script>');
        expect(result.sanitized).not.toContain('javascript:');
        expect(result.sanitized).not.toContain('<iframe>');
      });
    });

    it('should prevent NoSQL injection attempts', () => {
      // Arrange
      const noSQLInjectionAttempts = [
        '{"$ne": null}',
        '{"$gt": ""}',
        '{"$where": "this.password.match(/.*/)"}',
        '{"$regex": ".*"}'
      ];

      // Act & Assert
      noSQLInjectionAttempts.forEach(attempt => {
        const result = medicalDataValidator.validateCondition(attempt);
        expect(result.valid).toBe(false);
        expect(result.error).toContain('invalid characters');
      });
    });
  });

  describe('Performance and Optimization', () => {
    it('should validate large batches of data efficiently', () => {
      // Arrange
      const startTime = performance.now();
      const largeBatch = Array(1000).fill().map((_, i) => `Condition ${i}`);

      // Act
      const results = largeBatch.map(condition => 
        medicalDataValidator.validateCondition(condition)
      );

      const endTime = performance.now();
      const executionTime = endTime - startTime;

      // Assert
      expect(results.length).toBe(1000);
      expect(results.every(result => result.valid)).toBe(true);
      expect(executionTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should cache validation results for repeated inputs', () => {
      // Arrange
      const condition = 'Hypertension';

      // Act - First validation
      const result1 = medicalDataValidator.validateCondition(condition);
      // Act - Second validation (should use cache)
      const result2 = medicalDataValidator.validateCondition(condition);

      // Assert
      expect(result1.valid).toBe(true);
      expect(result2.valid).toBe(true);
      expect(result2.cached).toBe(true);
    });
  });

  describe('HIPAA Compliance', () => {
    it('should not log sensitive medical data during validation', () => {
      // Arrange
      const sensitiveCondition = 'HIV/AIDS';
      const consoleSpy = vi.spyOn(console, 'log');

      // Act
      medicalDataValidator.validateCondition(sensitiveCondition);

      // Assert
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining(sensitiveCondition)
      );
    });

    it('should handle validation errors without exposing sensitive data', () => {
      // Arrange
      const sensitiveData = 'Patient has HIV and depression';

      // Act
      const result = medicalDataValidator.validateMedicalNotes(sensitiveData);

      // Assert
      if (!result.valid) {
        expect(result.error).not.toContain('HIV');
        expect(result.error).not.toContain('depression');
      }
    });
  });
});
