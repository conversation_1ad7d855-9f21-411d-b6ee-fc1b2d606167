/**
 * PATIENT ROUTES MODULE
 * 
 * Patient-specific routes with role-based access control and medical data management.
 * Includes fallback components for offline scenarios.
 */

import React from 'react';
import { createLazyComponent } from '../../utils/lazyLoading';

// =============================================================================
// PATIENT DASHBOARD ROUTES
// =============================================================================

export const PatientDashboard = createLazyComponent(
  () => import('../dashboard/PatientDashboard').catch(() => ({
    default: () => (
      <div className="p-8 bg-blue-50 border border-blue-200 rounded">
        <h2 className="text-blue-800 font-bold">👤 Patient Dashboard</h2>
        <p className="text-blue-700 mt-2">Your health dashboard is loading...</p>
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-blue-100 rounded">
            <h3 className="text-blue-800 font-semibold">Quick Actions</h3>
            <ul className="text-blue-700 text-sm mt-2 space-y-1">
              <li>• View medical history</li>
              <li>• Track symptoms</li>
              <li>• Manage medications</li>
              <li>• Book appointments</li>
            </ul>
          </div>
          <div className="p-4 bg-blue-100 rounded">
            <h3 className="text-blue-800 font-semibold">Health Overview</h3>
            <p className="text-blue-700 text-sm mt-2">Your health summary will appear here</p>
          </div>
        </div>
      </div>
    )
  })),
  {
    priority: 'high',
    requiredRole: 'patient'
  }
);

// =============================================================================
// MEDICAL DATA ROUTES
// =============================================================================

export const MedicalHistory = createLazyComponent(
  () => import('../medical/MedicalHistory').catch(() => ({
    default: () => (
      <div className="p-8 bg-green-50 border border-green-200 rounded">
        <h2 className="text-green-800 font-bold">📋 Medical History</h2>
        <p className="text-green-700 mt-2">Your medical records are loading...</p>
        <div className="mt-4 space-y-3">
          <div className="p-3 bg-green-100 rounded">
            <h3 className="text-green-800 font-semibold">Recent Visits</h3>
            <p className="text-green-700 text-sm">Your recent medical visits will appear here</p>
          </div>
          <div className="p-3 bg-green-100 rounded">
            <h3 className="text-green-800 font-semibold">Diagnoses</h3>
            <p className="text-green-700 text-sm">Your medical diagnoses will appear here</p>
          </div>
          <div className="p-3 bg-green-100 rounded">
            <h3 className="text-green-800 font-semibold">Test Results</h3>
            <p className="text-green-700 text-sm">Your lab and test results will appear here</p>
          </div>
        </div>
      </div>
    )
  })),
  {
    priority: 'normal',
    requiredRole: 'patient'
  }
);

export const SymptomTracker = createLazyComponent(
  () => import('../medical/SymptomTracker').catch(() => ({
    default: () => (
      <div className="p-8 bg-purple-50 border border-purple-200 rounded">
        <h2 className="text-purple-800 font-bold">📊 Symptom Tracker</h2>
        <p className="text-purple-700 mt-2">Symptom tracking interface is loading...</p>
        <div className="mt-4 space-y-3">
          <div className="p-3 bg-purple-100 rounded">
            <h3 className="text-purple-800 font-semibold">Track Your Symptoms</h3>
            <p className="text-purple-700 text-sm">Record and monitor your symptoms over time</p>
          </div>
          <div className="p-3 bg-purple-100 rounded">
            <h3 className="text-purple-800 font-semibold">Symptom Patterns</h3>
            <p className="text-purple-700 text-sm">Identify patterns and triggers in your symptoms</p>
          </div>
        </div>
      </div>
    )
  })),
  {
    priority: 'normal',
    requiredRole: 'patient'
  }
);

export const MedicationManager = createLazyComponent(
  () => import('../medical/MedicationManager').catch(() => ({
    default: () => (
      <div className="p-8 bg-orange-50 border border-orange-200 rounded">
        <h2 className="text-orange-800 font-bold">💊 Medication Manager</h2>
        <p className="text-orange-700 mt-2">Medication management system is loading...</p>
        <div className="mt-4 space-y-3">
          <div className="p-3 bg-orange-100 rounded">
            <h3 className="text-orange-800 font-semibold">Current Medications</h3>
            <p className="text-orange-700 text-sm">View and manage your current medications</p>
          </div>
          <div className="p-3 bg-orange-100 rounded">
            <h3 className="text-orange-800 font-semibold">Medication Reminders</h3>
            <p className="text-orange-700 text-sm">Set up reminders for taking your medications</p>
          </div>
          <div className="p-3 bg-orange-100 rounded">
            <h3 className="text-orange-800 font-semibold">Refill Tracking</h3>
            <p className="text-orange-700 text-sm">Track when you need medication refills</p>
          </div>
        </div>
      </div>
    )
  })),
  {
    priority: 'normal',
    requiredRole: 'patient'
  }
);

// =============================================================================
// APPOINTMENT AND CONSULTATION ROUTES
// =============================================================================

export const AppointmentBooking = createLazyComponent(
  () => import('../appointments/AppointmentBooking').catch(() => ({
    default: () => (
      <div className="p-8 bg-indigo-50 border border-indigo-200 rounded">
        <h2 className="text-indigo-800 font-bold">📅 Appointment Booking</h2>
        <p className="text-indigo-700 mt-2">Appointment booking system is loading...</p>
        <div className="mt-4 space-y-3">
          <div className="p-3 bg-indigo-100 rounded">
            <h3 className="text-indigo-800 font-semibold">Book New Appointment</h3>
            <p className="text-indigo-700 text-sm">Schedule appointments with healthcare providers</p>
          </div>
          <div className="p-3 bg-indigo-100 rounded">
            <h3 className="text-indigo-800 font-semibold">Upcoming Appointments</h3>
            <p className="text-indigo-700 text-sm">View your scheduled appointments</p>
          </div>
          <div className="p-3 bg-indigo-100 rounded">
            <h3 className="text-indigo-800 font-semibold">Appointment History</h3>
            <p className="text-indigo-700 text-sm">Review your past appointments</p>
          </div>
        </div>
      </div>
    )
  })),
  {
    priority: 'normal',
    requiredRole: 'patient'
  }
);

// =============================================================================
// HEALTH MONITORING ROUTES
// =============================================================================

export const HealthMetrics = createLazyComponent(
  () => import('../medical/HealthMetrics').catch(() => ({
    default: () => (
      <div className="p-8 bg-teal-50 border border-teal-200 rounded">
        <h2 className="text-teal-800 font-bold">📈 Health Metrics</h2>
        <p className="text-teal-700 mt-2">Health metrics dashboard is loading...</p>
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-3 bg-teal-100 rounded">
            <h3 className="text-teal-800 font-semibold">Vital Signs</h3>
            <p className="text-teal-700 text-sm">Track your blood pressure, heart rate, and more</p>
          </div>
          <div className="p-3 bg-teal-100 rounded">
            <h3 className="text-teal-800 font-semibold">Health Trends</h3>
            <p className="text-teal-700 text-sm">View trends in your health data over time</p>
          </div>
        </div>
      </div>
    )
  })),
  { 
    priority: 'normal',
    requiredRole: 'patient'
  }
);

// =============================================================================
// PATIENT PROFILE ROUTES
// =============================================================================

export const ProfileSettings = createLazyComponent(
  () => import('../profile/ProfileSettings').catch(() => ({
    default: () => (
      <div className="p-8 bg-gray-50 border border-gray-200 rounded">
        <h2 className="text-gray-800 font-bold">⚙️ Profile Settings</h2>
        <p className="text-gray-700 mt-2">Profile settings are loading...</p>
        <div className="mt-4 space-y-3">
          <div className="p-3 bg-gray-100 rounded">
            <h3 className="text-gray-800 font-semibold">Personal Information</h3>
            <p className="text-gray-700 text-sm">Update your personal details</p>
          </div>
          <div className="p-3 bg-gray-100 rounded">
            <h3 className="text-gray-800 font-semibold">Privacy Settings</h3>
            <p className="text-gray-700 text-sm">Manage your privacy preferences</p>
          </div>
          <div className="p-3 bg-gray-100 rounded">
            <h3 className="text-gray-800 font-semibold">Notification Preferences</h3>
            <p className="text-gray-700 text-sm">Configure your notification settings</p>
          </div>
        </div>
      </div>
    )
  })),
  { 
    priority: 'normal',
    requiredRole: 'patient'
  }
);

// =============================================================================
// PATIENT ROUTE UTILITIES
// =============================================================================

/**
 * Preload patient-specific routes
 */
export function preloadPatientRoutes(): void {
  console.log('👤 Preloading patient routes...');
  
  const patientComponents = [
    PatientDashboard,
    MedicalHistory,
    SymptomTracker,
    MedicationManager,
    AppointmentBooking
  ];

  patientComponents.forEach((Component, index) => {
    try {
      React.createElement(Component);
      console.log(`✅ Patient component ${index + 1}/${patientComponents.length} preloaded`);
    } catch (error) {
      console.warn(`⚠️ Failed to preload patient component ${index + 1}:`, error);
    }
  });
}

/**
 * Get patient route priorities
 */
export function getPatientRoutePriorities(): Record<string, 'low' | 'normal' | 'high'> {
  return {
    'PatientDashboard': 'high',
    'MedicalHistory': 'normal',
    'SymptomTracker': 'normal',
    'MedicationManager': 'normal',
    'AppointmentBooking': 'normal',
    'HealthMetrics': 'normal',
    'ProfileSettings': 'normal'
  };
}
