/// <reference types="vitest/globals" />

import 'vitest/globals';

// Ensure vitest globals are available in TypeScript
declare global {
  const describe: typeof import('vitest').describe;
  const it: typeof import('vitest').it;
  const test: typeof import('vitest').test;
  const expect: typeof import('vitest').expect;
  const beforeEach: typeof import('vitest').beforeEach;
  const afterEach: typeof import('vitest').afterEach;
  const beforeAll: typeof import('vitest').beforeAll;
  const afterAll: typeof import('vitest').afterAll;
  const vi: typeof import('vitest').vi;

  // Additional vitest types
  type MockedFunction<T extends (...args: any[]) => any> = import('vitest').MockedFunction<T>;
  type MockInstance<T extends (...args: any[]) => any> = import('vitest').MockInstance<T>;
}
