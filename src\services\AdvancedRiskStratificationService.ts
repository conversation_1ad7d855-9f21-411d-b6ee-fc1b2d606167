/**
 * ADVANCED RISK STRATIFICATION SERVICE (REFACTORED)
 *
 * Main orchestrator for sophisticated risk assessment with regional disease patterns,
 * socioeconomic factors, and predictive analytics for African healthcare contexts.
 * 
 * This service has been refactored into modular components for better maintainability:
 * - RiskCalculationService: Core risk calculation algorithms
 * - RegionalRiskService: Regional and environmental risk factors
 * - Type definitions moved to ../types/riskAssessment.ts
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { wrapWithPerformanceMonitoring } from '../utils/performanceMonitoringWrapper';
import { handleServiceError } from '../utils/standardErrorHandler';
import { authenticationService } from './AuthenticationService';

// Import modular risk services
import { riskCalculationService } from './risk/RiskCalculationService';
import { regionalRiskService } from './risk/RegionalRiskService';

// Import types
import type {
  RiskAssessmentRequest,
  RiskAssessmentResult,
  EmergencyRiskAssessment,
  PatientDemographics,
  VitalSigns,
  UrgentAction,
  ModifiableRiskFactor,
  NonModifiableRiskFactor
} from '../types/riskAssessment';

export class AdvancedRiskStratificationService {
  private supabase: SupabaseClient;
  private riskModelCache: Map<string, any> = new Map();
  private regionalDataCache: Map<string, any> = new Map();
  private readonly cacheTimeout = 30 * 60 * 1000; // 30 minutes

  constructor() {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing for advanced risk stratification');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);

    // Apply performance monitoring to critical methods
    this.performRiskAssessment = wrapWithPerformanceMonitoring(
      this.performRiskAssessment.bind(this),
      {
        operation: 'risk_assessment_analysis',
        emergencyOperation: true,
        target: 2000, // 2 second emergency requirement
        includeMetadata: false
      },
      'AdvancedRiskStratificationService',
      'performRiskAssessment'
    );
  }

  /**
   * Perform comprehensive risk assessment
   */
  async performRiskAssessment(request: RiskAssessmentRequest): Promise<RiskAssessmentResult> {
    const startTime = performance.now();

    try {
      console.log(`🔍 Starting risk assessment for patient ${request.patientId}...`);

      // Step 1: Load regional risk models
      const regionalModels = await regionalRiskService.loadRegionalRiskModels(
        request.demographics.country
      );

      // Step 2: Calculate condition-specific risks
      const conditionRisks = await riskCalculationService.calculateConditionSpecificRisks(
        request,
        regionalModels
      );

      // Step 3: Assess regional and environmental factors
      const regionalFactors = await regionalRiskService.assessRegionalRiskFactors(
        request.demographics,
        request.environmentalFactors
      );

      // Step 4: Analyze modifiable and non-modifiable factors
      const { modifiableFactors, nonModifiableFactors } = await this.analyzeRiskFactors(
        request
      );

      // Step 5: Generate predictive analytics
      const predictiveAnalytics = await this.generatePredictiveAnalytics(
        request,
        conditionRisks
      );

      // Step 6: Calculate overall risk score
      const overallRiskScore = riskCalculationService.calculateOverallRiskScore(
        conditionRisks,
        regionalFactors,
        modifiableFactors,
        nonModifiableFactors
      );

      // Step 7: Generate recommendations
      const recommendations = await this.generateRiskRecommendations(
        request,
        conditionRisks,
        modifiableFactors
      );

      // Step 8: Identify urgent actions
      const urgentActions = this.identifyUrgentActions(
        conditionRisks,
        predictiveAnalytics,
        request
      );

      // Step 9: Create follow-up schedule
      const followUpSchedule = this.createFollowUpSchedule(
        overallRiskScore,
        conditionRisks,
        request.assessmentType
      );

      // Step 10: Add cultural considerations
      const culturalConsiderations = await this.generateCulturalConsiderations(
        request.culturalFactors,
        recommendations
      );

      const processingTime = performance.now() - startTime;

      const result: RiskAssessmentResult = {
        overallRiskScore,
        riskCategory: riskCalculationService.categorizeRisk(overallRiskScore),
        conditionSpecificRisks: conditionRisks,
        regionalRiskFactors: regionalFactors,
        modifiableRiskFactors: modifiableFactors,
        nonModifiableRiskFactors: nonModifiableFactors,
        predictiveAnalytics,
        recommendations,
        urgentActions,
        followUpSchedule,
        culturalConsiderations,
        assessmentMetadata: {
          assessmentDate: new Date(),
          assessmentVersion: '3.0',
          dataCompleteness: this.calculateDataCompleteness(request),
          confidenceLevel: this.calculateConfidenceLevel(request, regionalModels),
          limitationsNoted: this.identifyLimitations(request),
          dataSourcesUsed: ['regional_models', 'clinical_guidelines', 'epidemiological_data'],
          processingTime
        }
      };

      console.log(`✅ Risk assessment completed in ${processingTime.toFixed(2)}ms - Overall risk: ${result.riskCategory}`);

      return result;

    } catch (error) {
      throw handleServiceError(
        error,
        'AdvancedRiskStratificationService',
        'performRiskAssessment',
        request.patientId,
        request.sessionId
      );
    }
  }

  /**
   * Quick emergency risk assessment for urgent situations
   */
  async performEmergencyRiskAssessment(
    symptoms: string[],
    vitalSigns: VitalSigns,
    demographics: PatientDemographics,
    medicalHistory: string[]
  ): Promise<EmergencyRiskAssessment> {
    const startTime = performance.now();

    try {
      console.log(`🚨 Emergency risk assessment for ${demographics.age}y ${demographics.gender}...`);

      // Load emergency-specific risk models
      const emergencyModels = await regionalRiskService.loadEmergencyRiskModels(demographics.country);

      // Calculate emergency risk score based on symptoms and vital signs
      let emergencyRiskScore = 0;

      // Symptom-based risk scoring
      emergencyRiskScore += this.calculateSymptomRiskScore(symptoms);

      // Vital signs risk scoring
      emergencyRiskScore += this.calculateVitalSignsRiskScore(vitalSigns);

      // Medical history risk scoring
      emergencyRiskScore += this.calculateMedicalHistoryRiskScore(medicalHistory);

      // Age and demographic adjustments
      emergencyRiskScore += this.calculateDemographicRiskScore(demographics);

      const emergencyRiskLevel = this.categorizeEmergencyRisk(emergencyRiskScore);

      // Generate urgent actions based on risk level
      const urgentActions = this.generateEmergencyActions(emergencyRiskLevel, symptoms, vitalSigns);

      // Calculate time to action
      const timeToAction = this.calculateTimeToAction(emergencyRiskLevel, symptoms);

      // Cultural considerations for emergency care
      const culturalConsiderations = this.getEmergencyCulturalConsiderations(demographics);

      const processingTime = performance.now() - startTime;
      console.log(`⚡ Emergency assessment completed in ${processingTime.toFixed(2)}ms - Risk: ${emergencyRiskLevel}`);

      return {
        emergencyRiskLevel,
        urgentActions,
        timeToAction,
        culturalConsiderations
      };

    } catch (error) {
      console.error('❌ Error in emergency risk assessment:', error);
      // Return conservative high-risk assessment
      return {
        emergencyRiskLevel: 'high',
        urgentActions: [{
          action: 'Seek immediate medical attention',
          timeframe: 'immediately',
          rationale: 'Emergency assessment failed - err on side of caution',
          consequences: 'Potential serious complications if delayed',
          culturalConsiderations: ['Inform family members as culturally appropriate']
        }],
        timeToAction: 5,
        culturalConsiderations: ['Emergency situation - prioritize immediate care']
      };
    }
  }

  // =====================================================
  // HELPER METHODS (Simplified implementations)
  // =====================================================

  private async analyzeRiskFactors(request: RiskAssessmentRequest): Promise<{
    modifiableFactors: ModifiableRiskFactor[];
    nonModifiableFactors: NonModifiableRiskFactor[];
  }> {
    const modifiableFactors: ModifiableRiskFactor[] = [];
    const nonModifiableFactors: NonModifiableRiskFactor[] = [];

    // Simplified implementation - in production would be more comprehensive
    if (request.behavioralFactors) {
      if (request.behavioralFactors.smokingStatus === 'current') {
        modifiableFactors.push({
          factor: 'Smoking',
          currentLevel: 'high',
          targetLevel: 'low',
          interventions: ['Smoking cessation counseling', 'Nicotine replacement therapy'],
          timeToImprovement: '3-6 months',
          difficultyLevel: 'difficult',
          culturalBarriers: ['Social smoking culture', 'Stress relief habits']
        });
      }
    }

    // Non-modifiable factors
    if (request.demographics.age > 65) {
      nonModifiableFactors.push({
        factor: 'Advanced age',
        riskContribution: 20,
        compensatoryMeasures: ['Regular health monitoring', 'Preventive care'],
        monitoringRequired: true
      });
    }

    return { modifiableFactors, nonModifiableFactors };
  }

  private async generatePredictiveAnalytics(request: RiskAssessmentRequest, conditionRisks: any): Promise<any> {
    // Simplified implementation
    return {
      diseaseProgressionRisk: {},
      hospitalizationRisk: { riskScore: 0, timeframe: 'unknown', primaryRiskFactors: [], preventiveActions: [], warningSignsToWatch: [] },
      mortalityRisk: { oneYearRisk: 0, fiveYearRisk: 0, tenYearRisk: 0, primaryContributors: [], modifiableFactors: [], interventionPriorities: [] },
      complicationRisk: {},
      treatmentResponsePrediction: {}
    };
  }

  private async generateRiskRecommendations(request: RiskAssessmentRequest, conditionRisks: any, modifiableFactors: any): Promise<any[]> {
    // Simplified implementation
    return [
      {
        category: 'lifestyle',
        recommendation: 'Maintain healthy lifestyle',
        priority: 'medium',
        timeframe: '3-6 months',
        expectedBenefit: 'Reduced overall risk',
        culturalAdaptations: ['Incorporate traditional foods'],
        barriers: [],
        alternatives: []
      }
    ];
  }

  private identifyUrgentActions(conditionRisks: any, predictiveAnalytics: any, request: RiskAssessmentRequest): UrgentAction[] {
    // Simplified implementation
    return [];
  }

  private createFollowUpSchedule(overallRiskScore: number, conditionRisks: any, assessmentType: string): any {
    // Simplified implementation
    return {
      nextAssessment: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      frequency: 'monthly',
      monitoringParameters: ['blood_pressure', 'weight'],
      escalationTriggers: ['symptom_worsening']
    };
  }

  private async generateCulturalConsiderations(culturalFactors: any, recommendations: any): Promise<string[]> {
    // Simplified implementation
    return ['Consider cultural preferences in treatment planning'];
  }

  private calculateDataCompleteness(request: RiskAssessmentRequest): number {
    // Simplified implementation
    return 75;
  }

  private calculateConfidenceLevel(request: RiskAssessmentRequest, regionalModels: any): number {
    // Simplified implementation
    return 80;
  }

  private identifyLimitations(request: RiskAssessmentRequest): string[] {
    // Simplified implementation
    return ['Limited laboratory data available'];
  }

  // Emergency assessment helper methods
  private calculateSymptomRiskScore(symptoms: string[]): number {
    // Simplified implementation
    return symptoms.length * 10;
  }

  private calculateVitalSignsRiskScore(vitalSigns: VitalSigns): number {
    // Simplified implementation
    let score = 0;
    if (vitalSigns.bloodPressure?.systolic && vitalSigns.bloodPressure.systolic > 180) score += 30;
    if (vitalSigns.heartRate && vitalSigns.heartRate > 120) score += 20;
    return score;
  }

  private calculateMedicalHistoryRiskScore(medicalHistory: string[]): number {
    // Simplified implementation
    return medicalHistory.length * 5;
  }

  private calculateDemographicRiskScore(demographics: PatientDemographics): number {
    // Simplified implementation
    return demographics.age > 65 ? 20 : 0;
  }

  private categorizeEmergencyRisk(riskScore: number): 'low' | 'moderate' | 'high' | 'critical' {
    if (riskScore >= 85) return 'critical';
    if (riskScore >= 70) return 'high';
    if (riskScore >= 50) return 'moderate';
    return 'low';
  }

  private generateEmergencyActions(riskLevel: string, symptoms: string[], vitalSigns: VitalSigns): UrgentAction[] {
    // Simplified implementation
    return [{
      action: 'Monitor vital signs',
      timeframe: 'immediately',
      rationale: 'Emergency situation requires monitoring',
      consequences: 'Potential deterioration if not monitored',
      culturalConsiderations: []
    }];
  }

  private calculateTimeToAction(riskLevel: string, symptoms: string[]): number {
    // Simplified implementation
    switch (riskLevel) {
      case 'critical': return 5;
      case 'high': return 15;
      case 'moderate': return 60;
      default: return 240;
    }
  }

  private getEmergencyCulturalConsiderations(demographics: PatientDemographics): string[] {
    // Simplified implementation
    return ['Consider family involvement in emergency decisions'];
  }
}

// Export singleton instance
export const advancedRiskStratificationService = new AdvancedRiskStratificationService();
