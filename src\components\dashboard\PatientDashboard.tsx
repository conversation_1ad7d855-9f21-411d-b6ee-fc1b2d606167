/**
 * Patient Dashboard Component
 * Main dashboard interface for patients to view their health information
 */

import React from 'react';

interface PatientDashboardProps {
  className?: string;
}

const PatientDashboard: React.FC<PatientDashboardProps> = ({ className = '' }) => {
  return (
    <div className={`p-6 bg-white ${className}`}>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Patient Dashboard</h1>
        <p className="text-gray-600">Welcome to your health management portal</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Health Overview */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h3 className="font-semibold text-blue-800 mb-2">Health Overview</h3>
          <p className="text-blue-700 text-sm">View your latest health metrics and trends</p>
          <button className="mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
            View Details
          </button>
        </div>
        
        {/* Appointments */}
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <h3 className="font-semibold text-green-800 mb-2">Appointments</h3>
          <p className="text-green-700 text-sm">Manage your upcoming appointments</p>
          <button className="mt-3 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
            Schedule
          </button>
        </div>
        
        {/* Medical Records */}
        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
          <h3 className="font-semibold text-purple-800 mb-2">Medical Records</h3>
          <p className="text-purple-700 text-sm">Access your medical history and documents</p>
          <button className="mt-3 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
            View Records
          </button>
        </div>
        
        {/* Medications */}
        <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
          <h3 className="font-semibold text-orange-800 mb-2">Medications</h3>
          <p className="text-orange-700 text-sm">Track your medications and reminders</p>
          <button className="mt-3 px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors">
            Manage
          </button>
        </div>
        
        {/* Symptoms */}
        <div className="bg-red-50 p-4 rounded-lg border border-red-200">
          <h3 className="font-semibold text-red-800 mb-2">Symptom Tracker</h3>
          <p className="text-red-700 text-sm">Log and track your symptoms</p>
          <button className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
            Log Symptoms
          </button>
        </div>
        
        {/* Emergency */}
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h3 className="font-semibold text-gray-800 mb-2">Emergency</h3>
          <p className="text-gray-700 text-sm">Quick access to emergency services</p>
          <button className="mt-3 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
            Emergency
          </button>
        </div>
      </div>
      
      <div className="mt-6 bg-yellow-50 p-3 rounded border border-yellow-200">
        <p className="text-yellow-800 text-sm">
          ⚠️ This is a placeholder component. Full patient dashboard functionality will be implemented.
        </p>
      </div>
    </div>
  );
};

export default PatientDashboard;
