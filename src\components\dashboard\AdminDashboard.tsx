/**
 * Admin Dashboard Component
 * Main dashboard interface for system administrators
 */

import React from 'react';

interface AdminDashboardProps {
  className?: string;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ className = '' }) => {
  return (
    <div className={`p-6 bg-white ${className}`}>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Admin Dashboard</h1>
        <p className="text-gray-600">System administration and management portal</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* User Management */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h3 className="font-semibold text-blue-800 mb-2">User Management</h3>
          <p className="text-blue-700 text-sm">Manage users, roles, and permissions</p>
          <button className="mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
            Manage Users
          </button>
        </div>
        
        {/* System Settings */}
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <h3 className="font-semibold text-green-800 mb-2">System Settings</h3>
          <p className="text-green-700 text-sm">Configure system settings and preferences</p>
          <button className="mt-3 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
            Settings
          </button>
        </div>
        
        {/* Analytics */}
        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
          <h3 className="font-semibold text-purple-800 mb-2">Analytics</h3>
          <p className="text-purple-700 text-sm">View system analytics and reports</p>
          <button className="mt-3 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
            View Analytics
          </button>
        </div>
      </div>
      
      <div className="mt-6 bg-yellow-50 p-3 rounded border border-yellow-200">
        <p className="text-yellow-800 text-sm">
          ⚠️ This is a placeholder component. Full admin dashboard functionality will be implemented.
        </p>
      </div>
    </div>
  );
};

export default AdminDashboard;
