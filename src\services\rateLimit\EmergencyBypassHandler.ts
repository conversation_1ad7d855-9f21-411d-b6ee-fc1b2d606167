/**
 * EMERGENCY BYPASS HANDLER
 * 
 * Handles emergency situations that require bypassing normal rate limits.
 * Extracted from rateLimitingService.ts for better maintainability.
 */

import type { UserRole } from '../../types/auth';
import type { EmergencyBypassConfig, BypassReason } from './RateLimitConfig';
import { bypassReasons } from './RateLimitConfig';
import auditLogger from '../../utils/auditLogger';

export interface EmergencyRequest {
  id: string;
  userId?: string;
  userRole?: UserRole;
  endpoint: string;
  reason: BypassReason;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface EmergencyBypassResult {
  allowed: boolean;
  reason?: BypassReason;
  emergencyId?: string;
  auditRequired: boolean;
}

export class EmergencyBypassHandler {
  private emergencyRequests = new Map<string, EmergencyRequest>();
  private emergencyCount = 0;

  constructor(private config: EmergencyBypassConfig) {}

  /**
   * Check if request qualifies for emergency bypass
   */
  async checkEmergencyBypass(
    userId: string | undefined,
    userRole: UserRole | undefined,
    endpoint: string,
    requestBody?: any,
    headers?: Record<string, string>
  ): Promise<EmergencyBypassResult> {
    
    if (!this.config.enabled) {
      return { allowed: false, auditRequired: false };
    }

    // Check emergency header
    if (headers?.['x-emergency-override']) {
      return await this.processEmergencyBypass(
        userId,
        userRole,
        endpoint,
        bypassReasons.EMERGENCY_HEADER,
        { header: headers['x-emergency-override'] }
      );
    }

    // Check emergency role
    if (userRole && this.config.emergencyRoles.includes(userRole)) {
      return await this.processEmergencyBypass(
        userId,
        userRole,
        endpoint,
        bypassReasons.EMERGENCY_ROLE,
        { role: userRole }
      );
    }

    // Check emergency endpoint
    if (this.isEmergencyEndpoint(endpoint)) {
      return await this.processEmergencyBypass(
        userId,
        userRole,
        endpoint,
        bypassReasons.EMERGENCY_ENDPOINT,
        { endpoint }
      );
    }

    // Check emergency keywords in request body
    if (requestBody && this.containsEmergencyKeywords(requestBody)) {
      return await this.processEmergencyBypass(
        userId,
        userRole,
        endpoint,
        bypassReasons.EMERGENCY_KEYWORDS,
        { keywords: this.extractEmergencyKeywords(requestBody) }
      );
    }

    return { allowed: false, auditRequired: false };
  }

  /**
   * Process emergency bypass and create audit trail
   */
  private async processEmergencyBypass(
    userId: string | undefined,
    userRole: UserRole | undefined,
    endpoint: string,
    reason: BypassReason,
    metadata: Record<string, any>
  ): Promise<EmergencyBypassResult> {
    
    // Check if we've exceeded emergency request limits
    if (this.emergencyCount >= this.config.maxEmergencyRequests) {
      await this.auditEmergencyDenial(userId, endpoint, 'emergency_limit_exceeded');
      return { allowed: false, auditRequired: true };
    }

    // Create emergency request record
    const emergencyId = this.generateEmergencyId();
    const emergencyRequest: EmergencyRequest = {
      id: emergencyId,
      userId: userId || undefined,
      userRole: userRole || undefined,
      endpoint,
      reason,
      timestamp: Date.now(),
      metadata: metadata || undefined
    };

    // Store emergency request
    this.emergencyRequests.set(emergencyId, emergencyRequest);
    this.emergencyCount++;

    // Audit the emergency bypass
    if (this.config.auditRequired) {
      await this.auditEmergencyBypass(emergencyRequest);
    }

    return {
      allowed: true,
      reason,
      emergencyId,
      auditRequired: this.config.auditRequired
    };
  }

  /**
   * Check if endpoint is emergency-related
   */
  private isEmergencyEndpoint(endpoint: string): boolean {
    return this.config.emergencyEndpoints.some(emergencyEndpoint =>
      endpoint.startsWith(emergencyEndpoint)
    );
  }

  /**
   * Check if request body contains emergency keywords
   */
  private containsEmergencyKeywords(body: any): boolean {
    const bodyText = JSON.stringify(body).toLowerCase();
    return this.config.emergencyKeywords.some(keyword =>
      bodyText.includes(keyword.toLowerCase())
    );
  }

  /**
   * Extract emergency keywords found in request body
   */
  private extractEmergencyKeywords(body: any): string[] {
    const bodyText = JSON.stringify(body).toLowerCase();
    return this.config.emergencyKeywords.filter(keyword =>
      bodyText.includes(keyword.toLowerCase())
    );
  }

  /**
   * Generate unique emergency ID
   */
  private generateEmergencyId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `emergency_${timestamp}_${random}`;
  }

  /**
   * Audit emergency bypass for compliance
   */
  private async auditEmergencyBypass(request: EmergencyRequest): Promise<void> {
    try {
      await auditLogger.logSecurityEvent(
        'emergency_bypass_granted',
        'high',
        {
          emergencyId: request.id,
          userId: request.userId,
          userRole: request.userRole,
          endpoint: request.endpoint,
          reason: request.reason,
          timestamp: request.timestamp,
          metadata: request.metadata
        }
      );
    } catch (error) {
      console.error('Failed to audit emergency bypass:', error);
    }
  }

  /**
   * Audit emergency bypass denial
   */
  private async auditEmergencyDenial(
    userId: string | undefined,
    endpoint: string,
    reason: string
  ): Promise<void> {
    try {
      await auditLogger.logSecurityEvent(
        'emergency_bypass_denied',
        'critical',
        {
          userId,
          endpoint,
          reason,
          timestamp: Date.now(),
          emergencyRequestCount: this.emergencyCount,
          maxEmergencyRequests: this.config.maxEmergencyRequests
        }
      );
    } catch (error) {
      console.error('Failed to audit emergency denial:', error);
    }
  }

  /**
   * Get emergency request by ID
   */
  getEmergencyRequest(emergencyId: string): EmergencyRequest | undefined {
    return this.emergencyRequests.get(emergencyId);
  }

  /**
   * Get all active emergency requests
   */
  getActiveEmergencyRequests(): EmergencyRequest[] {
    return Array.from(this.emergencyRequests.values());
  }

  /**
   * Get emergency statistics
   */
  getEmergencyStatistics() {
    const now = Date.now();
    const recentRequests = Array.from(this.emergencyRequests.values())
      .filter(req => now - req.timestamp < this.config.emergencyWindowMs);

    const reasonCounts = recentRequests.reduce((counts, req) => {
      counts[req.reason] = (counts[req.reason] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    return {
      totalEmergencyRequests: this.emergencyCount,
      activeEmergencyRequests: this.emergencyRequests.size,
      recentEmergencyRequests: recentRequests.length,
      reasonBreakdown: reasonCounts,
      configLimits: {
        maxRequests: this.config.maxEmergencyRequests,
        windowMs: this.config.emergencyWindowMs
      }
    };
  }

  /**
   * Clean up old emergency requests
   */
  cleanup(): void {
    const now = Date.now();
    const cutoff = now - this.config.emergencyWindowMs;

    for (const [id, request] of this.emergencyRequests.entries()) {
      if (request.timestamp < cutoff) {
        this.emergencyRequests.delete(id);
      }
    }
  }

  /**
   * Reset emergency state (for testing or system reset)
   */
  reset(): void {
    this.emergencyRequests.clear();
    this.emergencyCount = 0;
  }
}
