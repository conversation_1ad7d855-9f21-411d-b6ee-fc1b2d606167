/**
 * AGENT TYPE DEFINITIONS
 * 
 * Comprehensive type definitions for the AI agent system
 * supporting medical consultations, specialist referrals, and emergency protocols.
 */

// Re-export base agent types
export {
  type AgentRequest,
  type AgentResponse,
  type AgentRole,
  type AgentCapability,
  type AgentHandoffSuggestion,
  type EmergencyFlag,
  type FollowUpAction,
  type PatientContext,
  type IAgent,
  type AgentPerformanceMetrics
} from '../agents/BaseAgent';

// Import ConversationMessage from correct location
export type { ConversationMessage } from '../types/memory';

// GP-specific types
export interface SOAPAssessment {
  sessionId: string;
  currentPhase: 'subjective' | 'objective' | 'assessment' | 'plan' | 'complete';
  completionPercentage: number;
  keyFindings: string[];
  criticalFlags: string[];
  nextSteps: string[];
  subjective: {
    chiefComplaint: string;
    historyOfPresentIllness: string;
    reviewOfSystems: string[];
    pastMedicalHistory: string[];
    medications: string[];
    allergies: string[];
    socialHistory: string;
    familyHistory: string[];
  };
  objective: {
    vitalSigns: {
      bloodPressure?: string;
      heartRate?: number;
      temperature?: number;
      respiratoryRate?: number;
      oxygenSaturation?: number;
      weight?: number;
      height?: number;
    };
    physicalExam: string[];
    diagnosticTests: string[];
    observations: string[];
  };
  assessment: {
    primaryDiagnosis: string;
    differentialDiagnoses: string[];
    clinicalImpression: string;
    riskFactors: string[];
    prognosis: string;
  };
  plan: {
    treatments: string[];
    medications: string[];
    diagnosticOrders: string[];
    referrals: string[];
    followUp: string[];
    patientEducation: string[];
    lifestyle: string[];
  };
  timestamp: Date;
  confidence: number;
  completeness: number;
}

export interface DiagnosticContext {
  symptoms: string[];
  duration: string;
  severity: 'mild' | 'moderate' | 'severe';
  associatedFactors: string[];
  relievingFactors: string[];
  aggravatingFactors: string[];
  previousTreatments: string[];
  familyHistory: string[];
  medicalHistory: string[];
  currentMedications: string[];
  allergies: string[];
}

export interface TreatmentPlan {
  primaryTreatment: {
    type: 'medication' | 'procedure' | 'lifestyle' | 'referral';
    description: string;
    rationale: string;
    expectedOutcome: string;
    timeline: string;
  };
  alternativeTreatments: Array<{
    type: 'medication' | 'procedure' | 'lifestyle' | 'referral';
    description: string;
    rationale: string;
    conditions: string[];
  }>;
  supportiveCare: string[];
  monitoring: {
    parameters: string[];
    frequency: string;
    duration: string;
    warningSignsToWatch: string[];
  };
  followUp: {
    timeframe: string;
    purpose: string;
    provider: string;
    urgentIfSymptoms: string[];
  };
}

export interface SpecialistReferralCriteria {
  specialty: string;
  urgency: 'routine' | 'urgent' | 'emergent';
  reason: string;
  expectedBenefit: string;
  patientPreparation: string[];
  informationToProvide: string[];
  timeframe: string;
}

export interface EmergencyProtocol {
  triggerSymptoms: string[];
  immediateActions: string[];
  timeToResponse: number; // seconds
  escalationPath: string[];
  patientInstructions: string[];
  providerNotifications: string[];
}

export interface PatientEducationContent {
  topic: string;
  keyPoints: string[];
  resources: Array<{
    type: 'article' | 'video' | 'infographic' | 'website';
    title: string;
    url?: string;
    description: string;
  }>;
  culturalAdaptations: string[];
  languageOptions: string[];
  followUpQuestions: string[];
}

export interface ClinicalDecisionSupport {
  guidelines: Array<{
    source: string;
    recommendation: string;
    evidenceLevel: 'A' | 'B' | 'C' | 'D';
    applicability: number; // 0-100%
  }>;
  riskFactors: Array<{
    factor: string;
    weight: number;
    modifiable: boolean;
    interventions: string[];
  }>;
  contraindications: string[];
  drugInteractions: Array<{
    medication1: string;
    medication2: string;
    severity: 'minor' | 'moderate' | 'major';
    description: string;
    management: string;
  }>;
  alerts: Array<{
    type: 'allergy' | 'interaction' | 'contraindication' | 'monitoring';
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    action: string;
  }>;
}

export interface RegionalHealthContext {
  country: string;
  region: string;
  commonDiseases: string[];
  seasonalFactors: string[];
  culturalConsiderations: string[];
  healthcareAccess: 'limited' | 'moderate' | 'good';
  availableResources: string[];
  referralNetworks: string[];
  emergencyServices: {
    available: boolean;
    contactInfo: string;
    responseTime: string;
  };
}

export interface GPAgentConfig {
  maxResponseTime: number;
  confidenceThreshold: number;
  emergencyResponseTime: number;
  specialistReferralThreshold: number;
  enabledFeatures: {
    soapAssessment: boolean;
    visualAnalysis: boolean;
    ragRetrieval: boolean;
    emergencyDetection: boolean;
    specialistReferral: boolean;
    patientEducation: boolean;
  };
  culturalAdaptations: {
    enabled: boolean;
    supportedCultures: string[];
    defaultLanguage: string;
  };
  qualityAssurance: {
    enabledChecks: string[];
    minimumConfidence: number;
    requireSecondOpinion: boolean;
  };
}

export interface GPMetrics extends AgentPerformanceMetrics {
  soapAssessmentsCompleted: number;
  specialistReferralsMade: number;
  emergenciesDetected: number;
  patientEducationProvided: number;
  averageConsultationTime: number;
  patientSatisfactionScore: number;
  clinicalAccuracyRate: number;
  followUpComplianceRate: number;
}

// Response generation types
export interface ResponseGenerationContext {
  patientContext?: PatientContext;
  assembledContext?: any;
  regionalContext?: RegionalHealthContext;
  soapAssessment: SOAPAssessment;
  ragResponse?: any;
  visualAnalysisResults?: any;
  emergencyFlags: EmergencyFlag[];
  handoffSuggestions: AgentHandoffSuggestion[];
}

export interface StructuredResponse {
  greeting: string;
  assessment: string;
  recommendations: string[];
  education: string;
  followUp: string;
  emergencyInstructions?: string;
  culturalAdaptations: string[];
  confidence: number;
  reasoning: string;
}

// Tool integration types
export interface RAGQueryResult {
  success: boolean;
  data?: {
    relevantGuidelines: Array<{
      title: string;
      content: string;
      source: string;
      evidenceLevel: string;
      relevanceScore: number;
    }>;
    relatedResearch: Array<{
      title: string;
      abstract: string;
      authors: string[];
      journal: string;
      year: number;
      relevanceScore: number;
    }>;
    clinicalProtocols: Array<{
      condition: string;
      protocol: string;
      steps: string[];
      contraindications: string[];
      monitoring: string[];
    }>;
  };
  error?: string;
}

export interface VisualAnalysisResult {
  success: boolean;
  data?: {
    findings: Array<{
      type: string;
      description: string;
      confidence: number;
      location?: string;
      severity?: string;
    }>;
    recommendations: string[];
    urgency: 'low' | 'medium' | 'high' | 'critical';
    followUpNeeded: boolean;
    specialistReferral?: {
      specialty: string;
      urgency: string;
      reason: string;
    };
  };
  error?: string;
}
