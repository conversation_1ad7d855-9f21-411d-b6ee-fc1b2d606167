/**
 * RISK ASSESSMENT TYPE DEFINITIONS
 * 
 * Comprehensive type definitions for the Advanced Risk Stratification System
 * supporting African healthcare contexts with regional, cultural, and socioeconomic factors.
 */

export interface RiskAssessmentRequest {
  patientId: string;
  sessionId?: string;
  demographics: PatientDemographics;
  clinicalData?: ClinicalData;
  medicalHistory?: string[];
  currentSymptoms?: string[];
  socioeconomicFactors?: SocioeconomicFactors;
  environmentalFactors?: EnvironmentalFactors;
  culturalFactors?: CulturalFactors;
  behavioralFactors?: BehavioralFactors;
  assessmentType: 'comprehensive' | 'condition_specific' | 'emergency' | 'screening';
  targetConditions?: string[];
}

export interface PatientDemographics {
  age: number;
  gender: 'male' | 'female' | 'other';
  ethnicity: string;
  region: string;
  country: string;
  urbanRural: 'urban' | 'rural' | 'semi_urban';
  occupation: string;
}

export interface ClinicalData {
  currentSymptoms: string[];
  medicalHistory: string[];
  familyMedicalHistory: string[];
  currentMedications: string[];
  allergies: string[];
  vitalSigns: VitalSigns;
  laboratoryResults: LabResult[];
  immunizationHistory: string[];
  previousHospitalizations: string[];
  chronicConditions: string[];
}

export interface VitalSigns {
  bloodPressure?: { systolic: number; diastolic: number };
  heartRate?: number;
  temperature?: number;
  respiratoryRate?: number;
  oxygenSaturation?: number;
  weight?: number;
  height?: number;
  bmi?: number;
}

export interface LabResult {
  testName: string;
  value: number | string;
  unit: string;
  referenceRange: string;
  date: Date;
  abnormal: boolean;
}

export interface SocioeconomicFactors {
  income: 'low' | 'middle' | 'high';
  education: 'none' | 'primary' | 'secondary' | 'tertiary';
  employment: 'unemployed' | 'informal' | 'formal' | 'retired';
  healthcareAccess: 'limited' | 'moderate' | 'good';
  insurance: boolean;
  householdSize: number;
  waterAccess: 'none' | 'limited' | 'basic' | 'safely_managed';
  sanitationAccess: 'none' | 'limited' | 'basic' | 'safely_managed';
  lifestyle?: {
    smoking: boolean;
    alcohol: 'none' | 'moderate' | 'heavy';
    physicalActivity: 'sedentary' | 'light' | 'moderate' | 'vigorous';
    diet: 'poor' | 'fair' | 'good' | 'excellent';
  };
}

export interface EnvironmentalFactors {
  airQuality: 'good' | 'moderate' | 'poor' | 'hazardous';
  waterQuality: 'safe' | 'questionable' | 'unsafe';
  season: 'dry' | 'wet' | 'harmattan' | 'transition';
  altitude: number;
  temperature: number;
  humidity: number;
  vectorExposure: boolean;
  occupationalHazards: string[];
}

export interface CulturalFactors {
  primaryLanguage: string;
  religiousBeliefs: string;
  traditionalMedicineUse: boolean;
  familyDecisionMaking: boolean;
  genderRoles: string;
  dietaryRestrictions: string[];
  healthBeliefs: string[];
}

export interface BehavioralFactors {
  adherenceHistory: 'poor' | 'fair' | 'good' | 'excellent';
  healthSeekingBehavior: 'delayed' | 'appropriate' | 'proactive';
  riskTakingBehavior: 'low' | 'moderate' | 'high';
  socialSupport: 'none' | 'limited' | 'moderate' | 'strong';
  stressLevel: 'low' | 'moderate' | 'high' | 'severe';
}

export interface RiskAssessmentResult {
  overallRiskScore: number; // 0-100
  riskCategory: 'low' | 'moderate' | 'high' | 'critical';
  conditionSpecificRisks: ConditionRisk[];
  regionalRiskFactors: RegionalRiskFactor[];
  modifiableRiskFactors: ModifiableRiskFactor[];
  nonModifiableRiskFactors: NonModifiableRiskFactor[];
  predictiveAnalytics: PredictiveAnalytics;
  recommendations: RiskRecommendation[];
  urgentActions: UrgentAction[];
  followUpSchedule: FollowUpSchedule;
  culturalConsiderations: string[];
  assessmentMetadata: AssessmentMetadata;
}

export interface ConditionRisk {
  condition: string;
  riskScore: number;
  riskLevel: 'low' | 'moderate' | 'high' | 'critical';
  timeframe: string;
  contributingFactors: string[];
  evidenceLevel: 'A' | 'B' | 'C' | 'D';
  regionalPrevalence: number;
  seasonalVariation: boolean;
}

export interface RegionalRiskFactor {
  type: 'endemic_disease' | 'environmental' | 'seasonal' | 'socioeconomic' | 'healthcare_access';
  factor: string;
  riskScore: number;
  prevalence: number;
  seasonality: boolean;
  mitigation: string[];
}

export interface ModifiableRiskFactor {
  factor: string;
  currentLevel: 'low' | 'moderate' | 'high';
  targetLevel: 'low' | 'moderate' | 'high';
  interventions: string[];
  timeToImprovement: string;
  difficultyLevel: 'easy' | 'moderate' | 'difficult';
  culturalBarriers: string[];
}

export interface NonModifiableRiskFactor {
  factor: string;
  riskContribution: number;
  compensatoryMeasures: string[];
  monitoringRequired: boolean;
}

export interface PredictiveAnalytics {
  diseaseProgressionRisk: any;
  hospitalizationRisk: any;
  mortalityRisk: any;
  complicationRisk: any;
  treatmentResponsePrediction: any;
}

export interface RiskRecommendation {
  category: 'lifestyle' | 'medical' | 'screening' | 'environmental' | 'cultural';
  recommendation: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  timeframe: string;
  expectedBenefit: string;
  culturalAdaptations: string[];
  barriers: string[];
  alternatives: string[];
}

export interface UrgentAction {
  action: string;
  timeframe: 'immediately' | 'within_hours' | 'within_days' | 'within_weeks';
  rationale: string;
  consequences: string;
  culturalConsiderations: string[];
}

export interface FollowUpSchedule {
  nextAssessment: Date;
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'annually';
  monitoringParameters: string[];
  escalationTriggers: string[];
}

export interface AssessmentMetadata {
  assessmentDate: Date;
  assessmentVersion: string;
  dataCompleteness: number;
  confidenceLevel: number;
  limitationsNoted: string[];
  dataSourcesUsed: string[];
  processingTime: number;
}

// Emergency Risk Assessment Types
export interface EmergencyRiskAssessment {
  emergencyRiskLevel: 'low' | 'moderate' | 'high' | 'critical';
  urgentActions: UrgentAction[];
  timeToAction: number; // minutes
  culturalConsiderations: string[];
}

// Regional Risk Model Types
export interface RegionalRiskModel {
  country: string;
  region?: string;
  models: {
    [condition: string]: {
      accuracy: number;
      lastUpdated: string;
      prevalenceData: number;
      riskFactors: string[];
    };
  };
  isDefault: boolean;
}
