/**
 * CULTURAL ADAPTATION SERVICE
 * 
 * Provides cultural adaptation for medical communications and healthcare delivery
 * across African regions with support for multiple languages and cultural contexts.
 * 
 * FEATURES:
 * - Multi-language medical terminology translation
 * - Cultural communication style adaptation
 * - Traditional medicine integration awareness
 * - Religious and dietary consideration integration
 * - Family involvement protocol adaptation
 * - Gender-sensitive consultation modes
 * - Health literacy level adaptation
 * - Cultural sensitivity scoring
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface CulturalProfile {
  cultureCode: string;
  languagePreference: string;
  secondaryLanguages: string[];
  communicationStyle: CommunicationStyle;
  religiousConsiderations: ReligiousConsiderations;
  familyInvolvementPreferences: FamilyInvolvementPreferences;
  traditionalMedicineOpenness: number; // 1-5 scale
  genderSensitivityLevel: number; // 1-5 scale
  authorityRespectLevel: number; // 1-5 scale
  healthLiteracyLevel: 'basic' | 'intermediate' | 'advanced';
  preferredExplanationStyle: 'technical' | 'simplified' | 'storytelling' | 'visual';
  dietaryRestrictions: DietaryRestrictions;
  culturalTaboos: string[];
  preferredConsultationStyle: 'direct' | 'indirect' | 'collaborative';
}

export interface CommunicationStyle {
  directness: 'direct' | 'indirect';
  formalityLevel: 'formal' | 'informal' | 'mixed';
  eyeContactComfort: 'high' | 'medium' | 'low';
  personalSpacePreference: 'close' | 'moderate' | 'distant';
  touchComfort: 'comfortable' | 'limited' | 'avoid';
}

export interface ReligiousConsiderations {
  religion?: string | undefined;
  prayerTimes?: string[] | undefined;
  fastingPeriods?: string[] | undefined;
  dietaryRestrictions?: string[] | undefined;
  modestyConcerns?: string[] | undefined;
  spiritualHealingBeliefs?: boolean | undefined;
}

export interface FamilyInvolvementPreferences {
  level: 'minimal' | 'moderate' | 'high';
  decisionMakers: string[]; // e.g., ['patient', 'spouse', 'elder']
  informationSharing: 'patient_only' | 'immediate_family' | 'extended_family';
  consultationPresence: boolean;
}

export interface DietaryRestrictions {
  type?: 'halal' | 'kosher' | 'vegetarian' | 'vegan' | 'traditional' | 'none' | undefined;
  specificRestrictions?: string[] | undefined;
  culturalFoods?: string[] | undefined;
  avoidedFoods?: string[] | undefined;
}

export interface AdaptedMessage {
  originalMessage: string;
  adaptedMessage: string;
  adaptationReason: string;
  culturalContext: string;
  sensitivityScore: number;
  languageUsed: string;
}

export interface MedicalTermTranslation {
  englishTerm: string;
  translatedTerm: string;
  culturalContext: string;
  usageNotes: string;
  sensitivityLevel: 'low' | 'medium' | 'high';
  pronunciationGuide?: string | undefined;
  alternativeTerms?: string[] | undefined;
}

export interface CulturalSensitivityScore {
  overallScore: number; // 0-100
  communicationScore: number;
  religiousScore: number;
  familyScore: number;
  traditionalMedicineScore: number;
  genderScore: number;
  recommendations: string[];
}

export interface TraditionalMedicineContext {
  relevantRemedies: TraditionalRemedy[];
  culturalSignificance: string;
  integrationApproach: string;
  safetyConsiderations: string[];
}

export interface TraditionalRemedy {
  name: string;
  localName: string;
  uses: string[];
  safetyProfile: string;
  interactions: string[];
}

// =====================================================
// CULTURAL ADAPTATION SERVICE
// =====================================================

export class CulturalAdaptationService {
  private supabase: SupabaseClient;
  private cache: Map<string, any> = new Map();
  private readonly cacheTimeout = 10 * 60 * 1000; // 10 minutes

  constructor() {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing for cultural adaptation service');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    console.log('✅ CulturalAdaptationService initialized');
  }

  /**
   * Adapt communication style based on cultural profile
   */
  async adaptCommunicationStyle(
    message: string,
    culturalProfile: CulturalProfile
  ): Promise<AdaptedMessage> {
    try {
      const startTime = performance.now();
      const cacheKey = `adapt_${this.hashMessage(message)}_${culturalProfile.cultureCode}`;

      if (this.cache.has(cacheKey)) {
        return this.cache.get(cacheKey);
      }

      let adaptedMessage = message;
      const adaptationReasons: string[] = [];

      // Apply communication style adaptations
      if (culturalProfile.communicationStyle.directness === 'indirect') {
        adaptedMessage = this.makeMessageIndirect(adaptedMessage);
        adaptationReasons.push('Applied indirect communication style');
      }

      // Apply formality level
      if (culturalProfile.communicationStyle.formalityLevel === 'formal') {
        adaptedMessage = this.makeFormal(adaptedMessage);
        adaptationReasons.push('Applied formal communication style');
      }

      // Apply authority respect
      if (culturalProfile.authorityRespectLevel >= 4) {
        adaptedMessage = this.addRespectfulTone(adaptedMessage);
        adaptationReasons.push('Added respectful tone for high authority respect culture');
      }

      // Apply health literacy adaptation
      if (culturalProfile.healthLiteracyLevel === 'basic') {
        adaptedMessage = this.simplifyMedicalLanguage(adaptedMessage);
        adaptationReasons.push('Simplified medical language for basic health literacy');
      }

      // Apply explanation style
      if (culturalProfile.preferredExplanationStyle === 'storytelling') {
        adaptedMessage = this.addStorytellingElements(adaptedMessage);
        adaptationReasons.push('Added storytelling elements');
      }

      // Calculate sensitivity score
      const sensitivityScore = await this.calculateSensitivityScore(adaptedMessage, culturalProfile);

      const result: AdaptedMessage = {
        originalMessage: message,
        adaptedMessage,
        adaptationReason: adaptationReasons.join('; '),
        culturalContext: culturalProfile.cultureCode,
        sensitivityScore,
        languageUsed: culturalProfile.languagePreference
      };

      // Cache result
      this.cache.set(cacheKey, result);
      setTimeout(() => this.cache.delete(cacheKey), this.cacheTimeout);

      const responseTime = performance.now() - startTime;
      console.log(`✅ Communication adapted in ${responseTime.toFixed(2)}ms`);

      return result;
    } catch (error) {
      console.error('❌ Error adapting communication style:', error);
      return {
        originalMessage: message,
        adaptedMessage: message,
        adaptationReason: 'No adaptation applied due to error',
        culturalContext: culturalProfile.cultureCode,
        sensitivityScore: 50,
        languageUsed: culturalProfile.languagePreference
      };
    }
  }

  /**
   * Translate medical terms with cultural context
   */
  async translateMedicalTerms(
    terms: string[],
    targetLanguage: string,
    culturalContext?: string
  ): Promise<MedicalTermTranslation[]> {
    try {
      if (terms.length === 0) return [];

      const { data: translations, error } = await this.supabase
        .rpc('translate_medical_terms', {
          p_terms: terms,
          p_target_language: targetLanguage,
          p_cultural_context: culturalContext
        });

      if (error) {
        console.error('❌ Error translating medical terms:', error);
        return terms.map(term => ({
          englishTerm: term,
          translatedTerm: term,
          culturalContext: culturalContext || '',
          usageNotes: 'Translation not available',
          sensitivityLevel: 'medium' as const
        }));
      }

      return (translations || []).map((translation: any) => ({
        englishTerm: translation.english_term,
        translatedTerm: translation.translated_term,
        culturalContext: translation.cultural_context || '',
        usageNotes: translation.usage_notes || '',
        sensitivityLevel: translation.sensitivity_level || 'medium',
        pronunciationGuide: translation.pronunciation_guide,
        alternativeTerms: translation.alternative_terms
      }));
    } catch (error) {
      console.error('❌ Error translating medical terms:', error);
      return terms.map(term => ({
        englishTerm: term,
        translatedTerm: term,
        culturalContext: culturalContext || '',
        usageNotes: 'Translation error',
        sensitivityLevel: 'medium' as const
      }));
    }
  }

  /**
   * Assess cultural sensitivity of content
   */
  async assessCulturalSensitivity(
    content: string,
    culturalProfile: CulturalProfile
  ): Promise<CulturalSensitivityScore> {
    try {
      const scores = {
        communication: this.scoreCommunicationSensitivity(content, culturalProfile),
        religious: this.scoreReligiousSensitivity(content, culturalProfile),
        family: this.scoreFamilySensitivity(content, culturalProfile),
        traditionalMedicine: this.scoreTraditionalMedicineSensitivity(content, culturalProfile),
        gender: this.scoreGenderSensitivity(content, culturalProfile)
      };

      const overallScore = Object.values(scores).reduce((sum, score) => sum + score, 0) / 5;
      const recommendations = this.generateSensitivityRecommendations(scores, culturalProfile);

      return {
        overallScore: Math.round(overallScore),
        communicationScore: scores.communication,
        religiousScore: scores.religious,
        familyScore: scores.family,
        traditionalMedicineScore: scores.traditionalMedicine,
        genderScore: scores.gender,
        recommendations
      };
    } catch (error) {
      console.error('❌ Error assessing cultural sensitivity:', error);
      return {
        overallScore: 50,
        communicationScore: 50,
        religiousScore: 50,
        familyScore: 50,
        traditionalMedicineScore: 50,
        genderScore: 50,
        recommendations: ['Unable to assess cultural sensitivity due to error']
      };
    }
  }

  /**
   * Get traditional medicine context for condition
   */
  async getTraditionalMedicineContext(
    condition: string,
    region: string
  ): Promise<TraditionalMedicineContext> {
    try {
      const { data: remedies, error } = await this.supabase
        .rpc('search_traditional_remedies', {
          p_search_term: condition,
          p_region: region
        });

      if (error) {
        console.error('❌ Error getting traditional medicine context:', error);
        return {
          relevantRemedies: [],
          culturalSignificance: 'Unable to retrieve traditional medicine information',
          integrationApproach: 'Consult with traditional healers if appropriate',
          safetyConsiderations: ['Always inform healthcare providers about traditional remedies']
        };
      }

      const relevantRemedies: TraditionalRemedy[] = (remedies || []).map((remedy: any) => ({
        name: remedy.name,
        localName: remedy.local_name || remedy.name,
        uses: remedy.traditional_uses || [],
        safetyProfile: remedy.safety_profile?.summary || 'Safety profile under review',
        interactions: remedy.drug_interactions?.interactions || []
      }));

      return {
        relevantRemedies,
        culturalSignificance: `Traditional medicine plays an important role in ${region} healthcare`,
        integrationApproach: 'Respectful integration with modern medicine',
        safetyConsiderations: [
          'Discuss all traditional remedies with healthcare provider',
          'Be aware of potential drug interactions',
          'Ensure quality and safety of traditional preparations'
        ]
      };
    } catch (error) {
      console.error('❌ Error getting traditional medicine context:', error);
      return {
        relevantRemedies: [],
        culturalSignificance: 'Error retrieving information',
        integrationApproach: 'Consult healthcare provider',
        safetyConsiderations: ['Inform provider about all treatments']
      };
    }
  }

  // =====================================================
  // PRIVATE HELPER METHODS
  // =====================================================

  private hashMessage(message: string): string {
    // Simple hash function for caching
    let hash = 0;
    for (let i = 0; i < message.length; i++) {
      const char = message.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  private makeMessageIndirect(message: string): string {
    // Convert direct statements to more indirect, polite forms
    return message
      .replace(/You should/g, 'It might be helpful to consider')
      .replace(/You must/g, 'It would be advisable to')
      .replace(/This is/g, 'This appears to be')
      .replace(/You have/g, 'It seems you may have');
  }

  private makeFormal(message: string): string {
    // Add formal language elements
    return message
      .replace(/can't/g, 'cannot')
      .replace(/won't/g, 'will not')
      .replace(/don't/g, 'do not')
      .replace(/isn't/g, 'is not');
  }

  private addRespectfulTone(message: string): string {
    // Add respectful prefixes and language
    if (!message.toLowerCase().includes('please') && !message.toLowerCase().includes('kindly')) {
      return `Please ${message.charAt(0).toLowerCase() + message.slice(1)}`;
    }
    return message;
  }

  private simplifyMedicalLanguage(message: string): string {
    // Replace complex medical terms with simpler alternatives
    const simplifications: { [key: string]: string } = {
      'hypertension': 'high blood pressure',
      'myocardial infarction': 'heart attack',
      'cerebrovascular accident': 'stroke',
      'pneumonia': 'lung infection',
      'gastroenteritis': 'stomach flu',
      'analgesic': 'pain medicine',
      'antibiotic': 'infection medicine'
    };

    let simplified = message;
    Object.entries(simplifications).forEach(([complex, simple]) => {
      const regex = new RegExp(complex, 'gi');
      simplified = simplified.replace(regex, simple);
    });

    return simplified;
  }

  private addStorytellingElements(message: string): string {
    // Add narrative elements to make explanations more engaging
    if (message.includes('condition') || message.includes('symptoms')) {
      return `Think of your body like a well-functioning community. ${message} This helps us understand what's happening and how to help.`;
    }
    return message;
  }

  private async calculateSensitivityScore(message: string, profile: CulturalProfile): Promise<number> {
    // Calculate overall sensitivity score based on various factors
    let score = 70; // Base score

    // Adjust based on cultural adaptations applied
    if (profile.communicationStyle.directness === 'indirect' && message.includes('consider')) {
      score += 10;
    }

    if (profile.healthLiteracyLevel === 'basic' && !this.containsComplexMedicalTerms(message)) {
      score += 10;
    }

    if (profile.authorityRespectLevel >= 4 && (message.includes('please') || message.includes('kindly'))) {
      score += 10;
    }

    return Math.min(score, 100);
  }

  private containsComplexMedicalTerms(message: string): boolean {
    const complexTerms = ['hypertension', 'myocardial', 'cerebrovascular', 'pathophysiology', 'etiology'];
    return complexTerms.some(term => message.toLowerCase().includes(term));
  }

  private scoreCommunicationSensitivity(content: string, profile: CulturalProfile): number {
    let score = 70;
    
    if (profile.communicationStyle.directness === 'indirect' && content.includes('consider')) {
      score += 15;
    }
    
    if (profile.communicationStyle.formalityLevel === 'formal' && !content.includes("can't")) {
      score += 10;
    }
    
    return Math.min(score, 100);
  }

  private scoreReligiousSensitivity(content: string, profile: CulturalProfile): number {
    let score = 80;
    
    if (profile.religiousConsiderations.religion && content.toLowerCase().includes('spiritual')) {
      score += 10;
    }
    
    return Math.min(score, 100);
  }

  private scoreFamilySensitivity(content: string, profile: CulturalProfile): number {
    let score = 75;
    
    if (profile.familyInvolvementPreferences.level === 'high' && content.includes('family')) {
      score += 15;
    }
    
    return Math.min(score, 100);
  }

  private scoreTraditionalMedicineSensitivity(content: string, profile: CulturalProfile): number {
    let score = 70;
    
    if (profile.traditionalMedicineOpenness >= 3 && content.toLowerCase().includes('traditional')) {
      score += 20;
    }
    
    return Math.min(score, 100);
  }

  private scoreGenderSensitivity(content: string, profile: CulturalProfile): number {
    let score = 80;
    
    if (profile.genderSensitivityLevel >= 4) {
      // Check for gender-neutral language
      if (!content.includes('he/she') && !content.includes('his/her')) {
        score += 10;
      }
    }
    
    return Math.min(score, 100);
  }

  private generateSensitivityRecommendations(scores: any, profile: CulturalProfile): string[] {
    const recommendations: string[] = [];
    
    if (scores.communication < 70) {
      recommendations.push('Consider adapting communication style to be more culturally appropriate');
    }
    
    if (scores.religious < 70 && profile.religiousConsiderations.religion) {
      recommendations.push('Include religious considerations in healthcare discussions');
    }
    
    if (scores.family < 70 && profile.familyInvolvementPreferences.level === 'high') {
      recommendations.push('Consider involving family members in healthcare decisions');
    }
    
    if (scores.traditionalMedicine < 70 && profile.traditionalMedicineOpenness >= 3) {
      recommendations.push('Acknowledge and discuss traditional medicine practices respectfully');
    }
    
    return recommendations;
  }

  /**
   * Clear cache for memory management
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🧹 Cultural adaptation service cache cleared');
  }
}

// Export singleton instance
export const culturalAdaptationService = new CulturalAdaptationService();
