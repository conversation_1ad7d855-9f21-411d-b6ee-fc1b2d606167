/**
 * EMERGENCY BYPASS SERVICE
 * 
 * Handles emergency situations that require bypassing normal rate limits.
 * Ensures patient safety by allowing critical medical requests through.
 */

import type { Request, Response, NextFunction } from 'express';
import type { RateLimitRequest } from './RateLimitCore';
import auditLogger from '../../utils/auditLogger';

export interface EmergencyContext {
  emergencyType: 'medical' | 'system' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  reason: string;
  requestId: string;
  userId?: string;
  timestamp: number;
}

/**
 * Emergency bypass service for critical medical situations
 */
export class EmergencyBypassService {
  private emergencyRequests = new Map<string, EmergencyContext>();
  
  /**
   * Create emergency bypass middleware
   */
  createBypassMiddleware() {
    return async (req: RateLimitRequest, res: Response, next: NextFunction) => {
      try {
        const emergencyContext = this.detectEmergencyContext(req);
        
        if (emergencyContext) {
          // Log emergency bypass
          await this.logEmergencyBypass(emergencyContext, req);
          
          // Set emergency flag
          if (req.user) {
            req.user.emergencyOverride = true;
          }
          
          // Add emergency info to request
          req.rateLimitInfo = {
            ...req.rateLimitInfo,
            isEmergency: true,
            bypassReason: emergencyContext.reason
          };
          
          // Store emergency context
          this.emergencyRequests.set(emergencyContext.requestId, emergencyContext);
        }
        
        next();
      } catch (error) {
        console.error('Emergency bypass error:', error);
        next(); // Continue even if emergency detection fails
      }
    };
  }

  /**
   * Detect if request is emergency-related
   */
  private detectEmergencyContext(req: RateLimitRequest): EmergencyContext | null {
    const requestId = this.generateRequestId(req);
    
    // Check for explicit emergency headers
    const emergencyHeader = req.headers['x-emergency-override'] as string;
    if (emergencyHeader) {
      return {
        emergencyType: 'medical',
        severity: 'high',
        reason: `Emergency override header: ${emergencyHeader}`,
        requestId,
        userId: req.user?.id,
        timestamp: Date.now()
      };
    }

    // Check for emergency endpoints
    if (this.isEmergencyEndpoint(req.path)) {
      return {
        emergencyType: 'medical',
        severity: 'critical',
        reason: `Emergency endpoint access: ${req.path}`,
        requestId,
        userId: req.user?.id,
        timestamp: Date.now()
      };
    }

    // Check for emergency keywords in request body
    if (req.body && this.containsEmergencyKeywords(req.body)) {
      return {
        emergencyType: 'medical',
        severity: 'medium',
        reason: 'Emergency keywords detected in request',
        requestId,
        userId: req.user?.id,
        timestamp: Date.now()
      };
    }

    return null;
  }

  /**
   * Check if endpoint is emergency-related
   */
  private isEmergencyEndpoint(path: string): boolean {
    const emergencyPaths = [
      '/api/emergency',
      '/api/consultation/emergency',
      '/api/agents/emergency',
      '/api/medical/emergency',
      '/api/triage/emergency'
    ];

    return emergencyPaths.some(emergencyPath => path.startsWith(emergencyPath));
  }

  /**
   * Check for emergency keywords in request content
   */
  private containsEmergencyKeywords(body: any): boolean {
    const emergencyKeywords = [
      'emergency', 'urgent', 'critical', 'life-threatening',
      'cardiac arrest', 'stroke', 'severe pain', 'difficulty breathing',
      'unconscious', 'bleeding', 'overdose', 'allergic reaction'
    ];

    const bodyText = JSON.stringify(body).toLowerCase();
    return emergencyKeywords.some(keyword => bodyText.includes(keyword));
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(req: RateLimitRequest): string {
    const timestamp = Date.now();
    const userId = req.user?.id || 'anonymous';
    const path = req.path;
    return `${timestamp}-${userId}-${path}`.replace(/[^a-zA-Z0-9-]/g, '-');
  }

  /**
   * Log emergency bypass for audit purposes
   */
  private async logEmergencyBypass(context: EmergencyContext, req: RateLimitRequest): Promise<void> {
    try {
      await auditLogger.logSecurityEvent(
        'emergency_bypass',
        context.severity,
        {
          emergencyType: context.emergencyType,
          reason: context.reason,
          requestId: context.requestId,
          userId: context.userId,
          endpoint: req.path,
          method: req.method,
          userAgent: req.headers['user-agent'],
          ip: req.ip,
          timestamp: context.timestamp
        }
      );
    } catch (error) {
      console.error('Failed to log emergency bypass:', error);
    }
  }

  /**
   * Get emergency context for a request
   */
  getEmergencyContext(requestId: string): EmergencyContext | undefined {
    return this.emergencyRequests.get(requestId);
  }

  /**
   * Clean up old emergency contexts
   */
  cleanup(): void {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
    
    for (const [requestId, context] of this.emergencyRequests.entries()) {
      if (context.timestamp < cutoff) {
        this.emergencyRequests.delete(requestId);
      }
    }
  }
}

export default new EmergencyBypassService();
