/**
 * RISK CALCULATION SERVICE
 * 
 * Core risk calculation algorithms for the Advanced Risk Stratification System.
 * Handles condition-specific risk scoring, overall risk aggregation, and risk categorization.
 */

import type {
  RiskAssessmentRequest,
  ConditionRisk,
  RegionalRiskFactor,
  ModifiableRiskFactor,
  NonModifiableRiskFactor,
  PatientDemographics,
  RegionalRiskModel
} from '../../types/riskAssessment';

export class RiskCalculationService {
  
  /**
   * Calculate condition-specific risks
   */
  async calculateConditionSpecificRisks(
    request: RiskAssessmentRequest,
    regionalModels: RegionalRiskModel
  ): Promise<ConditionRisk[]> {
    const conditionRisks: ConditionRisk[] = [];

    // Define conditions to assess based on region and demographics
    const conditionsToAssess = this.getConditionsToAssess(
      request.demographics,
      request.clinicalData || {
        currentSymptoms: [],
        medicalHistory: [],
        familyMedicalHistory: [],
        currentMedications: [],
        allergies: [],
        vitalSigns: {},
        laboratoryResults: [],
        immunizationHistory: [],
        previousHospitalizations: [],
        chronicConditions: []
      },
      request.targetConditions
    );

    for (const condition of conditionsToAssess) {
      const riskScore = await this.calculateConditionRisk(
        condition,
        request,
        regionalModels
      );

      conditionRisks.push({
        condition,
        riskScore,
        riskLevel: this.categorizeRisk(riskScore),
        timeframe: this.getConditionTimeframe(condition),
        contributingFactors: this.getContributingFactors(condition, request),
        evidenceLevel: this.getEvidenceLevel(condition, request.demographics.country),
        regionalPrevalence: this.getRegionalPrevalence(condition, request.demographics),
        seasonalVariation: this.hasSeasonalVariation(condition, request.demographics.country)
      });
    }

    return conditionRisks.sort((a, b) => b.riskScore - a.riskScore);
  }

  /**
   * Calculate overall risk score from all risk factors
   */
  calculateOverallRiskScore(
    conditionRisks: ConditionRisk[],
    regionalFactors: RegionalRiskFactor[],
    modifiableFactors: ModifiableRiskFactor[],
    nonModifiableFactors: NonModifiableRiskFactor[]
  ): number {
    // Weighted scoring algorithm
    let score = 0;

    // Condition risks (50% weight)
    const avgConditionRisk = conditionRisks.length > 0
      ? conditionRisks.reduce((sum, risk) => sum + risk.riskScore, 0) / conditionRisks.length
      : 0;
    score += avgConditionRisk * 0.5;

    // Regional factors (20% weight)
    const regionalRiskScore = this.calculateRegionalRiskScore(regionalFactors);
    score += regionalRiskScore * 0.2;

    // Modifiable factors (20% weight)
    const modifiableRiskScore = this.calculateModifiableRiskScore(modifiableFactors);
    score += modifiableRiskScore * 0.2;

    // Non-modifiable factors (10% weight)
    const nonModifiableRiskScore = this.calculateNonModifiableRiskScore(nonModifiableFactors);
    score += nonModifiableRiskScore * 0.1;

    return Math.round(Math.min(100, Math.max(0, score)));
  }

  /**
   * Categorize risk score into risk levels
   */
  categorizeRisk(riskScore: number): 'low' | 'moderate' | 'high' | 'critical' {
    if (riskScore >= 80) return 'critical';
    if (riskScore >= 60) return 'high';
    if (riskScore >= 40) return 'moderate';
    return 'low';
  }

  /**
   * Calculate risk for a specific condition
   */
  private async calculateConditionRisk(
    condition: string,
    request: RiskAssessmentRequest,
    regionalModels: RegionalRiskModel
  ): Promise<number> {
    try {
      let riskScore = 0;

      // Base risk from regional prevalence
      const baseRisk = this.getRegionalPrevalence(condition, request.demographics);
      riskScore += baseRisk * 0.3;

      // Demographic risk factors
      const demographicRisk = this.getDemographicRisk(condition, request.demographics);
      riskScore += demographicRisk * 0.25;

      // Clinical risk factors
      if (request.clinicalData) {
        const clinicalRisk = this.getClinicalRisk(condition, request.clinicalData);
        riskScore += clinicalRisk * 0.25;
      }

      // Environmental risk factors
      if (request.environmentalFactors) {
        const environmentalRisk = this.getEnvironmentalRisk(condition, request.environmentalFactors);
        riskScore += environmentalRisk * 0.1;
      }

      // Socioeconomic risk factors
      if (request.socioeconomicFactors) {
        const socioeconomicRisk = this.getSocioeconomicRisk(condition, request.socioeconomicFactors);
        riskScore += socioeconomicRisk * 0.1;
      }

      // Behavioral risk factors
      if (request.behavioralFactors) {
        const behavioralRisk = this.getBehavioralRisk(condition, request.behavioralFactors);
        riskScore += behavioralRisk * 0.1;
      }

      return Math.round(Math.min(100, Math.max(0, riskScore)));

    } catch (error) {
      console.error(`❌ Error calculating risk for condition ${condition}:`, error);
      return 0;
    }
  }

  // Helper methods for risk calculation
  private getConditionsToAssess(
    demographics: PatientDemographics,
    clinicalData: any,
    targetConditions?: string[]
  ): string[] {
    if (targetConditions && targetConditions.length > 0) {
      return targetConditions;
    }

    const conditions: string[] = [];
    
    // Region-specific conditions
    const regionSpecific = this.getRegionSpecificConditions(demographics.country);
    conditions.push(...regionSpecific);

    // Age-specific conditions
    const ageSpecific = this.getAgeSpecificConditions(demographics.age);
    conditions.push(...ageSpecific);

    // Symptom-based conditions
    if (clinicalData.currentSymptoms?.length > 0) {
      const symptomBased = this.getSymptomBasedConditions(clinicalData.currentSymptoms);
      conditions.push(...symptomBased);
    }

    return [...new Set(conditions)]; // Remove duplicates
  }

  private getRegionSpecificConditions(country: string): string[] {
    const regionConditions: { [key: string]: string[] } = {
      'nigeria': ['malaria', 'tuberculosis', 'hypertension', 'diabetes', 'HIV'],
      'kenya': ['malaria', 'tuberculosis', 'hypertension', 'diabetes', 'HIV'],
      'south_africa': ['tuberculosis', 'HIV', 'hypertension', 'diabetes', 'heart_disease'],
      'ghana': ['malaria', 'hypertension', 'diabetes', 'stroke'],
      'default': ['hypertension', 'diabetes', 'heart_disease', 'stroke']
    };

    return regionConditions[country.toLowerCase()] || regionConditions['default'] || [];
  }

  private getAgeSpecificConditions(age: number): string[] {
    if (age < 18) {
      return ['malaria', 'respiratory_infections', 'malnutrition'];
    } else if (age < 40) {
      return ['hypertension', 'diabetes', 'infectious_diseases'];
    } else if (age < 65) {
      return ['hypertension', 'diabetes', 'heart_disease', 'stroke'];
    } else {
      return ['hypertension', 'diabetes', 'heart_disease', 'stroke', 'chronic_kidney_disease'];
    }
  }

  private getSymptomBasedConditions(symptoms: string[]): string[] {
    const conditions: string[] = [];
    
    if (symptoms.some(s => s.includes('chest pain') || s.includes('shortness of breath'))) {
      conditions.push('heart_disease', 'hypertension');
    }
    
    if (symptoms.some(s => s.includes('fever') || s.includes('headache'))) {
      conditions.push('malaria', 'infectious_diseases');
    }
    
    if (symptoms.some(s => s.includes('cough') || s.includes('weight loss'))) {
      conditions.push('tuberculosis', 'respiratory_infections');
    }

    return conditions;
  }

  private getConditionTimeframe(condition: string): string {
    const timeframes: { [key: string]: string } = {
      'heart_failure': '6-12 months',
      'stroke': 'immediate-3 months',
      'diabetes': '1-5 years',
      'hypertension': '6 months-2 years',
      'malaria': '1-4 weeks',
      'tuberculosis': '2-8 weeks',
      'copd': '1-3 years',
      'chronic_kidney_disease': '2-5 years'
    };

    return timeframes[condition] || '3-12 months';
  }

  private getContributingFactors(condition: string, request: RiskAssessmentRequest): string[] {
    const factors: string[] = [];
    
    // Add demographic factors
    if (request.demographics.age > 65) factors.push('Advanced age');
    if (request.demographics.gender === 'male' && ['heart_failure', 'stroke'].includes(condition)) {
      factors.push('Male gender');
    }
    
    // Add clinical factors
    if (request.clinicalData?.medicalHistory?.includes('diabetes')) {
      factors.push('Diabetes mellitus');
    }
    if (request.clinicalData?.medicalHistory?.includes('hypertension')) {
      factors.push('Hypertension');
    }
    
    // Add lifestyle factors
    if (request.socioeconomicFactors?.lifestyle?.smoking) {
      factors.push('Smoking');
    }
    
    return factors;
  }

  private getEvidenceLevel(condition: string, country: string): 'A' | 'B' | 'C' | 'D' {
    const evidenceLevels: { [key: string]: { [key: string]: 'A' | 'B' | 'C' | 'D' } } = {
      'heart_failure': { 'default': 'A', 'nigeria': 'B', 'kenya': 'B' },
      'diabetes': { 'default': 'A', 'nigeria': 'A', 'kenya': 'A' },
      'malaria': { 'default': 'A', 'nigeria': 'A', 'kenya': 'A' },
      'tuberculosis': { 'default': 'A', 'nigeria': 'A', 'kenya': 'A' },
      'hypertension': { 'default': 'A', 'nigeria': 'B', 'kenya': 'B' }
    };

    return evidenceLevels[condition]?.[country.toLowerCase()] || 
           evidenceLevels[condition]?.['default'] || 'C';
  }

  private hasSeasonalVariation(condition: string, country: string): boolean {
    const seasonalConditions: { [key: string]: string[] } = {
      'malaria': ['nigeria', 'kenya', 'ghana', 'uganda'],
      'respiratory_infections': ['nigeria', 'kenya', 'south_africa'],
      'dengue': ['nigeria', 'kenya'],
      'meningitis': ['nigeria', 'ghana', 'burkina_faso']
    };

    return seasonalConditions[condition]?.includes(country.toLowerCase()) || false;
  }

  private getRegionalPrevalence(condition: string, demographics: PatientDemographics): number {
    const prevalenceData: { [key: string]: { [key: string]: number } } = {
      'hypertension': { 'nigeria': 28, 'kenya': 24, 'south_africa': 32, 'default': 25 },
      'diabetes': { 'nigeria': 5, 'kenya': 4, 'south_africa': 7, 'default': 5 },
      'malaria': { 'nigeria': 60, 'kenya': 35, 'ghana': 45, 'default': 10 },
      'tuberculosis': { 'nigeria': 219, 'kenya': 233, 'south_africa': 520, 'default': 100 },
      'HIV': { 'nigeria': 1.4, 'kenya': 4.9, 'south_africa': 20.4, 'default': 2 }
    };

    return prevalenceData[condition]?.[demographics.country.toLowerCase()] || 
           prevalenceData[condition]?.['default'] || 5;
  }

  // Placeholder methods for risk calculation components
  private getDemographicRisk(condition: string, demographics: PatientDemographics): number {
    // Simplified demographic risk calculation
    let risk = 0;
    
    // Age factor
    if (demographics.age > 65) risk += 20;
    else if (demographics.age > 45) risk += 10;
    
    // Gender factor for specific conditions
    if (condition === 'heart_disease' && demographics.gender === 'male') risk += 10;
    
    return Math.min(100, risk);
  }

  private getClinicalRisk(condition: string, clinicalData: any): number {
    // Simplified clinical risk calculation
    let risk = 0;
    
    if (clinicalData.medicalHistory?.includes(condition)) risk += 30;
    if (clinicalData.familyMedicalHistory?.includes(condition)) risk += 15;
    
    return Math.min(100, risk);
  }

  private getEnvironmentalRisk(condition: string, environmentalFactors: any): number {
    // Simplified environmental risk calculation
    let risk = 0;
    
    if (condition === 'malaria' && environmentalFactors.vectorExposure) risk += 25;
    if (environmentalFactors.airQuality === 'poor') risk += 10;
    
    return Math.min(100, risk);
  }

  private getSocioeconomicRisk(condition: string, socioeconomicFactors: any): number {
    // Simplified socioeconomic risk calculation
    let risk = 0;
    
    if (socioeconomicFactors.healthcareAccess === 'limited') risk += 15;
    if (socioeconomicFactors.income === 'low') risk += 10;
    
    return Math.min(100, risk);
  }

  private getBehavioralRisk(condition: string, behavioralFactors: any): number {
    // Simplified behavioral risk calculation
    let risk = 0;
    
    if (behavioralFactors.adherenceHistory === 'poor') risk += 20;
    if (behavioralFactors.healthSeekingBehavior === 'delayed') risk += 15;
    
    return Math.min(100, risk);
  }

  private calculateRegionalRiskScore(regionalFactors: RegionalRiskFactor[]): number {
    if (regionalFactors.length === 0) return 0;
    
    const totalScore = regionalFactors.reduce((sum, factor) => sum + factor.riskScore, 0);
    return totalScore / regionalFactors.length;
  }

  private calculateModifiableRiskScore(modifiableFactors: ModifiableRiskFactor[]): number {
    if (modifiableFactors.length === 0) return 0;
    
    // Calculate based on current levels of modifiable factors
    let totalRisk = 0;
    for (const factor of modifiableFactors) {
      const riskValue = factor.currentLevel === 'high' ? 30 : 
                       factor.currentLevel === 'moderate' ? 15 : 5;
      totalRisk += riskValue;
    }
    
    return Math.min(100, totalRisk / modifiableFactors.length);
  }

  private calculateNonModifiableRiskScore(nonModifiableFactors: NonModifiableRiskFactor[]): number {
    if (nonModifiableFactors.length === 0) return 0;
    
    const totalRisk = nonModifiableFactors.reduce((sum, factor) => sum + factor.riskContribution, 0);
    return Math.min(100, totalRisk / nonModifiableFactors.length);
  }
}

export const riskCalculationService = new RiskCalculationService();
