/**
 * CORE RATE LIMITING LOGIC
 * 
 * Extracted from rateLimitingMiddleware.ts for better maintainability.
 * Contains the core rate limiting algorithms and decision logic.
 */

import type { Request } from 'express';
import type { UserRole } from '../../types/auth';

export interface RateLimitRequest extends Request {
  user?: {
    id: string;
    role: UserRole;
    permissions: string[];
    emergencyOverride?: boolean;
  };
  rateLimitInfo?: {
    requestCount: number;
    windowStart: number;
    isEmergency: boolean;
    bypassReason?: string;
  };
}

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  emergencyBypass: boolean;
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
  keyGenerator?: (req: RateLimitRequest) => string;
  onLimitReached?: (req: RateLimitRequest) => void;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
  bypassReason?: string;
}

/**
 * Core rate limiting decision engine
 */
export class RateLimitCore {
  private requestCounts = new Map<string, { count: number; windowStart: number }>();
  
  constructor(private config: RateLimitConfig) {}

  /**
   * Check if request should be rate limited
   */
  checkRateLimit(req: RateLimitRequest): RateLimitResult {
    const key = this.generateKey(req);
    const now = Date.now();
    const windowStart = Math.floor(now / this.config.windowMs) * this.config.windowMs;
    
    // Check for emergency bypass
    if (this.shouldBypassForEmergency(req)) {
      return {
        allowed: true,
        remaining: this.config.maxRequests,
        resetTime: windowStart + this.config.windowMs,
        bypassReason: 'emergency_override'
      };
    }

    // Get or create request count for this window
    const existing = this.requestCounts.get(key);
    let count = 0;
    
    if (existing && existing.windowStart === windowStart) {
      count = existing.count;
    }

    // Check if limit exceeded
    if (count >= this.config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: windowStart + this.config.windowMs,
        retryAfter: Math.ceil((windowStart + this.config.windowMs - now) / 1000)
      };
    }

    // Increment count
    this.requestCounts.set(key, { count: count + 1, windowStart });
    
    return {
      allowed: true,
      remaining: this.config.maxRequests - count - 1,
      resetTime: windowStart + this.config.windowMs
    };
  }

  /**
   * Generate unique key for rate limiting
   */
  private generateKey(req: RateLimitRequest): string {
    if (this.config.keyGenerator) {
      return this.config.keyGenerator(req);
    }
    
    // Default key generation
    const userId = req.user?.id || 'anonymous';
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    return `${userId}:${ip}`;
  }

  /**
   * Check if request should bypass rate limiting for emergency
   */
  private shouldBypassForEmergency(req: RateLimitRequest): boolean {
    if (!this.config.emergencyBypass) {
      return false;
    }

    // Check for emergency override flag
    if (req.user?.emergencyOverride) {
      return true;
    }

    // Check for emergency-related endpoints
    const emergencyPaths = [
      '/api/emergency',
      '/api/consultation/emergency',
      '/api/agents/emergency'
    ];

    return emergencyPaths.some(path => req.path.startsWith(path));
  }

  /**
   * Clean up old entries to prevent memory leaks
   */
  cleanup(): void {
    const now = Date.now();
    const cutoff = now - this.config.windowMs * 2; // Keep 2 windows worth of data
    
    for (const [key, data] of this.requestCounts.entries()) {
      if (data.windowStart < cutoff) {
        this.requestCounts.delete(key);
      }
    }
  }
}

/**
 * Default configurations for different endpoint types
 */
export const defaultConfigs = {
  medical: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    emergencyBypass: true,
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  },
  
  emergency: {
    windowMs: 60 * 1000, // 1 minute  
    maxRequests: 1000, // Higher limit for emergency
    emergencyBypass: true,
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  },
  
  authentication: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // Stricter for auth
    emergencyBypass: false,
    skipSuccessfulRequests: true,
    skipFailedRequests: false
  }
};
