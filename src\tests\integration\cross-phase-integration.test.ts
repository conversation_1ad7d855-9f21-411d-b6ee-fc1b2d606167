/**
 * CROSS-PHASE INTEGRATION TEST SUITE
 * 
 * Comprehensive integration tests covering all three phases of VoiceHealth AI:
 * - Phase 1 → Phase 2 Integration (Services with Authentication & Encryption)
 * - Phase 1 → Phase 3 Integration (Services with Monitoring & Security)
 * - Phase 2 → Phase 3 Integration (Auth/Encryption with Monitoring)
 * - End-to-End Workflow Integration
 * 
 * TARGET: 90%+ integration coverage across all service boundaries
 */

// Vitest globals are available via vitest.d.ts
import { 
  aiOrchestrator,
  clinicalDocumentationService,
  advancedRiskStratificationService,
  culturalValidationService,
  authenticationService,
  encryptionService,
  performanceValidationService,
  productionMonitoringDashboard,
  securityAuditService
} from '../../services';

// =====================================================
// SHARED MOCK SETUP FUNCTIONS
// =====================================================

const createMockUser = (role = 'provider', permissions = ['clinical_documentation', 'risk_assessment']) => ({
  id: `${role}-123`,
  email: `${role}@hospital.com`,
  role,
  permissions: permissions.map(resource => ({ resource, actions: ['create', 'read', 'update'] }))
});

const createMockDocumentation = () => ({
  success: true,
  documentation: {
    clinicalNote: {
      id: 'note-123',
      chiefComplaint: 'Patient presents with headache',
      historyOfPresentIllness: 'Severe headache for 3 days'
    },
    culturalAdaptations: [{
      aspect: 'family_involvement',
      adaptation: 'Include family in treatment decisions'
    }],
    qualityMetrics: {
      completeness: 92,
      accuracy: 88,
      culturalSensitivity: 95
    }
  }
});

const createMockRiskAssessment = () => ({
  success: true,
  riskScore: 75,
  riskCategory: 'moderate',
  recommendations: ['Monitor blood pressure', 'Follow up in 2 weeks']
});

const createMockValidation = () => ({
  success: true,
  overallScore: 85,
  validationStatus: 'approved'
});

const setupAuthenticationMocks = (user = createMockUser()) => {
  vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(user);
  vi.spyOn(authenticationService, 'hasPermission').mockResolvedValue(true);
};

const setupPerformanceMocks = () => {
  vi.spyOn(performanceValidationService, 'recordMetric').mockImplementation(() => {});
};

// =====================================================
// PHASE 1 → PHASE 2 INTEGRATION TESTS
// =====================================================

describe('Phase 1 → Phase 2 Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Clinical Documentation with Authentication', () => {
    it('should validate authentication before generating clinical documentation', async () => {
      // Setup shared mocks
      setupAuthenticationMocks();
      const mockDocumentation = createMockDocumentation();
      vi.spyOn(clinicalDocumentationService, 'convertVoiceToStructuredNote')
        .mockResolvedValue(mockDocumentation);

      const result = await aiOrchestrator.generateClinicalDocumentation(
        'Patient presents with severe headache for 3 days',
        'patient-123',
        'provider-123',
        { cultureCode: 'akan', familyInvolvementLevel: 'high' }
      );

      expect(result.success).toBe(true);
      expect(authenticationService.getCurrentUser).toHaveBeenCalled();
      expect(authenticationService.hasPermission).toHaveBeenCalledWith(
        mockUser, 'clinical_documentation', 'create'
      );
      expect(clinicalDocumentationService.generateVoiceToNote).toHaveBeenCalled();
    });

    it('should reject clinical documentation for unauthorized users', async () => {
      vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(null);

      await expect(
        aiOrchestrator.generateClinicalDocumentation(
          'Test transcription',
          'patient-123',
          'unauthorized-user'
        )
      ).rejects.toThrow('Authentication required');
    });

    it('should reject clinical documentation for insufficient permissions', async () => {
      const mockUser = createMockUser('patient', ['patient_data']);
      vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(mockUser);
      vi.spyOn(authenticationService, 'hasPermission').mockResolvedValue(false);

      await expect(
        aiOrchestrator.generateClinicalDocumentation(
          'Test transcription',
          'patient-123',
          'patient-123'
        )
      ).rejects.toThrow('Insufficient permissions');
    });
  });

  describe('Risk Stratification with Authentication', () => {
    it('should validate authentication before risk assessment', async () => {
      setupAuthenticationMocks();
      const mockRiskAssessment = createMockRiskAssessment();
      vi.spyOn(advancedRiskStratificationService, 'performRiskAssessment')
        .mockResolvedValue(mockRiskAssessment);

      const patientData = {
        id: 'patient-123',
        demographics: { age: 45, gender: 'female' },
        medicalHistory: ['hypertension']
      };

      const result = await aiOrchestrator.performRiskStratification(
        patientData,
        { cultureCode: 'akan' }
      );

      expect(result.success).toBe(true);
      expect(authenticationService.hasPermission).toHaveBeenCalledWith(
        mockUser, 'patient_data', 'read'
      );
    });
  });

  describe('Cultural Validation with Authentication', () => {
    it('should validate authentication before cultural validation', async () => {
      const mockUser = {
        id: 'cultural-expert-123',
        role: 'cultural_expert',
        permissions: [
          { resource: 'cultural_validation', actions: ['create', 'read'] }
        ]
      };

      vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(mockUser);
      vi.spyOn(authenticationService, 'hasPermission').mockResolvedValue(true);

      const mockValidation = {
        overallScore: 88,
        culturalSensitivity: { score: 92 },
        biasDetection: { biasDetected: false }
      };

      vi.spyOn(culturalValidationService, 'validateCulturalContent')
        .mockResolvedValue(mockValidation);

      const result = await aiOrchestrator.validateCulturalContent(
        'Please take your medication as prescribed',
        { cultureCode: 'akan', languagePreference: 'en' }
      );

      expect(result.success).toBe(true);
      expect(authenticationService.hasPermission).toHaveBeenCalledWith(
        mockUser, 'cultural_validation', 'create'
      );
    });
  });

  describe('PHI Encryption Integration', () => {
    it('should encrypt clinical documentation before storage', async () => {
      const mockUser = {
        id: 'provider-123',
        role: 'provider',
        permissions: [{ resource: 'clinical_documentation', actions: ['create'] }]
      };

      vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(mockUser);
      vi.spyOn(authenticationService, 'hasPermission').mockResolvedValue(true);

      const mockEncryptedData = {
        data: 'encrypted_clinical_note_data',
        algorithm: 'AES-256-GCM',
        keyVersion: 'v1',
        encryptedAt: new Date()
      };

      vi.spyOn(encryptionService, 'encryptPHI').mockResolvedValue(mockEncryptedData);

      const mockDocumentation = {
        success: true,
        documentation: {
          clinicalNote: {
            content: 'Patient has severe headache',
            encryptedContent: mockEncryptedData.data
          }
        }
      };

      vi.spyOn(clinicalDocumentationService, 'generateVoiceToNote')
        .mockResolvedValue(mockDocumentation);

      const result = await aiOrchestrator.generateClinicalDocumentation(
        'Patient has severe headache',
        'patient-123',
        'provider-123'
      );

      expect(result.success).toBe(true);
      expect(encryptionService.encryptPHI).toHaveBeenCalledWith(
        expect.any(String),
        'patient-123'
      );
    });

    it('should encrypt risk assessment data before storage', async () => {
      const mockUser = {
        id: 'provider-123',
        role: 'provider',
        permissions: [{ resource: 'risk_assessment', actions: ['create'] }]
      };

      vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(mockUser);
      vi.spyOn(authenticationService, 'hasPermission').mockResolvedValue(true);

      const mockEncryptedData = {
        data: 'encrypted_risk_data',
        algorithm: 'AES-256-GCM',
        keyVersion: 'v1'
      };

      vi.spyOn(encryptionService, 'encryptPHI').mockResolvedValue(mockEncryptedData);

      const mockRiskAssessment = {
        overallRiskScore: 65,
        encryptedData: mockEncryptedData.data
      };

      vi.spyOn(advancedRiskStratificationService, 'performRiskAssessment')
        .mockResolvedValue(mockRiskAssessment);

      const patientData = {
        id: 'patient-123',
        demographics: { age: 45, gender: 'female' }
      };

      const result = await aiOrchestrator.performRiskStratification(patientData);

      expect(result.success).toBe(true);
      expect(encryptionService.encryptPHI).toHaveBeenCalled();
    });
  });
});

// =====================================================
// PHASE 1 → PHASE 3 INTEGRATION TESTS
// =====================================================

describe('Phase 1 → Phase 3 Integration', () => {
  describe('Services with Performance Monitoring', () => {
    it('should record performance metrics for clinical documentation', async () => {
      const mockUser = {
        id: 'provider-123',
        role: 'provider',
        permissions: [{ resource: 'clinical_documentation', actions: ['create'] }]
      };

      vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(mockUser);
      vi.spyOn(authenticationService, 'hasPermission').mockResolvedValue(true);

      const mockDocumentation = {
        success: true,
        documentation: { clinicalNote: { id: 'note-123' } }
      };

      vi.spyOn(clinicalDocumentationService, 'generateVoiceToNote')
        .mockResolvedValue(mockDocumentation);

      vi.spyOn(performanceValidationService, 'recordMetric').mockImplementation(() => {});

      const startTime = Date.now();
      const result = await aiOrchestrator.generateClinicalDocumentation(
        'Test transcription',
        'patient-123',
        'provider-123'
      );
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(performanceValidationService.recordMetric).toHaveBeenCalledWith({
        operation: 'clinical_documentation_generation',
        responseTime: expect.any(Number),
        success: true,
        timestamp: expect.any(Date),
        metadata: expect.any(Object)
      });

      // Verify response time is reasonable
      const responseTime = endTime - startTime;
      expect(responseTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should record performance metrics for risk stratification', async () => {
      const mockUser = {
        id: 'provider-123',
        role: 'provider',
        permissions: [{ resource: 'risk_assessment', actions: ['create'] }]
      };

      vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(mockUser);
      vi.spyOn(authenticationService, 'hasPermission').mockResolvedValue(true);

      const mockRiskAssessment = {
        overallRiskScore: 65,
        riskCategory: 'moderate'
      };

      vi.spyOn(advancedRiskStratificationService, 'performRiskAssessment')
        .mockResolvedValue(mockRiskAssessment);

      vi.spyOn(performanceValidationService, 'recordMetric').mockImplementation(() => {});

      const patientData = {
        id: 'patient-123',
        demographics: { age: 45, gender: 'female' }
      };

      const result = await aiOrchestrator.performRiskStratification(patientData);

      expect(result.success).toBe(true);
      expect(performanceValidationService.recordMetric).toHaveBeenCalledWith({
        operation: 'risk_stratification',
        responseTime: expect.any(Number),
        success: true,
        timestamp: expect.any(Date),
        metadata: expect.objectContaining({
          patientId: 'patient-123',
          riskScore: 65
        })
      });
    });

    it('should record performance metrics for cultural validation', async () => {
      const mockUser = {
        id: 'cultural-expert-123',
        role: 'cultural_expert',
        permissions: [{ resource: 'cultural_validation', actions: ['create'] }]
      };

      vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(mockUser);
      vi.spyOn(authenticationService, 'hasPermission').mockResolvedValue(true);

      const mockValidation = {
        overallScore: 88,
        culturalSensitivity: { score: 92 }
      };

      vi.spyOn(culturalValidationService, 'validateCulturalContent')
        .mockResolvedValue(mockValidation);

      vi.spyOn(performanceValidationService, 'recordMetric').mockImplementation(() => {});

      const result = await aiOrchestrator.validateCulturalContent(
        'Test content',
        { cultureCode: 'akan' }
      );

      expect(result.success).toBe(true);
      expect(performanceValidationService.recordMetric).toHaveBeenCalledWith({
        operation: 'cultural_validation',
        responseTime: expect.any(Number),
        success: true,
        timestamp: expect.any(Date),
        metadata: expect.objectContaining({
          cultureCode: 'akan',
          overallScore: 88
        })
      });
    });
  });

  describe('Services with Security Monitoring', () => {
    it('should trigger security audit for sensitive operations', async () => {
      const mockUser = {
        id: 'provider-123',
        role: 'provider',
        permissions: [{ resource: 'clinical_documentation', actions: ['create'] }]
      };

      vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(mockUser);
      vi.spyOn(authenticationService, 'hasPermission').mockResolvedValue(true);

      vi.spyOn(securityAuditService, 'performVulnerabilityAssessment')
        .mockResolvedValue([]);

      const mockDocumentation = {
        success: true,
        documentation: { clinicalNote: { id: 'note-123' } }
      };

      vi.spyOn(clinicalDocumentationService, 'generateVoiceToNote')
        .mockResolvedValue(mockDocumentation);

      const result = await aiOrchestrator.generateClinicalDocumentation(
        'Sensitive patient information',
        'patient-123',
        'provider-123'
      );

      expect(result.success).toBe(true);
      // Security audit should be triggered for sensitive operations
      expect(securityAuditService.performVulnerabilityAssessment).toHaveBeenCalled();
    });

    it('should monitor for suspicious access patterns', async () => {
      const mockUser = {
        id: 'suspicious-user-123',
        role: 'provider',
        permissions: [{ resource: 'patient_data', actions: ['read'] }]
      };

      vi.spyOn(authenticationService, 'getCurrentUser').mockResolvedValue(mockUser);
      vi.spyOn(authenticationService, 'hasPermission').mockResolvedValue(true);

      vi.spyOn(productionMonitoringDashboard, 'getSystemHealthOverview')
        .mockResolvedValue({
          timestamp: new Date(),
          overallStatus: 'healthy',
          components: [],
          emergencyCompliance: true,
          culturalAdaptationScore: 92,
          hipaaCompliance: true,
          activeAlerts: 0,
          performanceSummary: {
            emergencyResponseTime: 1.8,
            authenticationTime: 0.4,
            apiResponseTime: 0.8,
            databaseQueryTime: 150,
            throughput: 100,
            errorRate: 0.1
          }
        });

      // Simulate multiple rapid access attempts
      const promises = Array.from({ length: 10 }, () =>
        aiOrchestrator.performRiskStratification({
          id: `patient-${Math.random()}`,
          demographics: { age: 30, gender: 'male' }
        })
      );

      await Promise.allSettled(promises);

      // Monitoring should detect the pattern
      expect(productionMonitoringDashboard.getSystemHealthOverview).toHaveBeenCalled();
    });
  });
});

// =====================================================
// PHASE 2 → PHASE 3 INTEGRATION TESTS
// =====================================================

describe('Phase 2 → Phase 3 Integration', () => {
  describe('Authentication with Monitoring', () => {
    it('should monitor authentication performance', async () => {
      vi.spyOn(performanceValidationService, 'recordMetric').mockImplementation(() => {});

      const authRequest = {
        email: '<EMAIL>',
        password: 'securePassword123',
        clientInfo: {
          userAgent: 'VoiceHealth App/1.0',
          ipAddress: '*************'
        }
      };

      // Mock successful authentication
      vi.spyOn(authenticationService['supabase'].auth, 'signInWithPassword')
        .mockResolvedValue({
          data: {
            user: { id: 'provider-123', email: '<EMAIL>' },
            session: { access_token: 'mock-token' }
          },
          error: null
        });

      const startTime = Date.now();
      const result = await authenticationService.authenticate(authRequest);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(performanceValidationService.recordMetric).toHaveBeenCalledWith({
        operation: 'authentication',
        responseTime: expect.any(Number),
        success: true,
        timestamp: expect.any(Date),
        category: 'authentication',
        target: 500 // Normal auth target: 500ms
      });

      // Verify authentication performance target
      const responseTime = endTime - startTime;
      expect(responseTime).toBeLessThan(500); // Normal auth should be < 500ms
    });

    it('should monitor emergency authentication performance', async () => {
      vi.spyOn(performanceValidationService, 'recordMetric').mockImplementation(() => {});

      const emergencyAuthRequest = {
        email: '<EMAIL>',
        password: 'emergency',
        emergencyOverride: true,
        clientInfo: {
          userAgent: 'Emergency Response System',
          ipAddress: '*************'
        }
      };

      // Mock emergency authentication
      vi.spyOn(authenticationService, 'authenticate').mockResolvedValue({
        success: true,
        user: {
          id: 'emergency-123',
          role: 'emergency',
          emergencyAccess: true
        },
        emergencyBypass: true
      });

      const startTime = Date.now();
      const result = await authenticationService.authenticate(emergencyAuthRequest);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.emergencyBypass).toBe(true);

      // Emergency auth should be much faster
      const responseTime = endTime - startTime;
      expect(responseTime).toBeLessThan(50); // Emergency auth should be < 50ms
    });
  });

  describe('Encryption with Security Monitoring', () => {
    it('should monitor encryption performance and security', async () => {
      vi.spyOn(performanceValidationService, 'recordMetric').mockImplementation(() => {});
      vi.spyOn(securityAuditService, 'performComprehensiveAudit')
        .mockResolvedValue({
          auditId: 'audit-123',
          timestamp: new Date(),
          auditType: 'comprehensive',
          overallScore: 95,
          riskLevel: 'low',
          findings: [],
          complianceStatus: {
            hipaa: { status: 'compliant', score: 100 },
            gdpr: { status: 'compliant', score: 100 },
            regional: {},
            overall: 'compliant'
          },
          recommendations: [],
          nextAuditDate: new Date()
        });

      const testData = 'Sensitive patient information';
      const patientId = 'patient-123';

      const startTime = Date.now();
      const encryptedData = await encryptionService.encryptPHI(testData, patientId);
      const endTime = Date.now();

      expect(encryptedData.algorithm).toBe('AES-256-GCM');
      expect(performanceValidationService.recordMetric).toHaveBeenCalledWith({
        operation: 'phi_encryption',
        responseTime: expect.any(Number),
        success: true,
        timestamp: expect.any(Date),
        metadata: expect.objectContaining({
          algorithm: 'AES-256-GCM',
          dataSize: testData.length
        })
      });

      // Verify encryption performance
      const responseTime = endTime - startTime;
      expect(responseTime).toBeLessThan(1000); // Encryption should be < 1 second
    });
  });
});
