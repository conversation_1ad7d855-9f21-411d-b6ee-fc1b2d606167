/**
 * MEDICAL DATA TYPES AND INTERFACES
 * 
 * Extracted from OptimizedMedicalDataContext for better maintainability.
 * Contains all type definitions for medical data management.
 */

import type { MedicalCondition, Medication, Symptom } from '../../types/medical';

export interface MedicalDataState {
  conditions: MedicalCondition[];
  medications: Medication[];
  symptoms: Symptom[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: number;
  syncStatus: 'idle' | 'syncing' | 'error' | 'offline';
}

export interface MedicalDataActions {
  addCondition: (condition: MedicalCondition) => Promise<void>;
  updateCondition: (id: string, updates: Partial<MedicalCondition>) => Promise<void>;
  removeCondition: (id: string) => Promise<void>;
  
  addMedication: (medication: Medication) => Promise<void>;
  updateMedication: (id: string, updates: Partial<Medication>) => Promise<void>;
  removeMedication: (id: string) => Promise<void>;
  
  addSymptom: (symptom: Symptom) => Promise<void>;
  updateSymptom: (id: string, updates: Partial<Symptom>) => Promise<void>;
  removeSymptom: (id: string) => Promise<void>;
  
  syncData: () => Promise<void>;
  clearCache: () => void;
  refreshData: () => Promise<void>;
}

export interface MedicalDataContext {
  state: MedicalDataState;
  actions: MedicalDataActions;
  selectors: {
    getConditionById: (id: string) => MedicalCondition | undefined;
    getMedicationById: (id: string) => Medication | undefined;
    getSymptomById: (id: string) => Symptom | undefined;
    getActiveConditions: () => MedicalCondition[];
    getCurrentMedications: () => Medication[];
    getRecentSymptoms: (days?: number) => Symptom[];
  };
}

export interface CacheConfig {
  maxAge: number; // milliseconds
  maxSize: number; // number of items
  enableOffline: boolean;
  syncInterval: number; // milliseconds
}

export interface SyncOptions {
  force?: boolean;
  priority?: 'low' | 'normal' | 'high' | 'emergency';
  timeout?: number;
}

export const defaultCacheConfig: CacheConfig = {
  maxAge: 5 * 60 * 1000, // 5 minutes
  maxSize: 1000,
  enableOffline: true,
  syncInterval: 30 * 1000 // 30 seconds
};

export const initialMedicalDataState: MedicalDataState = {
  conditions: [],
  medications: [],
  symptoms: [],
  isLoading: false,
  error: null,
  lastUpdated: 0,
  syncStatus: 'idle'
};
