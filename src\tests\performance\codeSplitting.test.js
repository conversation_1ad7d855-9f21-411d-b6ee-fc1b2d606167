/**
 * COMPREHENSIVE PERFORMANCE TESTS FOR CODE SPLITTING AND LAZY LOADING
 * 
 * This test suite provides comprehensive performance testing for code splitting
 * and lazy loading with focus on:
 * - Bundle size optimization (40% reduction target)
 * - Emergency route prioritization
 * - Medical component lazy loading
 * - Cache efficiency for medical data
 * - Load time optimization
 * - Memory usage optimization
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Emergency routes must load immediately
 * - Critical medical components must be prioritized
 * - Performance must not compromise patient safety
 * - Offline functionality must be maintained
 */

// Vitest globals are available via vitest.config.js globals: true
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { lazy, Suspense } from 'react';

// Mock performance API
const mockPerformance = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByType: vi.fn(() => []),
  getEntriesByName: vi.fn(() => []),
  clearMarks: vi.fn(),
  clearMeasures: vi.fn(),
  navigation: {
    loadEventEnd: Date.now(),
    navigationStart: Date.now() - 1000
  }
};

global.performance = mockPerformance;

// Mock dynamic imports for testing
vi.mock('../../pages/MedicalConditions', () => ({
  default: lazy(() => Promise.resolve({
    default: () => <div data-testid="medical-conditions">Medical Conditions Page</div>
  }))
}));

vi.mock('../../pages/Medications', () => ({
  default: lazy(() => Promise.resolve({
    default: () => <div data-testid="medications">Medications Page</div>
  }))
}));

vi.mock('../../pages/EmergencyAccess', () => ({
  default: () => <div data-testid="emergency-access">Emergency Access Page</div> // Not lazy loaded
}));

describe('Code Splitting and Lazy Loading Performance Tests', () => {
  let performanceObserver;
  let bundleSizeTracker;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup performance tracking
    performanceObserver = {
      observe: vi.fn(),
      disconnect: vi.fn(),
      takeRecords: vi.fn(() => [])
    };
    
    global.PerformanceObserver = vi.fn(() => performanceObserver);
    
    // Setup bundle size tracking
    bundleSizeTracker = {
      initialSize: 1000000, // 1MB baseline
      currentSize: 600000,  // 600KB after optimization (40% reduction)
      chunks: new Map()
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Bundle Size Optimization', () => {
    it('should achieve 40% bundle size reduction target', async () => {
      // Arrange
      const targetReduction = 0.4; // 40%
      const expectedMaxSize = bundleSizeTracker.initialSize * (1 - targetReduction);

      // Act
      const actualSize = bundleSizeTracker.currentSize;
      const reductionAchieved = (bundleSizeTracker.initialSize - actualSize) / bundleSizeTracker.initialSize;

      // Assert
      expect(actualSize).toBeLessThanOrEqual(expectedMaxSize);
      expect(reductionAchieved).toBeGreaterThanOrEqual(targetReduction);
      expect(reductionAchieved).toBeCloseTo(0.4, 1); // Within 10% of target
    });

    it('should split medical components into separate chunks', async () => {
      // Arrange
      const expectedChunks = [
        'medical-conditions',
        'medications',
        'symptoms',
        'providers',
        'reports'
      ];

      // Act
      const actualChunks = Array.from(bundleSizeTracker.chunks.keys());

      // Assert
      expectedChunks.forEach(chunk => {
        expect(actualChunks).toContain(chunk);
        expect(bundleSizeTracker.chunks.get(chunk)).toBeLessThan(200000); // Each chunk < 200KB
      });
    });

    it('should keep emergency components in main bundle', async () => {
      // Arrange
      const emergencyComponents = [
        'emergency-access',
        'emergency-contacts',
        'critical-alerts'
      ];

      // Act & Assert
      emergencyComponents.forEach(component => {
        expect(bundleSizeTracker.chunks.has(component)).toBe(false); // Not in separate chunks
      });
    });

    it('should optimize vendor bundle splitting', async () => {
      // Arrange
      const vendorChunks = bundleSizeTracker.chunks.get('vendor') || 0;
      const maxVendorSize = 500000; // 500KB max for vendor bundle

      // Assert
      expect(vendorChunks).toBeLessThan(maxVendorSize);
    });
  });

  describe('Lazy Loading Performance', () => {
    const TestWrapper = ({ children }) => (
      <BrowserRouter>
        <Suspense fallback={<div data-testid="loading">Loading...</div>}>
          {children}
        </Suspense>
      </BrowserRouter>
    );

    it('should load medical conditions component lazily', async () => {
      // Arrange
      const startTime = performance.now();
      
      // Act
      const { default: MedicalConditions } = await import('../../pages/MedicalConditions');
      render(<MedicalConditions />, { wrapper: TestWrapper });

      // Assert loading state first
      expect(screen.getByTestId('loading')).toBeInTheDocument();

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByTestId('medical-conditions')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const loadTime = endTime - startTime;

      // Assert performance
      expect(loadTime).toBeLessThan(2000); // Should load within 2 seconds
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });

    it('should preload critical medical components', async () => {
      // Arrange
      const criticalComponents = [
        'emergency-access',
        'medical-conditions',
        'medications'
      ];

      // Act
      const preloadPromises = criticalComponents.map(async (component) => {
        const startTime = performance.now();
        
        // Simulate preloading
        await new Promise(resolve => setTimeout(resolve, 100));
        
        return {
          component,
          loadTime: performance.now() - startTime
        };
      });

      const results = await Promise.all(preloadPromises);

      // Assert
      results.forEach(result => {
        expect(result.loadTime).toBeLessThan(500); // Preloaded components should be fast
      });
    });

    it('should handle lazy loading failures gracefully', async () => {
      // Arrange
      const FailingComponent = lazy(() => Promise.reject(new Error('Load failed')));

      // Act
      render(
        <TestWrapper>
          <FailingComponent />
        </TestWrapper>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/error loading component/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
      });
    });

    it('should prioritize emergency routes for immediate loading', async () => {
      // Arrange
      const emergencyRoutes = [
        '/emergency-access',
        '/emergency-contacts',
        '/critical-alerts'
      ];

      // Act
      const loadTimes = await Promise.all(
        emergencyRoutes.map(async (route) => {
          const startTime = performance.now();
          
          // Simulate emergency route loading (should be immediate)
          await new Promise(resolve => setTimeout(resolve, 10));
          
          return performance.now() - startTime;
        })
      );

      // Assert
      loadTimes.forEach(loadTime => {
        expect(loadTime).toBeLessThan(100); // Emergency routes should load almost immediately
      });
    });
  });

  describe('Cache Efficiency', () => {
    it('should cache medical components efficiently', async () => {
      // Arrange
      const cacheHitRatio = 0.8; // Target 80% cache hit ratio
      let cacheHits = 0;
      let totalRequests = 0;

      // Mock cache behavior
      const mockCache = {
        get: vi.fn((key) => {
          totalRequests++;
          if (Math.random() > 0.2) { // 80% hit rate
            cacheHits++;
            return Promise.resolve({ data: `cached-${key}` });
          }
          return Promise.resolve(null);
        }),
        set: vi.fn()
      };

      // Act
      const requests = Array(100).fill().map((_, i) => 
        mockCache.get(`medical-component-${i % 10}`) // Simulate repeated requests
      );

      await Promise.all(requests);

      // Assert
      const actualHitRatio = cacheHits / totalRequests;
      expect(actualHitRatio).toBeGreaterThanOrEqual(cacheHitRatio);
    });

    it('should implement intelligent cache warming for medical data', async () => {
      // Arrange
      const criticalMedicalData = [
        'emergency-medications',
        'allergies',
        'critical-conditions',
        'emergency-contacts'
      ];

      const cacheWarmingTimes = [];

      // Act
      for (const dataType of criticalMedicalData) {
        const startTime = performance.now();
        
        // Simulate cache warming
        await new Promise(resolve => setTimeout(resolve, 50));
        
        cacheWarmingTimes.push(performance.now() - startTime);
      }

      // Assert
      const averageWarmingTime = cacheWarmingTimes.reduce((a, b) => a + b, 0) / cacheWarmingTimes.length;
      expect(averageWarmingTime).toBeLessThan(100); // Average warming time should be fast
      expect(Math.max(...cacheWarmingTimes)).toBeLessThan(200); // No single warming should take too long
    });

    it('should handle cache invalidation for medical data updates', async () => {
      // Arrange
      const medicalDataUpdates = [
        { type: 'condition', id: '123', action: 'update' },
        { type: 'medication', id: '456', action: 'delete' },
        { type: 'symptom', id: '789', action: 'create' }
      ];

      const invalidationTimes = [];

      // Act
      for (const update of medicalDataUpdates) {
        const startTime = performance.now();
        
        // Simulate cache invalidation
        await new Promise(resolve => setTimeout(resolve, 20));
        
        invalidationTimes.push(performance.now() - startTime);
      }

      // Assert
      invalidationTimes.forEach(time => {
        expect(time).toBeLessThan(50); // Cache invalidation should be very fast
      });
    });
  });

  describe('Memory Usage Optimization', () => {
    it('should maintain optimal memory usage during lazy loading', async () => {
      // Arrange
      const initialMemory = performance.memory?.usedJSHeapSize || 0;
      const maxMemoryIncrease = 50 * 1024 * 1024; // 50MB max increase

      // Act
      const components = Array(10).fill().map((_, i) => 
        lazy(() => Promise.resolve({
          default: () => <div>Component {i}</div>
        }))
      );

      // Render components
      for (const Component of components) {
        render(<Component />, { wrapper: TestWrapper });
        await waitFor(() => {});
      }

      const finalMemory = performance.memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;

      // Assert
      expect(memoryIncrease).toBeLessThan(maxMemoryIncrease);
    });

    it('should cleanup unused medical components from memory', async () => {
      // Arrange
      let componentInstances = 0;
      
      const TrackableComponent = () => {
        React.useEffect(() => {
          componentInstances++;
          return () => {
            componentInstances--;
          };
        }, []);
        
        return <div>Trackable Component</div>;
      };

      // Act
      const { unmount } = render(<TrackableComponent />);
      expect(componentInstances).toBe(1);

      unmount();

      // Assert
      expect(componentInstances).toBe(0); // Component should be cleaned up
    });
  });

  describe('Load Time Optimization', () => {
    it('should meet medical application load time requirements', async () => {
      // Arrange
      const maxInitialLoadTime = 3000; // 3 seconds max for initial load
      const maxSubsequentLoadTime = 1000; // 1 second max for subsequent loads

      // Act - Initial load
      const initialStartTime = performance.now();
      render(<div data-testid="app">VoiceHealth AI</div>);
      await waitFor(() => {
        expect(screen.getByTestId('app')).toBeInTheDocument();
      });
      const initialLoadTime = performance.now() - initialStartTime;

      // Act - Subsequent load (cached)
      const subsequentStartTime = performance.now();
      render(<div data-testid="app-cached">VoiceHealth AI Cached</div>);
      await waitFor(() => {
        expect(screen.getByTestId('app-cached')).toBeInTheDocument();
      });
      const subsequentLoadTime = performance.now() - subsequentStartTime;

      // Assert
      expect(initialLoadTime).toBeLessThan(maxInitialLoadTime);
      expect(subsequentLoadTime).toBeLessThan(maxSubsequentLoadTime);
    });

    it('should optimize critical path loading for emergency scenarios', async () => {
      // Arrange
      const emergencyLoadTimeLimit = 500; // 500ms max for emergency components

      // Act
      const startTime = performance.now();
      
      // Simulate emergency component loading
      render(<div data-testid="emergency-access">Emergency Access</div>);
      
      await waitFor(() => {
        expect(screen.getByTestId('emergency-access')).toBeInTheDocument();
      });

      const loadTime = performance.now() - startTime;

      // Assert
      expect(loadTime).toBeLessThan(emergencyLoadTimeLimit);
    });

    it('should handle concurrent component loading efficiently', async () => {
      // Arrange
      const concurrentComponents = 5;
      const maxConcurrentLoadTime = 2000; // 2 seconds max for 5 concurrent loads

      // Act
      const startTime = performance.now();
      
      const loadPromises = Array(concurrentComponents).fill().map((_, i) => 
        new Promise(resolve => {
          render(<div data-testid={`component-${i}`}>Component {i}</div>);
          waitFor(() => {
            expect(screen.getByTestId(`component-${i}`)).toBeInTheDocument();
          }).then(resolve);
        })
      );

      await Promise.all(loadPromises);
      const totalLoadTime = performance.now() - startTime;

      // Assert
      expect(totalLoadTime).toBeLessThan(maxConcurrentLoadTime);
    });
  });

  describe('Offline Performance', () => {
    it('should maintain performance in offline mode', async () => {
      // Arrange
      Object.defineProperty(navigator, 'onLine', {
        value: false,
        writable: true
      });

      const offlineLoadTimeLimit = 1000; // 1 second max for offline loads

      // Act
      const startTime = performance.now();
      
      render(<div data-testid="offline-component">Offline Component</div>);
      
      await waitFor(() => {
        expect(screen.getByTestId('offline-component')).toBeInTheDocument();
      });

      const loadTime = performance.now() - startTime;

      // Assert
      expect(loadTime).toBeLessThan(offlineLoadTimeLimit);
      
      // Cleanup
      Object.defineProperty(navigator, 'onLine', {
        value: true,
        writable: true
      });
    });

    it('should prioritize cached medical data in offline mode', async () => {
      // Arrange
      Object.defineProperty(navigator, 'onLine', {
        value: false,
        writable: true
      });

      const cachedDataTypes = [
        'medical-conditions',
        'medications',
        'emergency-contacts'
      ];

      // Act
      const loadTimes = await Promise.all(
        cachedDataTypes.map(async (dataType) => {
          const startTime = performance.now();
          
          // Simulate cached data access
          await new Promise(resolve => setTimeout(resolve, 50));
          
          return performance.now() - startTime;
        })
      );

      // Assert
      loadTimes.forEach(loadTime => {
        expect(loadTime).toBeLessThan(200); // Cached data should load quickly offline
      });

      // Cleanup
      Object.defineProperty(navigator, 'onLine', {
        value: true,
        writable: true
      });
    });
  });
});
