/**
 * AUTH STATE MANAGER
 * 
 * Handles authentication state management, session tracking, and user operations.
 * Extracted from OptimizedAuthContext for better maintainability.
 */

import type { User, UserProfile, AuthError, UserRole } from '../../types/auth';
import type { AuthState, AuthMetrics, AuthConfig, TokenInfo } from './AuthTypes';
import { initialAuthState, initialAuthMetrics } from './AuthTypes';
import auditLogger from '../../utils/auditLogger';

export class AuthStateManager {
  private state: AuthState = { ...initialAuthState };
  private metrics: AuthMetrics = { ...initialAuthMetrics };
  private sessionTimer?: NodeJS.Timeout;
  private refreshTimer?: NodeJS.Timeout;

  constructor(private config: AuthConfig) {
    this.setupSessionTimer();
  }

  /**
   * Get current auth state
   */
  getState(): AuthState {
    return { ...this.state };
  }

  /**
   * Get current metrics
   */
  getMetrics(): AuthMetrics {
    return { ...this.metrics };
  }

  /**
   * Update state and notify listeners
   */
  private setState(updates: Partial<AuthState>, callback?: () => void): void {
    this.state = { ...this.state, ...updates };
    if (callback) {
      callback();
    }
  }

  /**
   * Login user with credentials
   */
  async login(email: string, password: string): Promise<void> {
    try {
      this.setState({ isLoading: true, error: null });
      this.metrics.loginAttempts++;

      // Simulate authentication (replace with actual auth service)
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock successful login
      const user: User = {
        id: 'user-123',
        email,
        role: 'patient' as UserRole,
        permissions: ['read:profile', 'write:profile'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const profile: UserProfile = {
        id: 'profile-123',
        userId: user.id,
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        gender: 'male',
        phoneNumber: '+**********',
        emergencyContact: {
          name: 'Jane Doe',
          relationship: 'spouse',
          phoneNumber: '+**********'
        },
        medicalHistory: [],
        allergies: [],
        medications: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      this.setState({
        user,
        profile,
        isLoading: false,
        isAuthenticated: true,
        permissions: user.permissions,
        lastActivity: Date.now(),
        sessionExpiry: Date.now() + this.config.sessionTimeout
      });

      this.metrics.lastLoginTime = Date.now();
      this.setupSessionTimer();

      // Audit login
      await auditLogger.logSecurityEvent('user_login', 'low', {
        userId: user.id,
        email: user.email,
        timestamp: Date.now()
      });

    } catch (error) {
      this.setState({
        isLoading: false,
        error: {
          code: 'LOGIN_FAILED',
          message: error instanceof Error ? error.message : 'Login failed'
        }
      });
      throw error;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      const userId = this.state.user?.id;

      // Clear timers
      if (this.sessionTimer) {
        clearTimeout(this.sessionTimer);
      }
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
      }

      // Reset state
      this.setState({
        ...initialAuthState,
        lastActivity: Date.now()
      });

      // Reset metrics
      this.metrics = { ...initialAuthMetrics };

      // Audit logout
      if (userId) {
        await auditLogger.logSecurityEvent('user_logout', 'low', {
          userId,
          timestamp: Date.now()
        });
      }

    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<void> {
    try {
      if (!this.state.isAuthenticated) {
        return;
      }

      // Simulate token refresh
      await new Promise(resolve => setTimeout(resolve, 500));

      this.setState({
        sessionExpiry: Date.now() + this.config.sessionTimeout,
        lastActivity: Date.now()
      });

      this.metrics.tokenRefreshCount++;
      this.setupSessionTimer();

    } catch (error) {
      console.error('Token refresh error:', error);
      await this.logout();
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<UserProfile>): Promise<void> {
    try {
      if (!this.state.profile) {
        throw new Error('No profile to update');
      }

      this.setState({ isLoading: true });

      // Simulate profile update
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedProfile = {
        ...this.state.profile,
        ...updates,
        updatedAt: new Date().toISOString()
      };

      this.setState({
        profile: updatedProfile,
        isLoading: false,
        lastActivity: Date.now()
      });

      // Audit profile update
      await auditLogger.logDataAccess('user_profile', 'update', {
        userId: this.state.user?.id,
        profileId: updatedProfile.id,
        updatedFields: Object.keys(updates)
      });

    } catch (error) {
      this.setState({
        isLoading: false,
        error: {
          code: 'PROFILE_UPDATE_FAILED',
          message: error instanceof Error ? error.message : 'Profile update failed'
        }
      });
      throw error;
    }
  }

  /**
   * Clear authentication error
   */
  clearError(): void {
    this.setState({ error: null });
  }

  /**
   * Set emergency override
   */
  setEmergencyOverride(enabled: boolean): void {
    if (this.config.enableEmergencyOverride) {
      this.setState({ emergencyOverride: enabled });
      
      if (enabled) {
        this.metrics.emergencyOverrideCount++;
      }

      // Audit emergency override
      auditLogger.logSecurityEvent('emergency_override', 'high', {
        userId: this.state.user?.id,
        enabled,
        timestamp: Date.now()
      }).catch(console.error);
    }
  }

  /**
   * Check if user has specific permission
   */
  checkPermission(permission: string): boolean {
    this.metrics.permissionChecks++;
    return this.state.permissions.includes(permission);
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: UserRole): boolean {
    return this.state.user?.role === role;
  }

  /**
   * Update last activity timestamp
   */
  updateActivity(): void {
    this.setState({ lastActivity: Date.now() });
  }

  /**
   * Get session time remaining in milliseconds
   */
  getSessionTimeRemaining(): number {
    if (!this.state.isAuthenticated) {
      return 0;
    }
    return Math.max(0, this.state.sessionExpiry - Date.now());
  }

  /**
   * Setup session timeout timer
   */
  private setupSessionTimer(): void {
    if (this.sessionTimer) {
      clearTimeout(this.sessionTimer);
    }

    if (this.state.isAuthenticated) {
      const timeRemaining = this.getSessionTimeRemaining();
      
      if (timeRemaining > 0) {
        this.sessionTimer = setTimeout(() => {
          this.logout().catch(console.error);
        }, timeRemaining);
      }
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.sessionTimer) {
      clearTimeout(this.sessionTimer);
    }
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
  }
}
