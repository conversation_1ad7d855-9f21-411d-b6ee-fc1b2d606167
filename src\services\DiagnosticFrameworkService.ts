/**
 * DIAGNOSTIC FRAMEWORK SERVICE
 * 
 * Implements structured diagnostic methodologies (SOAP, OLDCARTS) for systematic
 * medical consultations. This service guides agents through comprehensive
 * patient assessment and ensures no critical information is missed.
 * 
 * Key Features:
 * - SOAP (Subjective, Objective, Assessment, Plan) framework
 * - OLDCARTS symptom exploration
 * - Conversation state management
 * - Systematic follow-up questions
 * - Clinical decision support
 */

export interface SOAPAssessment {
  subjective: SubjectiveData;
  objective: ObjectiveData;
  assessment: AssessmentData;
  plan: PlanData;
  currentPhase: 'subjective' | 'objective' | 'assessment' | 'plan' | 'complete';
  completionPercentage: number;
  nextSteps: string[];
  criticalFlags: string[];
}

export interface SubjectiveData {
  chiefComplaint: string;
  historyOfPresentIllness: HistoryOfPresentIllness;
  reviewOfSystems: ReviewOfSystems;
  pastMedicalHistory: string[];
  medications: string[];
  allergies: string[];
  socialHistory: SocialHistory;
  familyHistory: string[];
  completionStatus: {
    chiefComplaint: boolean;
    hpi: boolean;
    ros: boolean;
    pmh: boolean;
    medications: boolean;
    allergies: boolean;
    social: boolean;
    family: boolean;
  };
}

export interface HistoryOfPresentIllness {
  onset: string; // When did it start?
  location: string; // Where is it?
  duration: string; // How long?
  character: string; // What does it feel like?
  alleviatingFactors: string[]; // What makes it better?
  radiatingFactors: string[]; // Does it spread?
  timing: string; // When does it occur?
  severity: number; // 1-10 scale
  associatedSymptoms: string[];
  completionStatus: {
    onset: boolean;
    location: boolean;
    duration: boolean;
    character: boolean;
    alleviating: boolean;
    radiating: boolean;
    timing: boolean;
    severity: boolean;
    associated: boolean;
  };
}

export interface ReviewOfSystems {
  constitutional: string[]; // Fever, weight loss, fatigue
  cardiovascular: string[]; // Chest pain, palpitations
  respiratory: string[]; // Cough, shortness of breath
  gastrointestinal: string[]; // Nausea, vomiting, diarrhea
  genitourinary: string[]; // Urinary frequency, pain
  musculoskeletal: string[]; // Joint pain, muscle aches
  neurological: string[]; // Headache, dizziness
  psychiatric: string[]; // Mood changes, anxiety
  endocrine: string[]; // Heat/cold intolerance
  hematologic: string[]; // Easy bruising, bleeding
  completedSystems: string[];
}

export interface SocialHistory {
  smoking: string;
  alcohol: string;
  drugs: string;
  occupation: string;
  livingArrangement: string;
  recentTravel: string[];
  sexualHistory: string;
  exerciseHabits: string;
}

export interface ObjectiveData {
  vitalSigns: VitalSigns;
  physicalExam: PhysicalExam;
  diagnosticTests: DiagnosticTest[];
  observations: string[];
  limitations: string[]; // What we can't assess remotely
}

export interface VitalSigns {
  temperature: string;
  bloodPressure: string;
  heartRate: string;
  respiratoryRate: string;
  oxygenSaturation: string;
  weight: string;
  height: string;
  bmi: string;
  reportedBy: 'patient' | 'measured' | 'estimated';
}

export interface PhysicalExam {
  general: string;
  heent: string; // Head, Eyes, Ears, Nose, Throat
  cardiovascular: string;
  respiratory: string;
  abdominal: string;
  musculoskeletal: string;
  neurological: string;
  skin: string;
  limitations: string[]; // Remote consultation limitations
}

export interface DiagnosticTest {
  testName: string;
  result: string;
  date: string;
  interpretation: string;
  source: 'patient_reported' | 'recent_lab' | 'imaging';
}

export interface AssessmentData {
  differentialDiagnosis: DifferentialDiagnosis[];
  workingDiagnosis: string;
  riskStratification: RiskAssessment;
  redFlags: RedFlag[];
  clinicalImpression: string;
  confidence: number; // 0-1 scale
}

export interface DifferentialDiagnosis {
  diagnosis: string;
  probability: number; // 0-1 scale
  supportingEvidence: string[];
  contradictingEvidence: string[];
  nextStepsToConfirm: string[];
}

export interface RiskAssessment {
  overallRisk: 'low' | 'moderate' | 'high' | 'critical';
  immediateRisks: string[];
  longTermRisks: string[];
  riskFactors: string[];
  protectiveFactors: string[];
}

export interface RedFlag {
  flag: string;
  severity: 'warning' | 'urgent' | 'critical';
  action: string;
  timeframe: string;
}

export interface PlanData {
  diagnosticPlan: DiagnosticPlan;
  treatmentPlan: TreatmentPlan;
  followUpPlan: FollowUpPlan;
  patientEducation: PatientEducation;
  safetyNetting: SafetyNetting;
}

export interface DiagnosticPlan {
  recommendedTests: RecommendedTest[];
  referrals: Referral[];
  monitoring: MonitoringPlan[];
}

export interface RecommendedTest {
  testName: string;
  indication: string;
  urgency: 'routine' | 'urgent' | 'stat';
  timeframe: string;
  alternatives: string[];
}

export interface Referral {
  specialty: string;
  urgency: 'routine' | 'urgent' | 'emergent';
  reason: string;
  timeframe: string;
  preparation: string[];
}

export interface TreatmentPlan {
  medications: MedicationRecommendation[];
  nonPharmacological: NonPharmacologicalTreatment[];
  lifestyle: LifestyleRecommendation[];
  contraindications: string[];
}

export interface MedicationRecommendation {
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  indication: string;
  sideEffects: string[];
  interactions: string[];
  monitoring: string[];
}

export interface NonPharmacologicalTreatment {
  intervention: string;
  instructions: string;
  duration: string;
  expectedOutcome: string;
}

export interface LifestyleRecommendation {
  category: 'diet' | 'exercise' | 'sleep' | 'stress' | 'habits';
  recommendation: string;
  rationale: string;
  implementation: string[];
}

export interface FollowUpPlan {
  nextAppointment: string;
  monitoringSchedule: MonitoringSchedule[];
  warningSignsToWatch: string[];
  whenToSeekCare: WhenToSeekCare[];
}

export interface MonitoringSchedule {
  parameter: string;
  frequency: string;
  method: string;
  targetValues: string;
}

export interface WhenToSeekCare {
  symptoms: string[];
  urgency: 'routine' | 'urgent' | 'emergent';
  action: string;
  timeframe: string;
}

export interface PatientEducation {
  keyPoints: string[];
  resources: EducationalResource[];
  demonstrations: string[];
  comprehensionCheck: string[];
}

export interface EducationalResource {
  title: string;
  type: 'handout' | 'video' | 'website' | 'app';
  url?: string;
  description: string;
}

export interface SafetyNetting {
  redFlagSymptoms: string[];
  emergencyContacts: string[];
  backupPlans: string[];
  patientUnderstanding: boolean;
}

export interface ConversationState {
  currentPhase: 'greeting' | 'chief_complaint' | 'hpi' | 'ros' | 'pmh' | 'assessment' | 'plan' | 'closure';
  completedSections: string[];
  pendingQuestions: string[];
  criticalInformation: string[];
  nextRecommendedQuestions: string[];
  estimatedTimeRemaining: number;
}

class DiagnosticFrameworkService {
  constructor() {
    console.log('🩺 Diagnostic Framework Service initialized');
  }

  /**
   * Initialize SOAP assessment for a new consultation
   */
  initializeSOAPAssessment(chiefComplaint: string): SOAPAssessment {
    return {
      subjective: this.initializeSubjectiveData(chiefComplaint),
      objective: this.initializeObjectiveData(),
      assessment: this.initializeAssessmentData(),
      plan: this.initializePlanData(),
      currentPhase: 'subjective',
      completionPercentage: 0,
      nextSteps: this.generateInitialNextSteps(chiefComplaint),
      criticalFlags: []
    };
  }

  /**
   * Generate next questions based on current SOAP state
   */
  generateNextQuestions(soapAssessment: SOAPAssessment, lastResponse: string): string[] {
    const questions = [];

    switch (soapAssessment.currentPhase) {
      case 'subjective':
        questions.push(...this.generateSubjectiveQuestions(soapAssessment.subjective, lastResponse));
        break;
      case 'objective':
        questions.push(...this.generateObjectiveQuestions(soapAssessment.objective));
        break;
      case 'assessment':
        questions.push(...this.generateAssessmentQuestions(soapAssessment));
        break;
      case 'plan':
        questions.push(...this.generatePlanQuestions(soapAssessment));
        break;
    }

    return questions.slice(0, 3); // Limit to 3 questions at a time
  }

  /**
   * Update SOAP assessment with new information
   */
  updateSOAPAssessment(
    soapAssessment: SOAPAssessment, 
    newInformation: string, 
    informationType: string
  ): SOAPAssessment {
    const updated = { ...soapAssessment };

    // Process new information based on type
    switch (informationType) {
      case 'chief_complaint':
        updated.subjective.chiefComplaint = newInformation;
        updated.subjective.completionStatus.chiefComplaint = true;
        break;
      case 'hpi_onset':
        updated.subjective.historyOfPresentIllness.onset = newInformation;
        updated.subjective.historyOfPresentIllness.completionStatus.onset = true;
        break;
      case 'hpi_severity':
        const severity = this.extractSeverityScore(newInformation);
        updated.subjective.historyOfPresentIllness.severity = severity;
        updated.subjective.historyOfPresentIllness.completionStatus.severity = true;
        break;
      // Add more cases as needed
    }

    // Update completion percentage
    updated.completionPercentage = this.calculateCompletionPercentage(updated);

    // Update next steps
    updated.nextSteps = this.generateNextSteps(updated);

    // Check for critical flags
    updated.criticalFlags = this.identifyCriticalFlags(updated);

    return updated;
  }

  /**
   * Determine if consultation phase should advance
   */
  shouldAdvancePhase(soapAssessment: SOAPAssessment): boolean {
    switch (soapAssessment.currentPhase) {
      case 'subjective':
        return this.isSubjectiveComplete(soapAssessment.subjective);
      case 'objective':
        return this.isObjectiveComplete(soapAssessment.objective);
      case 'assessment':
        return this.isAssessmentComplete(soapAssessment.assessment);
      case 'plan':
        return this.isPlanComplete(soapAssessment.plan);
      default:
        return false;
    }
  }

  /**
   * Generate structured follow-up questions using OLDCARTS
   */
  generateOLDCARTSQuestions(symptom: string, currentHPI: HistoryOfPresentIllness): string[] {
    const questions = [];

    if (!currentHPI.completionStatus.onset) {
      questions.push(`When did your ${symptom} first start? Was it sudden or gradual?`);
    }

    if (!currentHPI.completionStatus.location) {
      questions.push(`Can you point to exactly where you feel the ${symptom}?`);
    }

    if (!currentHPI.completionStatus.duration) {
      questions.push(`How long does the ${symptom} last when it occurs?`);
    }

    if (!currentHPI.completionStatus.character) {
      questions.push(`How would you describe the ${symptom}? (sharp, dull, burning, cramping, etc.)`);
    }

    if (!currentHPI.completionStatus.alleviating) {
      questions.push(`What makes the ${symptom} better? Have you tried anything that helps?`);
    }

    if (!currentHPI.completionStatus.radiating) {
      questions.push(`Does the ${symptom} spread or move to other areas of your body?`);
    }

    if (!currentHPI.completionStatus.timing) {
      questions.push(`When does the ${symptom} typically occur? Is there a pattern?`);
    }

    if (!currentHPI.completionStatus.severity) {
      questions.push(`On a scale of 1-10, with 10 being the worst pain imaginable, how would you rate your ${symptom}?`);
    }

    return questions;
  }

  // Private helper methods
  private initializeSubjectiveData(chiefComplaint: string): SubjectiveData {
    return {
      chiefComplaint,
      historyOfPresentIllness: {
        onset: '',
        location: '',
        duration: '',
        character: '',
        alleviatingFactors: [],
        radiatingFactors: [],
        timing: '',
        severity: 0,
        associatedSymptoms: [],
        completionStatus: {
          onset: false,
          location: false,
          duration: false,
          character: false,
          alleviating: false,
          radiating: false,
          timing: false,
          severity: false,
          associated: false
        }
      },
      reviewOfSystems: {
        constitutional: [],
        cardiovascular: [],
        respiratory: [],
        gastrointestinal: [],
        genitourinary: [],
        musculoskeletal: [],
        neurological: [],
        psychiatric: [],
        endocrine: [],
        hematologic: [],
        completedSystems: []
      },
      pastMedicalHistory: [],
      medications: [],
      allergies: [],
      socialHistory: {
        smoking: '',
        alcohol: '',
        drugs: '',
        occupation: '',
        livingArrangement: '',
        recentTravel: [],
        sexualHistory: '',
        exerciseHabits: ''
      },
      familyHistory: [],
      completionStatus: {
        chiefComplaint: !!chiefComplaint,
        hpi: false,
        ros: false,
        pmh: false,
        medications: false,
        allergies: false,
        social: false,
        family: false
      }
    };
  }

  private initializeObjectiveData(): ObjectiveData {
    return {
      vitalSigns: {
        temperature: '',
        bloodPressure: '',
        heartRate: '',
        respiratoryRate: '',
        oxygenSaturation: '',
        weight: '',
        height: '',
        bmi: '',
        reportedBy: 'patient'
      },
      physicalExam: {
        general: '',
        heent: '',
        cardiovascular: '',
        respiratory: '',
        abdominal: '',
        musculoskeletal: '',
        neurological: '',
        skin: '',
        limitations: ['Remote consultation - physical examination limited']
      },
      diagnosticTests: [],
      observations: [],
      limitations: ['Remote consultation limitations apply']
    };
  }

  private initializeAssessmentData(): AssessmentData {
    return {
      differentialDiagnosis: [],
      workingDiagnosis: '',
      riskStratification: {
        overallRisk: 'low',
        immediateRisks: [],
        longTermRisks: [],
        riskFactors: [],
        protectiveFactors: []
      },
      redFlags: [],
      clinicalImpression: '',
      confidence: 0
    };
  }

  private initializePlanData(): PlanData {
    return {
      diagnosticPlan: {
        recommendedTests: [],
        referrals: [],
        monitoring: []
      },
      treatmentPlan: {
        medications: [],
        nonPharmacological: [],
        lifestyle: [],
        contraindications: []
      },
      followUpPlan: {
        nextAppointment: '',
        monitoringSchedule: [],
        warningSignsToWatch: [],
        whenToSeekCare: []
      },
      patientEducation: {
        keyPoints: [],
        resources: [],
        demonstrations: [],
        comprehensionCheck: []
      },
      safetyNetting: {
        redFlagSymptoms: [],
        emergencyContacts: [],
        backupPlans: [],
        patientUnderstanding: false
      }
    };
  }

  private generateInitialNextSteps(chiefComplaint: string): string[] {
    return [
      'Gather detailed history of present illness using OLDCARTS',
      'Conduct systematic review of systems',
      'Obtain relevant past medical history',
      'Assess current medications and allergies'
    ];
  }

  private generateSubjectiveQuestions(subjective: SubjectiveData, lastResponse: string): string[] {
    const questions = [];

    // Focus on HPI first
    if (!subjective.completionStatus.hpi) {
      questions.push(...this.generateOLDCARTSQuestions('symptoms', subjective.historyOfPresentIllness));
    }

    // Then move to other subjective areas
    if (!subjective.completionStatus.pmh) {
      questions.push('Do you have any ongoing medical conditions or past illnesses I should know about?');
    }

    if (!subjective.completionStatus.medications) {
      questions.push('What medications are you currently taking, including over-the-counter drugs and supplements?');
    }

    if (!subjective.completionStatus.allergies) {
      questions.push('Do you have any known allergies to medications, foods, or other substances?');
    }

    return questions;
  }

  private generateObjectiveQuestions(objective: ObjectiveData): string[] {
    return [
      'Can you check your temperature if you have a thermometer available?',
      'Do you know your current blood pressure or have a way to check it?',
      'How are you feeling overall right now? Any visible changes you\'ve noticed?'
    ];
  }

  private generateAssessmentQuestions(soapAssessment: SOAPAssessment): string[] {
    return [
      'Based on what you\'ve told me, I\'m considering a few possibilities. Does this sound consistent with your experience?',
      'Are there any other symptoms or concerns you haven\'t mentioned yet?'
    ];
  }

  private generatePlanQuestions(soapAssessment: SOAPAssessment): string[] {
    return [
      'Do you have any questions about the recommendations I\'ve discussed?',
      'Is there anything that might prevent you from following this plan?',
      'Do you understand when you should seek immediate medical attention?'
    ];
  }

  private extractSeverityScore(response: string): number {
    const match = response.match(/(\d+)/);
    return match ? Math.min(Math.max(parseInt(match[1]), 1), 10) : 0;
  }

  private calculateCompletionPercentage(soapAssessment: SOAPAssessment): number {
    // Calculate based on completed sections
    let completed = 0;
    let total = 0;

    // Subjective completion
    const subjectiveStatus = soapAssessment.subjective.completionStatus;
    const subjectiveCompleted = Object.values(subjectiveStatus).filter(Boolean).length;
    const subjectiveTotal = Object.keys(subjectiveStatus).length;
    
    completed += subjectiveCompleted;
    total += subjectiveTotal;

    // Add other phases as they're implemented
    
    return Math.round((completed / total) * 100);
  }

  private generateNextSteps(soapAssessment: SOAPAssessment): string[] {
    const steps = [];

    switch (soapAssessment.currentPhase) {
      case 'subjective':
        if (!this.isSubjectiveComplete(soapAssessment.subjective)) {
          steps.push('Complete history taking');
        } else {
          steps.push('Move to objective assessment');
        }
        break;
      case 'objective':
        steps.push('Gather available objective data');
        break;
      case 'assessment':
        steps.push('Formulate differential diagnosis');
        break;
      case 'plan':
        steps.push('Develop comprehensive care plan');
        break;
    }

    return steps;
  }

  private identifyCriticalFlags(soapAssessment: SOAPAssessment): string[] {
    const flags = [];

    // Check for emergency symptoms
    const chiefComplaint = soapAssessment.subjective.chiefComplaint.toLowerCase();
    if (chiefComplaint.includes('chest pain') || chiefComplaint.includes('difficulty breathing')) {
      flags.push('POTENTIAL_EMERGENCY');
    }

    // Check severity scores
    if (soapAssessment.subjective.historyOfPresentIllness.severity >= 8) {
      flags.push('HIGH_SEVERITY_PAIN');
    }

    return flags;
  }

  private isSubjectiveComplete(subjective: SubjectiveData): boolean {
    const status = subjective.completionStatus;
    return status.chiefComplaint && status.hpi && status.medications && status.allergies;
  }

  private isObjectiveComplete(objective: ObjectiveData): boolean {
    return objective.observations.length > 0 || objective.vitalSigns.temperature !== '';
  }

  private isAssessmentComplete(assessment: AssessmentData): boolean {
    return assessment.workingDiagnosis !== '' && assessment.differentialDiagnosis.length > 0;
  }

  private isPlanComplete(plan: PlanData): boolean {
    return plan.followUpPlan.nextAppointment !== '' && plan.safetyNetting.patientUnderstanding;
  }
}

// Export class and singleton instance
export { DiagnosticFrameworkService };
export const diagnosticFrameworkService = new DiagnosticFrameworkService();
