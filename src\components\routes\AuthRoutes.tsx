/**
 * AUTHENTICATION ROUTES MODULE
 * 
 * Authentication and authorization routes including login, registration,
 * password recovery, and role-based dashboard routing.
 */

import React from 'react';
import { createLazyComponent, createRoleBasedLazyComponent } from '../../utils/lazyLoading';
import type { UserRole } from '../../types/auth';

// =============================================================================
// AUTHENTICATION ROUTES
// =============================================================================

export const LoginPage = createLazyComponent(
  () => import('../auth/LoginPage').catch(() => ({
    default: () => (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold text-gray-800 text-center mb-6">Sign In</h2>
          <p className="text-gray-600 text-center mb-4">Login system is loading...</p>
          <div className="space-y-4">
            <div className="p-3 bg-blue-50 rounded border border-blue-200">
              <h3 className="text-blue-800 font-semibold text-sm">Demo Access</h3>
              <p className="text-blue-700 text-xs mt-1">Use demo credentials while system loads</p>
            </div>
            <div className="p-3 bg-green-50 rounded border border-green-200">
              <h3 className="text-green-800 font-semibold text-sm">Secure Login</h3>
              <p className="text-green-700 text-xs mt-1">Your data is protected with enterprise-grade security</p>
            </div>
          </div>
        </div>
      </div>
    )
  })),
  { 
    priority: 'high'
  }
);

export const RegisterPage = createLazyComponent(
  () => import('../auth/RegisterPage').catch(() => ({
    default: () => (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold text-gray-800 text-center mb-6">Create Account</h2>
          <p className="text-gray-600 text-center mb-4">Registration system is loading...</p>
          <div className="space-y-4">
            <div className="p-3 bg-purple-50 rounded border border-purple-200">
              <h3 className="text-purple-800 font-semibold text-sm">Account Types</h3>
              <ul className="text-purple-700 text-xs mt-1 space-y-1">
                <li>• Patient Account</li>
                <li>• Healthcare Provider</li>
                <li>• Emergency Responder</li>
              </ul>
            </div>
            <div className="p-3 bg-green-50 rounded border border-green-200">
              <h3 className="text-green-800 font-semibold text-sm">HIPAA Compliant</h3>
              <p className="text-green-700 text-xs mt-1">Your health information is protected</p>
            </div>
          </div>
        </div>
      </div>
    )
  })),
  { 
    priority: 'normal'
  }
);

export const ForgotPassword = createLazyComponent(
  () => import('../auth/ForgotPassword').catch(() => ({
    default: () => (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold text-gray-800 text-center mb-6">Reset Password</h2>
          <p className="text-gray-600 text-center mb-4">Password reset system is loading...</p>
          <div className="space-y-4">
            <div className="p-3 bg-yellow-50 rounded border border-yellow-200">
              <h3 className="text-yellow-800 font-semibold text-sm">Secure Reset</h3>
              <p className="text-yellow-700 text-xs mt-1">We'll send a secure reset link to your email</p>
            </div>
            <div className="p-3 bg-blue-50 rounded border border-blue-200">
              <h3 className="text-blue-800 font-semibold text-sm">Need Help?</h3>
              <p className="text-blue-700 text-xs mt-1">Contact support if you need assistance</p>
            </div>
          </div>
        </div>
      </div>
    )
  })),
  { 
    priority: 'normal'
  }
);

// =============================================================================
// ROLE-BASED DASHBOARD ROUTING
// =============================================================================

export const RoleBasedDashboard = createRoleBasedLazyComponent(
  {
    'patient': () => import('../dashboard/PatientDashboard').catch(() => ({
      default: () => (
        <div className="p-8 bg-blue-50 border border-blue-200 rounded">
          <h2 className="text-blue-800 font-bold">👤 Patient Dashboard</h2>
          <p className="text-blue-700 mt-2">Loading your personalized health dashboard...</p>
        </div>
      )
    })),
    'healthcare_provider': () => import('../dashboard/ProviderDashboard').catch(() => ({
      default: () => (
        <div className="p-8 bg-green-50 border border-green-200 rounded">
          <h2 className="text-green-800 font-bold">🩺 Provider Dashboard</h2>
          <p className="text-green-700 mt-2">Loading your clinical dashboard...</p>
        </div>
      )
    })),
    'admin': () => import('../dashboard/AdminDashboard').catch(() => ({
      default: () => (
        <div className="p-8 bg-purple-50 border border-purple-200 rounded">
          <h2 className="text-purple-800 font-bold">⚙️ Admin Dashboard</h2>
          <p className="text-purple-700 mt-2">Loading system administration dashboard...</p>
        </div>
      )
    })),
    'emergency_responder': () => import('../dashboard/EmergencyDashboard').catch(() => ({
      default: () => (
        <div className="p-8 bg-red-50 border border-red-200 rounded">
          <h2 className="text-red-800 font-bold">🚨 Emergency Dashboard</h2>
          <p className="text-red-700 mt-2">Loading emergency response dashboard...</p>
        </div>
      )
    }))
  },
  () => import('../dashboard/PatientDashboard').catch(() => ({
    default: () => (
      <div className="p-8 bg-gray-50 border border-gray-200 rounded">
        <h2 className="text-gray-800 font-bold">📊 Default Dashboard</h2>
        <p className="text-gray-700 mt-2">Loading default dashboard...</p>
      </div>
    )
  })),
  {
    priority: 'high',
    errorFallback: ({ error, resetErrorBoundary }) => (
      <div className="p-8 bg-yellow-100 border border-yellow-300 rounded">
        <h2 className="text-yellow-800 font-bold">Dashboard Loading Error</h2>
        <p className="text-yellow-700 mt-2">Failed to load role-specific dashboard.</p>
        <p className="text-yellow-600 text-sm mt-1">Error: {error.message}</p>
        <button
          onClick={resetErrorBoundary}
          className="mt-4 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
        >
          Retry Dashboard
        </button>
      </div>
    )
  }
);

// =============================================================================
// ONBOARDING ROUTES
// =============================================================================

export const WelcomeLanguageSelection = createLazyComponent(
  () => import('../../pages/welcome-language-selection').catch(() => ({
    default: () => (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-lg w-full bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-800 text-center mb-6">Welcome to VoiceHealth AI</h2>
          <p className="text-gray-600 text-center mb-4">Language selection is loading...</p>
          <div className="grid grid-cols-2 gap-3">
            <div className="p-3 bg-blue-50 rounded border border-blue-200 text-center">
              <span className="text-2xl">🇺🇸</span>
              <p className="text-blue-800 text-sm mt-1">English</p>
            </div>
            <div className="p-3 bg-green-50 rounded border border-green-200 text-center">
              <span className="text-2xl">🇫🇷</span>
              <p className="text-green-800 text-sm mt-1">Français</p>
            </div>
            <div className="p-3 bg-orange-50 rounded border border-orange-200 text-center">
              <span className="text-2xl">🇪🇸</span>
              <p className="text-orange-800 text-sm mt-1">Español</p>
            </div>
            <div className="p-3 bg-purple-50 rounded border border-purple-200 text-center">
              <span className="text-2xl">🌍</span>
              <p className="text-purple-800 text-sm mt-1">More</p>
            </div>
          </div>
        </div>
      </div>
    )
  })),
  { 
    priority: 'high'
  }
);

export const CountryRegionalSelection = createLazyComponent(
  () => import('../../pages/country-regional-selection').catch(() => ({
    default: () => (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-teal-100">
        <div className="max-w-lg w-full bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-800 text-center mb-6">Select Your Region</h2>
          <p className="text-gray-600 text-center mb-4">Regional selection is loading...</p>
          <div className="space-y-3">
            <div className="p-3 bg-green-50 rounded border border-green-200">
              <h3 className="text-green-800 font-semibold">🌍 Africa</h3>
              <p className="text-green-700 text-sm">Nigeria, Kenya, South Africa, Ghana</p>
            </div>
            <div className="p-3 bg-blue-50 rounded border border-blue-200">
              <h3 className="text-blue-800 font-semibold">🌎 Americas</h3>
              <p className="text-blue-700 text-sm">United States, Canada, Mexico</p>
            </div>
            <div className="p-3 bg-purple-50 rounded border border-purple-200">
              <h3 className="text-purple-800 font-semibold">🌏 Asia Pacific</h3>
              <p className="text-purple-700 text-sm">India, Australia, Singapore</p>
            </div>
          </div>
        </div>
      </div>
    )
  })),
  { 
    priority: 'normal'
  }
);

// =============================================================================
// AUTHENTICATION UTILITIES
// =============================================================================

/**
 * Preload authentication routes
 */
export function preloadAuthRoutes(): void {
  console.log('🔐 Preloading authentication routes...');
  
  const authComponents = [
    LoginPage,
    RegisterPage,
    WelcomeLanguageSelection
  ];

  authComponents.forEach((Component, index) => {
    try {
      React.createElement(Component);
      console.log(`✅ Auth component ${index + 1}/${authComponents.length} preloaded`);
    } catch (error) {
      console.warn(`⚠️ Failed to preload auth component ${index + 1}:`, error);
    }
  });
}

/**
 * Get dashboard component for user role
 */
export function getDashboardForRole(userRole: UserRole): React.ComponentType {
  const dashboardMap: Record<UserRole, React.ComponentType> = {
    'patient': () => React.createElement(RoleBasedDashboard, { userRole: 'patient' }),
    'healthcare_provider': () => React.createElement(RoleBasedDashboard, { userRole: 'healthcare_provider' }),
    'admin': () => React.createElement(RoleBasedDashboard, { userRole: 'admin' }),
    'emergency_responder': () => React.createElement(RoleBasedDashboard, { userRole: 'emergency_responder' })
  };

  return dashboardMap[userRole] || dashboardMap['patient'];
}

/**
 * Check if route requires authentication
 */
export function requiresAuthentication(routeName: string): boolean {
  const publicRoutes = ['LoginPage', 'RegisterPage', 'ForgotPassword', 'WelcomeLanguageSelection'];
  return !publicRoutes.includes(routeName);
}
