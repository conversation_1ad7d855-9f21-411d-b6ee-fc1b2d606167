{"country": {"code": "NG", "name": "Nigeria", "region": "West Africa", "currency": "NGN", "languages": ["en", "ha", "yo", "ig", "ff"], "timezone": "WAT"}, "healthcare": {"system": "National Health Insurance Scheme (NHIS)", "emergencyNumber": "199", "commonConditions": ["Malaria", "Hypertension", "Diabetes", "HIV/AIDS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sickle cell disease", "Respiratory infections", "Diarrheal diseases"], "endemicDiseases": ["Yellow fever", "Lassa fever", "Meningitis", "Cholera", "Hepatitis B", "Onchocercia<PERSON>", "Lymphatic filariasis"], "seasonalPatterns": {"rainySeasonDiseases": ["Malaria", "Cholera", "Lassa fever"], "drySeasonDiseases": ["Meningitis", "Respiratory infections"], "yearRound": ["Hypertension", "Diabetes", "Sickle cell disease"]}}, "cultural": {"healthBeliefs": {"traditionalMedicine": "very_widely_accepted", "spiritualHealing": "very_common", "familyInvolvement": "very_high", "genderConsiderations": "very_high"}, "communicationStyle": {"directness": "low", "respectForElders": "very_high", "hierarchical": "very_high", "groupDecisionMaking": "very_common"}, "dietaryConsiderations": ["Rice and yam staples", "Spicy food preferences", "Palm oil usage", "Protein from fish and poultry", "Regional dietary variations"]}, "regulations": {"dataProtection": {"law": "Nigeria Data Protection Regulation (NDPR) 2019", "requirements": ["lawful_basis", "consent", "data_minimization", "security"], "crossBorderTransfer": "adequacy_or_safeguards_required"}, "medicalDevice": {"authority": "National Agency for Food and Drug Administration and Control (NAFDAC)", "classification": "risk_based", "requirements": ["registration", "quality_management", "local_representation"]}, "telemedicine": {"status": "developing_framework", "requirements": ["medical_license", "patient_consent", "data_protection"], "restrictions": ["controlled_substances", "emergency_procedures"]}}, "infrastructure": {"internetPenetration": 0.51, "mobileSubscription": 0.84, "electricityAccess": 0.62, "urbanization": 0.52, "literacyRate": 0.62}, "localization": {"dateFormat": "dd/mm/yyyy", "timeFormat": "12h", "numberFormat": "1,234.56", "measurementSystem": "metric", "preferredLanguages": ["en", "ha", "yo", "ig"]}, "emergency": {"protocols": {"responseTime": "< 25 minutes urban, < 60 minutes rural", "escalationLevels": ["primary_health_center", "general_hospital", "specialist_hospital", "teaching_hospital"], "culturalConsiderations": ["extended_family_involvement", "religious_leader_consultation", "traditional_ruler_notification"]}, "contacts": {"ambulance": "199", "police": "199", "fire": "199", "emergencyManagement": "112"}}}