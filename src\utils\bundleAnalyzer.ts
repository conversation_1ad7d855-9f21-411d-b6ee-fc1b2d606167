/**
 * BUNDLE ANALYZER FOR MEDICAL APPLICATION
 * 
 * This utility monitors bundle sizes, loading performance, and provides
 * insights for optimizing the medical application's performance while
 * maintaining emergency system reliability.
 * 
 * MONITORING FEATURES:
 * - Real-time bundle size tracking
 * - Component loading performance
 * - Emergency route availability
 * - Cache effectiveness metrics
 * - User experience impact analysis
 * 
 * PERFORMANCE TARGETS:
 * - 40% bundle size reduction from baseline
 * - Emergency routes load < 100ms
 * - Medical components load < 500ms
 * - 95% cache hit rate for static assets
 */

interface BundleMetrics {
  readonly totalSize: number;
  readonly gzippedSize: number;
  readonly chunkCount: number;
  readonly loadTime: number;
  readonly cacheHitRate: number;
  readonly emergencyRouteLoadTime: number;
}

interface ComponentLoadMetrics {
  readonly componentName: string;
  readonly loadTime: number;
  readonly bundleSize: number;
  readonly cacheHit: boolean;
  readonly priority: 'low' | 'normal' | 'high' | 'emergency';
  readonly userRole?: string;
}

interface PerformanceTarget {
  readonly metric: string;
  readonly target: number;
  readonly current: number;
  readonly status: 'excellent' | 'good' | 'warning' | 'critical';
  readonly impact: 'low' | 'medium' | 'high' | 'critical';
}

class MedicalBundleAnalyzer {
  private metrics: BundleMetrics;
  private componentMetrics: Map<string, ComponentLoadMetrics>;
  private performanceTargets: PerformanceTarget[];
  private baselineSize: number;
  private startTime: number;

  constructor() {
    this.metrics = {
      totalSize: 0,
      gzippedSize: 0,
      chunkCount: 0,
      loadTime: 0,
      cacheHitRate: 0,
      emergencyRouteLoadTime: 0
    };
    
    this.componentMetrics = new Map();
    this.performanceTargets = [];
    this.baselineSize = 0;
    this.startTime = performance.now();
    
    this.initializeMonitoring();
  }

  /**
   * Initialize performance monitoring
   */
  private initializeMonitoring(): void {
    // Monitor resource loading
    if ('PerformanceObserver' in window) {
      this.setupResourceObserver();
      this.setupNavigationObserver();
    }

    // Monitor bundle loading
    this.monitorBundleLoading();
    
    // Set up performance targets
    this.setupPerformanceTargets();
    
    // Start periodic analysis
    this.startPeriodicAnalysis();
  }

  /**
   * Set up resource performance observer
   */
  private setupResourceObserver(): void {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          this.analyzeResourceEntry(entry as PerformanceResourceTiming);
        }
      }
    });

    observer.observe({ entryTypes: ['resource'] });
  }

  /**
   * Set up navigation performance observer
   */
  private setupNavigationObserver(): void {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          this.analyzeNavigationEntry(entry as PerformanceNavigationTiming);
        }
      }
    });

    observer.observe({ entryTypes: ['navigation'] });
  }

  /**
   * Analyze resource loading performance
   */
  private analyzeResourceEntry(entry: PerformanceResourceTiming): void {
    const url = new URL(entry.name);
    const pathname = url.pathname;
    
    // Track JavaScript bundles
    if (pathname.endsWith('.js')) {
      this.trackBundleLoad(pathname, entry);
    }
    
    // Track emergency route resources
    if (this.isEmergencyResource(pathname)) {
      this.trackEmergencyResourceLoad(pathname, entry);
    }
    
    // Update cache hit rate
    this.updateCacheHitRate(entry);
  }

  /**
   * Analyze navigation performance
   */
  private analyzeNavigationEntry(entry: PerformanceNavigationTiming): void {
    this.metrics = {
      ...this.metrics,
      loadTime: entry.loadEventEnd - entry.fetchStart
    };
  }

  /**
   * Track bundle loading performance
   */
  private trackBundleLoad(pathname: string, entry: PerformanceResourceTiming): void {
    const loadTime = entry.responseEnd - entry.requestStart;
    const transferSize = entry.transferSize || 0;
    const cacheHit = entry.transferSize === 0 && entry.decodedBodySize > 0;
    
    // Determine component priority
    const priority = this.determinePriority(pathname);
    
    // Extract component name from bundle path
    const componentName = this.extractComponentName(pathname);
    
    const metrics: ComponentLoadMetrics = {
      componentName,
      loadTime,
      bundleSize: transferSize,
      cacheHit,
      priority
    };
    
    this.componentMetrics.set(componentName, metrics);
    
    // Update total metrics
    this.metrics = {
      ...this.metrics,
      totalSize: this.metrics.totalSize + transferSize,
      chunkCount: this.metrics.chunkCount + 1
    };
  }

  /**
   * Track emergency resource loading
   */
  private trackEmergencyResourceLoad(pathname: string, entry: PerformanceResourceTiming): void {
    const loadTime = entry.responseEnd - entry.requestStart;
    
    // Update emergency route load time (keep the maximum)
    this.metrics = {
      ...this.metrics,
      emergencyRouteLoadTime: Math.max(this.metrics.emergencyRouteLoadTime, loadTime)
    };
  }

  /**
   * Update cache hit rate
   */
  private updateCacheHitRate(entry: PerformanceResourceTiming): void {
    const isCacheHit = entry.transferSize === 0 && entry.decodedBodySize > 0;
    const totalRequests = this.componentMetrics.size + 1;
    const cacheHits = Array.from(this.componentMetrics.values()).filter(m => m.cacheHit).length + (isCacheHit ? 1 : 0);
    
    this.metrics = {
      ...this.metrics,
      cacheHitRate: (cacheHits / totalRequests) * 100
    };
  }

  /**
   * Determine if resource is emergency-related
   */
  private isEmergencyResource(pathname: string): boolean {
    const emergencyPatterns = [
      '/emergency',
      '/critical',
      'emergency-protocols',
      'critical-vitals',
      'emergency-consultation'
    ];
    
    return emergencyPatterns.some(pattern => pathname.includes(pattern));
  }

  /**
   * Determine component priority from pathname
   */
  private determinePriority(pathname: string): 'low' | 'normal' | 'high' | 'emergency' {
    if (this.isEmergencyResource(pathname)) {
      return 'emergency';
    }
    
    if (pathname.includes('dashboard') || pathname.includes('auth')) {
      return 'high';
    }
    
    if (pathname.includes('medical') || pathname.includes('consultation')) {
      return 'normal';
    }
    
    return 'low';
  }

  /**
   * Extract component name from bundle path
   */
  private extractComponentName(pathname: string): string {
    const filename = pathname.split('/').pop() || 'unknown';
    return filename.replace(/\.[^.]+$/, '').replace(/-[a-f0-9]+$/, '');
  }

  /**
   * Monitor bundle loading with dynamic imports
   */
  private monitorBundleLoading(): void {
    // Note: Dynamic import monitoring would be implemented at the bundler level
    // This is a placeholder for conceptual tracking
    console.log('📦 Bundle loading monitoring initialized');

    // Track module loading through performance observer
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name.includes('.js') || entry.name.includes('.ts')) {
            const loadTime = entry.duration;
            this.trackDynamicImport(entry.name, loadTime, true);
          }
        }
      });

      observer.observe({ entryTypes: ['resource'] });
    }
  }

  /**
   * Track dynamic import performance
   */
  private trackDynamicImport(specifier: string, loadTime: number, success: boolean): void {
    const componentName = this.extractComponentName(specifier);
    const priority = this.determinePriority(specifier);
    
    const metrics: ComponentLoadMetrics = {
      componentName,
      loadTime,
      bundleSize: 0, // Size not available for dynamic imports
      cacheHit: false, // Assume cache miss for dynamic imports
      priority
    };
    
    this.componentMetrics.set(componentName, metrics);
  }

  /**
   * Set up performance targets
   */
  private setupPerformanceTargets(): void {
    this.performanceTargets = [
      {
        metric: 'Bundle Size Reduction',
        target: 40, // 40% reduction
        current: 0,
        status: 'warning',
        impact: 'high'
      },
      {
        metric: 'Emergency Route Load Time',
        target: 100, // 100ms
        current: 0,
        status: 'warning',
        impact: 'critical'
      },
      {
        metric: 'Medical Component Load Time',
        target: 500, // 500ms
        current: 0,
        status: 'warning',
        impact: 'high'
      },
      {
        metric: 'Cache Hit Rate',
        target: 95, // 95%
        current: 0,
        status: 'warning',
        impact: 'medium'
      }
    ];
  }

  /**
   * Start periodic performance analysis
   */
  private startPeriodicAnalysis(): void {
    setInterval(() => {
      this.updatePerformanceTargets();
      this.logPerformanceReport();
    }, 30000); // Every 30 seconds
  }

  /**
   * Update performance targets with current metrics
   */
  private updatePerformanceTargets(): void {
    this.performanceTargets = this.performanceTargets.map(target => {
      let current = 0;
      let status: 'excellent' | 'good' | 'warning' | 'critical' = 'warning';
      
      switch (target.metric) {
        case 'Bundle Size Reduction':
          if (this.baselineSize > 0) {
            current = ((this.baselineSize - this.metrics.totalSize) / this.baselineSize) * 100;
          }
          status = current >= target.target ? 'excellent' : 
                   current >= target.target * 0.8 ? 'good' :
                   current >= target.target * 0.5 ? 'warning' : 'critical';
          break;
          
        case 'Emergency Route Load Time':
          current = this.metrics.emergencyRouteLoadTime;
          status = current <= target.target ? 'excellent' :
                   current <= target.target * 1.5 ? 'good' :
                   current <= target.target * 2 ? 'warning' : 'critical';
          break;
          
        case 'Medical Component Load Time':
          const medicalComponents = Array.from(this.componentMetrics.values())
            .filter(m => m.priority === 'normal' || m.priority === 'high');
          current = medicalComponents.length > 0 ? 
            medicalComponents.reduce((sum, m) => sum + m.loadTime, 0) / medicalComponents.length : 0;
          status = current <= target.target ? 'excellent' :
                   current <= target.target * 1.2 ? 'good' :
                   current <= target.target * 1.5 ? 'warning' : 'critical';
          break;
          
        case 'Cache Hit Rate':
          current = this.metrics.cacheHitRate;
          status = current >= target.target ? 'excellent' :
                   current >= target.target * 0.9 ? 'good' :
                   current >= target.target * 0.8 ? 'warning' : 'critical';
          break;
      }
      
      return { ...target, current, status };
    });
  }

  /**
   * Log performance report
   */
  private logPerformanceReport(): void {
    if (process.env.NODE_ENV === 'development') {
      console.group('🏥 Medical App Performance Report');
      console.log('📊 Bundle Metrics:', this.metrics);
      console.log('🎯 Performance Targets:', this.performanceTargets);
      console.log('📦 Component Metrics:', Array.from(this.componentMetrics.entries()));
      console.groupEnd();
    }
  }

  /**
   * Get current performance summary
   */
  getPerformanceSummary(): {
    metrics: BundleMetrics;
    targets: PerformanceTarget[];
    componentCount: number;
    criticalIssues: string[];
  } {
    const criticalIssues = this.performanceTargets
      .filter(target => target.status === 'critical')
      .map(target => `${target.metric}: ${target.current} (target: ${target.target})`);
    
    return {
      metrics: this.metrics,
      targets: this.performanceTargets,
      componentCount: this.componentMetrics.size,
      criticalIssues
    };
  }

  /**
   * Set baseline size for comparison
   */
  setBaselineSize(size: number): void {
    this.baselineSize = size;
  }

  /**
   * Get component loading statistics
   */
  getComponentStats(): {
    byPriority: Record<string, number>;
    averageLoadTime: number;
    slowestComponents: ComponentLoadMetrics[];
  } {
    const components = Array.from(this.componentMetrics.values());
    
    const byPriority = components.reduce((acc, component) => {
      acc[component.priority] = (acc[component.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const averageLoadTime = components.length > 0 ?
      components.reduce((sum, c) => sum + c.loadTime, 0) / components.length : 0;
    
    const slowestComponents = components
      .sort((a, b) => b.loadTime - a.loadTime)
      .slice(0, 5);
    
    return {
      byPriority,
      averageLoadTime,
      slowestComponents
    };
  }
}

// Global bundle analyzer instance
export const bundleAnalyzer = new MedicalBundleAnalyzer();

// Export for use in development tools
if (process.env.NODE_ENV === 'development') {
  (window as any).bundleAnalyzer = bundleAnalyzer;
}

export default bundleAnalyzer;
