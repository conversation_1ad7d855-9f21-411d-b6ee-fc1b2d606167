/**
 * Emergency Protocols Component
 * Displays and manages emergency medical protocols for healthcare providers
 */

import React, { useState, useEffect } from 'react';

interface EmergencyProtocol {
  id: string;
  name: string;
  category: 'cardiac' | 'respiratory' | 'trauma' | 'neurological' | 'pediatric';
  severity: 'critical' | 'urgent' | 'standard';
  steps: string[];
  timeLimit: number; // in minutes
  requiredEquipment: string[];
  contraindications: string[];
}

interface EmergencyProtocolsProps {
  patientAge?: number;
  patientCondition?: string;
  onProtocolSelect?: (protocol: EmergencyProtocol) => void;
  emergencyType?: string;
}

const EmergencyProtocols: React.FC<EmergencyProtocolsProps> = ({
  patientAge,
  patientCondition,
  onProtocolSelect,
  emergencyType
}) => {
  const [protocols, setProtocols] = useState<EmergencyProtocol[]>([]);
  const [selectedProtocol, setSelectedProtocol] = useState<EmergencyProtocol | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock emergency protocols data
  const mockProtocols: EmergencyProtocol[] = [
    {
      id: 'cardiac-arrest-adult',
      name: 'Adult Cardiac Arrest',
      category: 'cardiac',
      severity: 'critical',
      steps: [
        'Check responsiveness and breathing',
        'Call for help and AED',
        'Begin chest compressions 30:2',
        'Apply AED when available',
        'Continue CPR until emergency services arrive'
      ],
      timeLimit: 4,
      requiredEquipment: ['AED', 'Bag-mask ventilation'],
      contraindications: ['DNR order', 'Signs of death']
    },
    {
      id: 'respiratory-distress',
      name: 'Severe Respiratory Distress',
      category: 'respiratory',
      severity: 'urgent',
      steps: [
        'Position patient upright',
        'Administer high-flow oxygen',
        'Assess for airway obstruction',
        'Prepare for intubation if needed',
        'Monitor vital signs continuously'
      ],
      timeLimit: 10,
      requiredEquipment: ['Oxygen', 'Pulse oximeter', 'Intubation kit'],
      contraindications: ['Pneumothorax without decompression']
    },
    {
      id: 'anaphylaxis',
      name: 'Anaphylaxis Management',
      category: 'respiratory',
      severity: 'critical',
      steps: [
        'Remove or stop suspected allergen',
        'Administer epinephrine IM',
        'Give high-flow oxygen',
        'Establish IV access',
        'Administer corticosteroids and antihistamines'
      ],
      timeLimit: 5,
      requiredEquipment: ['Epinephrine auto-injector', 'IV fluids', 'Oxygen'],
      contraindications: ['None in true anaphylaxis']
    }
  ];

  useEffect(() => {
    // Simulate loading protocols
    setTimeout(() => {
      setProtocols(mockProtocols);
      setLoading(false);
    }, 500);
  }, []);

  const handleProtocolSelect = (protocol: EmergencyProtocol) => {
    setSelectedProtocol(protocol);
    onProtocolSelect?.(protocol);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'urgent': return 'text-orange-600 bg-orange-100';
      case 'standard': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        <span className="ml-2 text-gray-600">Loading emergency protocols...</span>
      </div>
    );
  }

  return (
    <div className="emergency-protocols p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-red-600 mb-2">Emergency Protocols</h2>
        <p className="text-gray-600">
          Select appropriate emergency protocol based on patient condition
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {protocols.map((protocol) => (
          <div
            key={protocol.id}
            className={`protocol-card p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
              selectedProtocol?.id === protocol.id 
                ? 'border-red-500 bg-red-50' 
                : 'border-gray-200 hover:border-red-300'
            }`}
            onClick={() => handleProtocolSelect(protocol)}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-gray-800">{protocol.name}</h3>
              <span className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(protocol.severity)}`}>
                {protocol.severity.toUpperCase()}
              </span>
            </div>
            
            <p className="text-sm text-gray-600 mb-2">
              Category: {protocol.category}
            </p>
            
            <p className="text-sm text-gray-600 mb-3">
              Time limit: {protocol.timeLimit} minutes
            </p>
            
            <div className="text-xs text-gray-500">
              {protocol.steps.length} steps • {protocol.requiredEquipment.length} equipment items
            </div>
          </div>
        ))}
      </div>

      {selectedProtocol && (
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="font-bold text-red-800 mb-3">{selectedProtocol.name} - Protocol Steps</h3>
          
          <div className="mb-4">
            <h4 className="font-semibold text-gray-700 mb-2">Steps:</h4>
            <ol className="list-decimal list-inside space-y-1">
              {selectedProtocol.steps.map((step, index) => (
                <li key={index} className="text-sm text-gray-700">{step}</li>
              ))}
            </ol>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-gray-700 mb-2">Required Equipment:</h4>
              <ul className="list-disc list-inside space-y-1">
                {selectedProtocol.requiredEquipment.map((item, index) => (
                  <li key={index} className="text-sm text-gray-600">{item}</li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="font-semibold text-gray-700 mb-2">Contraindications:</h4>
              <ul className="list-disc list-inside space-y-1">
                {selectedProtocol.contraindications.map((item, index) => (
                  <li key={index} className="text-sm text-gray-600">{item}</li>
                ))}
              </ul>
            </div>
          </div>

          <div className="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded">
            <p className="text-sm text-yellow-800">
              <strong>⚠️ Critical Time Limit:</strong> This protocol must be initiated within {selectedProtocol.timeLimit} minutes
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmergencyProtocols;
