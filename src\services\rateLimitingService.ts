/**
 * RATE LIMITING SERVICE - MAIN ORCHESTRATOR
 * 
 * Simplified main service that orchestrates modular rate limiting components.
 * Complex logic has been moved to focused modules for better maintainability.
 * 
 * PATIENT SAFETY: Emergency medical requests bypass rate limits automatically.
 */

import type { UserRole } from '../types/auth';
import type { RateLimitConfig, RateLimitInfo } from './rateLimit/RateLimitConfig';
import { defaultConfigs } from './rateLimit/RateLimitConfig';
import { AdaptiveRateLimiter } from './rateLimit/AdaptiveRateLimiter';
import { EmergencyBypassHandler } from './rateLimit/EmergencyBypassHandler';
import auditLogger from '../utils/auditLogger';

export interface RateLimitRequest {
  userId?: string | undefined;
  userRole?: UserRole | undefined;
  endpoint: string;
  ip: string;
  userAgent?: string | undefined;
  body?: any | undefined;
  headers?: Record<string, string> | undefined;
}

export interface RateLimitResult extends RateLimitInfo {
  isEmergency?: boolean | undefined;
  emergencyId?: string | undefined;
  bypassReason?: string | undefined;
}

/**
 * Main rate limiting service
 */
class RateLimitingService {
  private rateLimiters = new Map<string, AdaptiveRateLimiter>();
  private emergencyHandlers = new Map<string, EmergencyBypassHandler>();

  constructor() {
    this.initializeRateLimiters();
    this.setupCleanupTimer();
  }

  /**
   * Initialize rate limiters for different endpoint types
   */
  private initializeRateLimiters(): void {
    // Medical endpoints
    this.rateLimiters.set('medical', new AdaptiveRateLimiter(defaultConfigs.medical));
    this.emergencyHandlers.set('medical', new EmergencyBypassHandler(defaultConfigs.medical.emergencyBypass!));

    // Authentication endpoints
    this.rateLimiters.set('auth', new AdaptiveRateLimiter(defaultConfigs.authentication));
    this.emergencyHandlers.set('auth', new EmergencyBypassHandler(defaultConfigs.authentication.emergencyBypass!));

    // General endpoints
    this.rateLimiters.set('general', new AdaptiveRateLimiter(defaultConfigs.general));
    this.emergencyHandlers.set('general', new EmergencyBypassHandler(defaultConfigs.general.emergencyBypass!));

    // Upload endpoints
    this.rateLimiters.set('upload', new AdaptiveRateLimiter(defaultConfigs.upload));
    this.emergencyHandlers.set('upload', new EmergencyBypassHandler(defaultConfigs.upload.emergencyBypass!));
  }

  /**
   * Check rate limit for a request
   */
  async checkRateLimit(request: RateLimitRequest): Promise<RateLimitResult> {
    try {
      const endpointType = this.getEndpointType(request.endpoint);
      const rateLimiter = this.rateLimiters.get(endpointType);
      const emergencyHandler = this.emergencyHandlers.get(endpointType);

      if (!rateLimiter || !emergencyHandler) {
        throw new Error(`No rate limiter configured for endpoint type: ${endpointType}`);
      }

      // Check for emergency bypass first
      const emergencyResult = await emergencyHandler.checkEmergencyBypass(
        request.userId,
        request.userRole,
        request.endpoint,
        request.body,
        request.headers
      );

      if (emergencyResult.allowed) {
        // Log emergency bypass
        await this.logRateLimitEvent(request, 'emergency_bypass', {
          reason: emergencyResult.reason,
          emergencyId: emergencyResult.emergencyId
        });

        return {
          limit: 999999, // Effectively unlimited for emergency
          remaining: 999999,
          resetTime: Date.now() + 60000,
          retryAfter: 0,
          isEmergency: true,
          emergencyId: emergencyResult.emergencyId || undefined,
          bypassReason: emergencyResult.reason || undefined
        };
      }

      // Normal rate limiting
      const key = this.generateRateLimitKey(request);
      const result = rateLimiter.checkRateLimit(key, request.userRole);

      // Log rate limit check
      if (result.remaining === 0) {
        await this.logRateLimitEvent(request, 'rate_limit_exceeded', {
          limit: result.limit,
          retryAfter: result.retryAfter
        });
      }

      return result;

    } catch (error) {
      console.error('Rate limiting error:', error);
      // Return permissive result on error to avoid blocking legitimate requests
      return {
        limit: 1000,
        remaining: 999,
        resetTime: Date.now() + 60000,
        retryAfter: 0
      };
    }
  }

  /**
   * Determine endpoint type for rate limiting
   */
  private getEndpointType(endpoint: string): string {
    if (endpoint.includes('/auth') || endpoint.includes('/login')) {
      return 'auth';
    } else if (endpoint.includes('/upload')) {
      return 'upload';
    } else if (endpoint.includes('/medical') || endpoint.includes('/consultation')) {
      return 'medical';
    } else {
      return 'general';
    }
  }

  /**
   * Generate unique key for rate limiting
   */
  private generateRateLimitKey(request: RateLimitRequest): string {
    const userId = request.userId || 'anonymous';
    const ip = request.ip || 'unknown';
    return `${userId}:${ip}`;
  }

  /**
   * Log rate limiting events for audit
   */
  private async logRateLimitEvent(
    request: RateLimitRequest,
    eventType: string,
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      await auditLogger.logSecurityEvent(eventType, 'medium', {
        userId: request.userId,
        endpoint: request.endpoint,
        ip: request.ip,
        userAgent: request.userAgent,
        timestamp: Date.now(),
        ...metadata
      });
    } catch (error) {
      console.error('Failed to log rate limit event:', error);
    }
  }

  /**
   * Update system load metrics for adaptive rate limiting
   */
  updateSystemLoad(metrics: { cpuUsage?: number; memoryUsage?: number }): void {
    for (const rateLimiter of this.rateLimiters.values()) {
      rateLimiter.updateSystemLoad(metrics);
    }
  }

  /**
   * Get rate limiting statistics
   */
  getStatistics() {
    const stats: Record<string, any> = {};
    
    for (const [type, rateLimiter] of this.rateLimiters.entries()) {
      stats[type] = rateLimiter.getStatistics();
    }

    for (const [type, emergencyHandler] of this.emergencyHandlers.entries()) {
      stats[`${type}_emergency`] = emergencyHandler.getEmergencyStatistics();
    }

    return stats;
  }

  /**
   * Setup cleanup timer to prevent memory leaks
   */
  private setupCleanupTimer(): void {
    setInterval(() => {
      for (const rateLimiter of this.rateLimiters.values()) {
        rateLimiter.cleanup();
      }
      for (const emergencyHandler of this.emergencyHandlers.values()) {
        emergencyHandler.cleanup();
      }
    }, 5 * 60 * 1000); // Cleanup every 5 minutes
  }

  /**
   * Reset all rate limiting state (for testing)
   */
  reset(): void {
    for (const rateLimiter of this.rateLimiters.values()) {
      rateLimiter.reset();
    }
    for (const emergencyHandler of this.emergencyHandlers.values()) {
      emergencyHandler.reset();
    }
  }
}

export default new RateLimitingService();
