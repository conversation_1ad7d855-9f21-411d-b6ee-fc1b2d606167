/**
 * GLOBAL ERROR HANDLER
 * 
 * Centralized error handling utility that provides consistent
 * HIPAA-compliant error processing across the entire application.
 * 
 * FEATURES:
 * - Automatic error sanitization
 * - Emergency error protocols
 * - User-friendly error messages
 * - Comprehensive error logging
 * - Error recovery mechanisms
 * - Performance impact monitoring
 */

import { errorSanitizationService, type ErrorContext, type SanitizedError } from '../services/ErrorSanitizationService';
import auditLogger from './auditLogger';

export interface ErrorHandlerOptions {
  context?: ErrorContext;
  emergencyBypass?: boolean;
  fallbackValue?: any;
  shouldThrow?: boolean;
  logLevel?: 'error' | 'warn' | 'info';
  userNotification?: boolean;
}

export interface ErrorRecoveryStrategy {
  name: string;
  condition: (error: Error) => boolean;
  recovery: (error: Error, context?: ErrorContext) => Promise<any>;
}

export class GlobalErrorHandler {
  private recoveryStrategies: ErrorRecoveryStrategy[] = [];
  private errorNotificationCallbacks: ((sanitizedError: SanitizedError) => void)[] = [];

  constructor() {
    console.log('🛡️ Initializing Global Error Handler...');
    this.initializeRecoveryStrategies();
    this.setupGlobalErrorListeners();
  }

  /**
   * Handle error with comprehensive processing
   */
  async handleError(
    error: Error | string,
    options: ErrorHandlerOptions = {}
  ): Promise<SanitizedError> {
    const {
      context,
      emergencyBypass = false,
      fallbackValue,
      shouldThrow = true,
      logLevel = 'error',
      userNotification = false
    } = options;

    try {
      // Sanitize the error
      const sanitizedError = errorSanitizationService.sanitizeError(
        error,
        context,
        emergencyBypass
      );

      // Log the error
      this.logError(sanitizedError, logLevel);

      // Try recovery strategies
      const recoveryResult = await this.attemptRecovery(
        error instanceof Error ? error : new Error(String(error)),
        context
      );

      if (recoveryResult.success) {
        console.log(`✅ Error recovery successful: ${sanitizedError.id}`);
        return {
          ...sanitizedError,
          userMessage: 'Issue resolved automatically. Continuing...'
        };
      }

      // Notify user if requested
      if (userNotification) {
        this.notifyUser(sanitizedError);
      }

      // Handle emergency scenarios
      if (sanitizedError.severity === 'emergency') {
        await this.handleEmergencyError(sanitizedError);
      }

      // Throw sanitized error if requested
      if (shouldThrow && !fallbackValue) {
        const sanitizedErrorObj = new Error(sanitizedError.userMessage);
        sanitizedErrorObj.name = `${sanitizedError.category}_${sanitizedError.severity}`;
        throw sanitizedErrorObj;
      }

      return sanitizedError;

    } catch (handlingError) {
      console.error('❌ Error in error handling:', handlingError);
      
      // Fallback error handling
      const fallbackError: SanitizedError = {
        id: `FALLBACK_${Date.now()}`,
        category: 'technical',
        severity: 'high',
        userMessage: 'An unexpected error occurred. Please contact support.',
        technicalMessage: 'Error handling failed',
        timestamp: new Date().toISOString(),
        context: context || { component: 'global', operation: 'fallback_error' },
        emergencyBypass: emergencyBypass || false
      };

      if (shouldThrow && !fallbackValue) {
        throw new Error(fallbackError.userMessage);
      }

      return fallbackError;
    }
  }

  /**
   * Handle async operations with error handling
   */
  async handleAsync<T>(
    operation: () => Promise<T>,
    options: ErrorHandlerOptions = {}
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      const sanitizedError = await this.handleError(
        error instanceof Error ? error : new Error(String(error)),
        {
          ...options,
          shouldThrow: false
        }
      );

      if (options.fallbackValue !== undefined) {
        console.log(`🔄 Using fallback value for failed operation: ${sanitizedError.id}`);
        return options.fallbackValue;
      }

      throw new Error(sanitizedError.userMessage);
    }
  }

  /**
   * Handle sync operations with error handling
   */
  handleSync<T>(
    operation: () => T,
    options: ErrorHandlerOptions = {}
  ): T {
    try {
      return operation();
    } catch (error) {
      const sanitizedError = errorSanitizationService.sanitizeError(
        error instanceof Error ? error : new Error(String(error)),
        options.context,
        options.emergencyBypass
      );

      this.logError(sanitizedError, options.logLevel || 'error');

      if (options.userNotification) {
        this.notifyUser(sanitizedError);
      }

      if (options.fallbackValue !== undefined) {
        console.log(`🔄 Using fallback value for failed operation: ${sanitizedError.id}`);
        return options.fallbackValue;
      }

      if (options.shouldThrow !== false) {
        throw new Error(sanitizedError.userMessage);
      }

      throw new Error(sanitizedError.userMessage);
    }
  }

  /**
   * Initialize error recovery strategies
   */
  private initializeRecoveryStrategies(): void {
    this.recoveryStrategies = [
      // Network error recovery
      {
        name: 'network_retry',
        condition: (error) => /network|timeout|connection|fetch/i.test(error.message),
        recovery: async (error, context) => {
          console.log('🔄 Attempting network error recovery...');
          // Could implement retry logic here
          return { success: false, reason: 'Retry not implemented' };
        }
      },

      // Database error recovery
      {
        name: 'database_fallback',
        condition: (error) => /database|sql|postgres|supabase/i.test(error.message),
        recovery: async (error, context) => {
          console.log('🔄 Attempting database error recovery...');
          // Could implement cache fallback or read replica
          return { success: false, reason: 'Database fallback not implemented' };
        }
      },

      // Authentication error recovery
      {
        name: 'auth_refresh',
        condition: (error) => /auth|token|unauthorized|forbidden/i.test(error.message),
        recovery: async (error, context) => {
          console.log('🔄 Attempting authentication recovery...');
          // Could implement token refresh
          return { success: false, reason: 'Auth refresh not implemented' };
        }
      }
    ];

    console.log(`✅ Initialized ${this.recoveryStrategies.length} error recovery strategies`);
  }

  /**
   * Attempt error recovery using registered strategies
   */
  private async attemptRecovery(
    error: Error,
    context?: ErrorContext
  ): Promise<{ success: boolean; reason?: string; result?: any }> {
    for (const strategy of this.recoveryStrategies) {
      if (strategy.condition(error)) {
        try {
          console.log(`🔄 Attempting recovery strategy: ${strategy.name}`);
          const result = await strategy.recovery(error, context);
          
          if (result.success) {
            console.log(`✅ Recovery successful with strategy: ${strategy.name}`);
            return result;
          } else {
            console.log(`⚠️ Recovery failed with strategy: ${strategy.name} - ${result.reason}`);
          }
        } catch (recoveryError) {
          console.error(`❌ Recovery strategy ${strategy.name} threw error:`, recoveryError);
        }
      }
    }

    return { success: false, reason: 'No applicable recovery strategy found' };
  }

  /**
   * Log error with appropriate level
   */
  private logError(sanitizedError: SanitizedError, level: 'error' | 'warn' | 'info'): void {
    const logMessage = `[${sanitizedError.id}] ${sanitizedError.category}/${sanitizedError.severity}: ${sanitizedError.technicalMessage}`;

    switch (level) {
      case 'error':
        console.error(`❌ ${logMessage}`);
        break;
      case 'warn':
        console.warn(`⚠️ ${logMessage}`);
        break;
      case 'info':
        console.info(`ℹ️ ${logMessage}`);
        break;
    }

    // Audit log for compliance
    auditLogger.logError({
      errorId: sanitizedError.id,
      category: sanitizedError.category,
      severity: sanitizedError.severity,
      sanitizedMessage: sanitizedError.technicalMessage,
      userMessage: sanitizedError.userMessage,
      context: sanitizedError.context,
      timestamp: sanitizedError.timestamp
    });
  }

  /**
   * Notify user about error
   */
  private notifyUser(sanitizedError: SanitizedError): void {
    // Call registered notification callbacks
    this.errorNotificationCallbacks.forEach(callback => {
      try {
        callback(sanitizedError);
      } catch (callbackError) {
        console.error('❌ Error in notification callback:', callbackError);
      }
    });
  }

  /**
   * Handle emergency errors
   */
  private async handleEmergencyError(sanitizedError: SanitizedError): Promise<void> {
    console.error(`🚨 Emergency error detected: ${sanitizedError.id}`);

    // Emergency protocols could include:
    // - Alerting administrators
    // - Escalating to emergency services
    // - Triggering backup systems
    // - Notifying on-call personnel

    try {
      await auditLogger.logEmergencyEvent({
        errorId: sanitizedError.id,
        severity: sanitizedError.severity,
        context: sanitizedError.context,
        userMessage: sanitizedError.userMessage,
        timestamp: sanitizedError.timestamp
      });
    } catch (auditError) {
      console.error('❌ Failed to log emergency event:', auditError);
    }
  }

  /**
   * Setup global error listeners
   */
  private setupGlobalErrorListeners(): void {
    // Handle unhandled promise rejections
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        console.error('🚨 Unhandled promise rejection:', event.reason);
        this.handleError(event.reason, {
          context: { component: 'global', operation: 'unhandled_rejection' },
          logLevel: 'error',
          shouldThrow: false
        });
      });

      // Handle uncaught errors
      window.addEventListener('error', (event) => {
        console.error('🚨 Uncaught error:', event.error);
        this.handleError(event.error, {
          context: { component: 'global', operation: 'uncaught_error' },
          logLevel: 'error',
          shouldThrow: false
        });
      });
    }

    console.log('👂 Global error listeners set up');
  }

  /**
   * Register error notification callback
   */
  onError(callback: (sanitizedError: SanitizedError) => void): void {
    this.errorNotificationCallbacks.push(callback);
  }

  /**
   * Register custom recovery strategy
   */
  registerRecoveryStrategy(strategy: ErrorRecoveryStrategy): void {
    this.recoveryStrategies.push(strategy);
    console.log(`🔧 Registered recovery strategy: ${strategy.name}`);
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(): Record<string, number> {
    return errorSanitizationService.getErrorStatistics();
  }
}

// Export singleton instance
export const globalErrorHandler = new GlobalErrorHandler();

// Convenience functions
export const handleError = (error: Error | string, options?: ErrorHandlerOptions) =>
  globalErrorHandler.handleError(error, options);

export const handleAsync = <T>(operation: () => Promise<T>, options?: ErrorHandlerOptions) =>
  globalErrorHandler.handleAsync(operation, options);

export const handleSync = <T>(operation: () => T, options?: ErrorHandlerOptions) =>
  globalErrorHandler.handleSync(operation, options);

export default globalErrorHandler;
