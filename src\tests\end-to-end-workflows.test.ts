/**
 * END-TO-END WORKFLOW TEST SUITE
 * 
 * Comprehensive end-to-end testing for VoiceHealth AI workflows including:
 * - Complete patient consultation workflows
 * - Emergency protocol workflows
 * - Cultural adaptation workflows
 * - Multi-service integration workflows
 * - Performance validation workflows
 * 
 * TARGET: Validate complete system functionality and integration
 */

// Vitest globals are available via vitest.d.ts
import { aiOrchestrator } from '../services/aiOrchestrator';
import { authenticationService } from '../services/AuthenticationService';
import { clinicalDocumentationService } from '../services/ClinicalDocumentationService';
import { advancedRiskStratificationService } from '../services/AdvancedRiskStratificationService';
import { culturalValidationService } from '../services/CulturalValidationService';
import { encryptionService } from '../services/EncryptionService';
import { performanceValidationService } from '../services/PerformanceValidationService';

// =====================================================
// COMPLETE PATIENT CONSULTATION WORKFLOW
// =====================================================

describe('Complete Patient Consultation Workflow', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should complete full patient consultation workflow', async () => {
    const workflowStartTime = Date.now();

    // Step 1: Provider Authentication
    const authRequest = {
      email: '<EMAIL>',
      password: 'securePassword123',
      clientInfo: {
        userAgent: 'VoiceHealth App/1.0',
        ipAddress: '*************'
      }
    };

    // Mock authentication
    vi.spyOn(authenticationService['supabase'].auth, 'signInWithPassword')
      .mockResolvedValue({
        data: {
          user: { id: 'provider-123', email: '<EMAIL>' },
          session: { access_token: 'mock-token' }
        },
        error: null
      });

    vi.spyOn(authenticationService as any, 'getUserProfile')
      .mockResolvedValue({
        id: 'provider-123',
        email: '<EMAIL>',
        role: 'provider',
        profile: {
          firstName: 'Dr. Sarah',
          lastName: 'Johnson',
          country: 'GH',
          language: 'en',
          timezone: 'GMT'
        },
        permissions: [
          { resource: 'patient_data', actions: ['read', 'write'] },
          { resource: 'clinical_documentation', actions: ['read', 'write'] }
        ],
        lastLogin: new Date(),
        mfaEnabled: false,
        emergencyAccess: false,
        status: 'active'
      });

    const authResult = await authenticationService.authenticate(authRequest);
    expect(authResult.success).toBe(true);
    expect(authResult.user?.role).toBe('provider');

    // Step 2: Voice Transcription Processing
    const voiceTranscription = `
      Patient is a 45-year-old Akan woman presenting with severe headache and fever for 3 days.
      She reports the headache started gradually and has been getting worse.
      Associated symptoms include nausea, photophobia, and neck stiffness.
      She has a history of hypertension and takes lisinopril daily.
      She lives with her extended family and prefers traditional healing alongside modern medicine.
      Vital signs: blood pressure 160/95, temperature 38.8 degrees Celsius, heart rate 95.
      Physical examination reveals neck stiffness and photophobia.
      She is concerned about missing work as a teacher.
    `;

    // Step 3: Cultural Context Assessment
    const culturalContext = {
      cultureCode: 'akan',
      country: 'GH',
      languagePreference: 'en',
      ethnicGroup: 'akan',
      religiousContext: ['christian', 'traditional'],
      familyStructure: 'extended',
      familyInvolvementLevel: 'high',
      traditionalMedicineOpenness: 4,
      educationLevel: 'tertiary',
      occupation: 'teacher'
    };

    const culturalValidation = await aiOrchestrator.validateCulturalContent(
      voiceTranscription,
      culturalContext
    );

    expect(culturalValidation.success).toBe(true);
    expect(culturalValidation.validation.overallScore).toBeGreaterThan(70);

    // Step 4: Clinical Documentation Generation
    const documentationResult = await aiOrchestrator.generateClinicalDocumentation(
      voiceTranscription,
      'patient-akan-123',
      'provider-123',
      culturalContext
    );

    expect(documentationResult.success).toBe(true);
    expect(documentationResult.documentation.clinicalNote).toBeDefined();
    expect(documentationResult.documentation.clinicalNote.chiefComplaint).toContain('headache');
    expect(documentationResult.documentation.clinicalNote.chiefComplaint).toContain('fever');
    expect(documentationResult.documentation.culturalAdaptations).toBeDefined();
    expect(documentationResult.documentation.culturalAdaptations.length).toBeGreaterThan(0);

    // Step 5: Advanced Risk Stratification
    const patientData = {
      id: 'patient-akan-123',
      demographics: {
        age: 45,
        gender: 'female',
        country: 'GH',
        ethnicity: 'akan'
      },
      medicalHistory: ['hypertension'],
      symptoms: ['headache', 'fever', 'neck stiffness', 'photophobia', 'nausea'],
      behavioralFactors: {
        smokingStatus: 'never',
        physicalActivity: 'moderate',
        dietQuality: 'good',
        adherenceToMedications: 'good'
      },
      socioeconomicFactors: {
        income: 'middle',
        education: 'tertiary',
        healthcareAccess: 'moderate',
        occupation: 'teacher'
      },
      environmentalFactors: ['urban_environment']
    };

    const riskAssessment = await aiOrchestrator.performRiskStratification(
      patientData,
      culturalContext
    );

    expect(riskAssessment.success).toBe(true);
    expect(riskAssessment.riskAssessment.overallRiskScore).toBeGreaterThan(0);
    expect(riskAssessment.riskAssessment.riskCategory).toMatch(/low|moderate|high|critical/);

    // Step 6: Data Encryption for HIPAA Compliance
    const clinicalData = {
      patientId: 'patient-akan-123',
      providerId: 'provider-123',
      consultation: documentationResult.documentation,
      riskAssessment: riskAssessment.riskAssessment,
      culturalContext
    };

    const encryptedData = await encryptionService.encryptPHI(
      clinicalData,
      'patient-akan-123'
    );

    expect(encryptedData.data).toBeDefined();
    expect(encryptedData.algorithm).toBe('AES-256-GCM');

    // Step 7: Performance Validation
    const workflowEndTime = Date.now();
    const totalWorkflowTime = workflowEndTime - workflowStartTime;

    expect(totalWorkflowTime).toBeLessThan(15000); // Complete workflow < 15 seconds

    // Record performance metric
    performanceValidationService.recordMetric({
      operation: 'complete_consultation_workflow',
      responseTime: totalWorkflowTime,
      timestamp: new Date(),
      success: true,
      target: 15000,
      category: 'api',
      metadata: {
        steps: 7,
        culturalAdaptations: documentationResult.documentation.culturalAdaptations.length,
        riskScore: riskAssessment.riskAssessment.overallRiskScore
      }
    });

    console.log(`✅ Complete consultation workflow completed in ${totalWorkflowTime}ms`);
  }, 30000); // 30 second timeout

  it('should handle consultation workflow with traditional medicine integration', async () => {
    const culturalContext = {
      cultureCode: 'akan',
      country: 'GH',
      traditionalMedicineOpenness: 5,
      familyInvolvementLevel: 'high'
    };

    const voiceTranscription = `
      Patient reports using herbal remedies from traditional healer for headache.
      Family elder recommended specific herbs for fever treatment.
      Patient wants to continue traditional medicine alongside modern treatment.
    `;

    const documentationResult = await aiOrchestrator.generateClinicalDocumentation(
      voiceTranscription,
      'patient-traditional-123',
      'provider-123',
      culturalContext
    );

    expect(documentationResult.success).toBe(true);
    
    const traditionalMedicineAdaptation = documentationResult.documentation.culturalAdaptations.find(
      adaptation => adaptation.aspect === 'traditional_medicine'
    );
    
    expect(traditionalMedicineAdaptation).toBeDefined();
    expect(traditionalMedicineAdaptation?.adaptation).toContain('traditional medicine');
  });
});

// =====================================================
// EMERGENCY PROTOCOL WORKFLOW
// =====================================================

describe('Emergency Protocol Workflow', () => {
  it('should complete emergency workflow within performance targets', async () => {
    const emergencyStartTime = Date.now();

    // Step 1: Emergency Authentication (< 50ms target)
    const emergencyAuthStart = Date.now();
    const emergencyAuth = await authenticationService.authenticate({
      email: '<EMAIL>',
      password: 'emergency',
      emergencyOverride: true,
      clientInfo: {
        userAgent: 'Emergency Response System',
        ipAddress: '*************'
      }
    });
    const emergencyAuthTime = Date.now() - emergencyAuthStart;

    expect(emergencyAuth.success).toBe(true);
    expect(emergencyAuth.emergencyBypass).toBe(true);
    expect(emergencyAuthTime).toBeLessThan(50);

    // Step 2: Emergency Risk Assessment (< 1000ms target)
    const emergencyRiskStart = Date.now();
    const emergencyPatientData = {
      id: 'emergency-patient-456',
      demographics: {
        age: 35,
        gender: 'male',
        country: 'GH'
      },
      medicalHistory: [],
      symptoms: ['chest pain', 'shortness of breath', 'sweating'],
      behavioralFactors: {
        smokingStatus: 'current',
        physicalActivity: 'sedentary',
        dietQuality: 'poor',
        adherenceToMedications: 'unknown'
      },
      socioeconomicFactors: {
        income: 'low',
        education: 'primary',
        healthcareAccess: 'limited'
      },
      environmentalFactors: ['rural_environment']
    };

    const emergencyRisk = await aiOrchestrator.performRiskStratification(
      emergencyPatientData
    );
    const emergencyRiskTime = Date.now() - emergencyRiskStart;

    expect(emergencyRisk.success).toBe(true);
    expect(emergencyRiskTime).toBeLessThan(1000);

    // Step 3: Emergency Protocol Execution (< 1000ms target)
    const emergencyProtocolStart = Date.now();
    const emergencyProtocol = await aiOrchestrator.optimizePerformance('emergency_protocol');
    const emergencyProtocolTime = Date.now() - emergencyProtocolStart;

    expect(emergencyProtocol.success).toBe(true);
    expect(emergencyProtocolTime).toBeLessThan(1000);

    // Step 4: Total Emergency Response Time Validation
    const totalEmergencyTime = Date.now() - emergencyStartTime;
    expect(totalEmergencyTime).toBeLessThan(2000); // < 2 seconds total

    console.log(`🚨 Emergency workflow completed in ${totalEmergencyTime}ms`);
    console.log(`  - Auth: ${emergencyAuthTime}ms`);
    console.log(`  - Risk: ${emergencyRiskTime}ms`);
    console.log(`  - Protocol: ${emergencyProtocolTime}ms`);
  });

  it('should handle emergency workflow with family notification', async () => {
    const emergencyAuth = await authenticationService.authenticate({
      email: '<EMAIL>',
      password: 'emergency',
      emergencyOverride: true
    });

    expect(emergencyAuth.success).toBe(true);
    expect(emergencyAuth.user?.emergencyAccess).toBe(true);

    // Verify emergency permissions
    const hasEmergencyPermission = authenticationService.hasPermission(
      emergencyAuth.user!,
      'emergency_protocols',
      'execute'
    );

    const hasPatientDataPermission = authenticationService.hasPermission(
      emergencyAuth.user!,
      'patient_data',
      'read'
    );

    expect(hasEmergencyPermission).toBe(true);
    expect(hasPatientDataPermission).toBe(true);
  });
});

// =====================================================
// MULTI-CULTURAL WORKFLOW
// =====================================================

describe('Multi-Cultural Workflow', () => {
  it('should handle multiple cultural contexts in single session', async () => {
    const cultures = [
      { cultureCode: 'akan', country: 'GH', language: 'en' },
      { cultureCode: 'yoruba', country: 'NG', language: 'en' },
      { cultureCode: 'kikuyu', country: 'KE', language: 'sw' },
      { cultureCode: 'zulu', country: 'ZA', language: 'en' },
      { cultureCode: 'amhara', country: 'ET', language: 'am' }
    ];

    const testContent = 'Patient needs medication and family consultation for treatment decisions.';

    for (const culture of cultures) {
      const validation = await aiOrchestrator.validateCulturalContent(
        testContent,
        culture
      );

      expect(validation.success).toBe(true);
      expect(validation.validation.overallScore).toBeGreaterThan(0);
      expect(validation.validation.culturalSensitivity).toBeDefined();
    }
  });

  it('should adapt clinical documentation for different cultures', async () => {
    const baseTranscription = 'Patient needs treatment and follow-up care.';
    
    const cultures = [
      {
        cultureCode: 'akan',
        familyInvolvementLevel: 'high',
        traditionalMedicineOpenness: 4
      },
      {
        cultureCode: 'yoruba',
        familyInvolvementLevel: 'high',
        traditionalMedicineOpenness: 5
      }
    ];

    for (const culture of cultures) {
      const documentation = await aiOrchestrator.generateClinicalDocumentation(
        baseTranscription,
        `patient-${culture.cultureCode}-123`,
        'provider-123',
        culture
      );

      expect(documentation.success).toBe(true);
      expect(documentation.documentation.culturalAdaptations).toBeDefined();
      expect(documentation.documentation.culturalAdaptations.length).toBeGreaterThan(0);

      const familyAdaptation = documentation.documentation.culturalAdaptations.find(
        adaptation => adaptation.aspect === 'family_involvement'
      );
      expect(familyAdaptation).toBeDefined();
    }
  });
});

// =====================================================
// PERFORMANCE VALIDATION WORKFLOW
// =====================================================

describe('Performance Validation Workflow', () => {
  it('should validate system-wide performance compliance', async () => {
    const performanceReport = await performanceValidationService.runPerformanceTestSuite();

    expect(performanceReport.overallStatus).toMatch(/pass|fail|warning/);
    expect(performanceReport.summary.emergencyCompliance).toBe(true);
    expect(performanceReport.summary.authenticationCompliance).toBe(true);
    expect(performanceReport.summary.totalTests).toBeGreaterThan(0);

    // Check specific performance targets
    const emergencyTests = performanceReport.testResults.filter(
      test => test.category === 'emergency'
    );

    for (const test of emergencyTests) {
      expect(test.actualP95).toBeLessThanOrEqual(test.target);
      expect(test.successRate).toBeGreaterThanOrEqual(95);
    }
  }, 60000); // 60 second timeout for full performance suite

  it('should maintain performance under concurrent load', async () => {
    const concurrentOperations = 10;
    const promises: Promise<any>[] = [];

    const startTime = Date.now();

    // Simulate concurrent operations
    for (let i = 0; i < concurrentOperations; i++) {
      promises.push(
        aiOrchestrator.generateClinicalDocumentation(
          `Patient ${i} has symptoms`,
          `patient-${i}`,
          'provider-123'
        )
      );
    }

    const results = await Promise.all(promises);
    const totalTime = Date.now() - startTime;

    // All operations should succeed
    expect(results.every(result => result.success)).toBe(true);

    // Average time per operation should be reasonable
    const averageTime = totalTime / concurrentOperations;
    expect(averageTime).toBeLessThan(10000); // < 10 seconds per operation on average
  });
});

// =====================================================
// DATA SECURITY AND COMPLIANCE WORKFLOW
// =====================================================

describe('Data Security and Compliance Workflow', () => {
  it('should maintain HIPAA compliance throughout workflow', async () => {
    const patientData = {
      patientId: 'hipaa-test-patient',
      personalInfo: {
        name: 'Jane Doe',
        dateOfBirth: '1985-03-15',
        ssn: '***********'
      },
      medicalInfo: {
        diagnosis: 'Hypertension',
        medications: ['Lisinopril 10mg'],
        allergies: ['Penicillin']
      }
    };

    // Encrypt PHI data
    const encrypted = await encryptionService.encryptPHI(
      patientData,
      'hipaa-test-patient'
    );

    expect(encrypted.data).toBeDefined();
    expect(encrypted.algorithm).toBe('AES-256-GCM');

    // Decrypt PHI data with audit logging
    const decrypted = await encryptionService.decryptPHI(
      encrypted,
      'provider-123'
    );

    expect(decrypted.success).toBe(true);
    expect(decrypted.data).toBeDefined();

    const decryptedData = JSON.parse(decrypted.data!);
    expect(decryptedData.personalInfo.name).toBe('Jane Doe');
    expect(decryptedData.medicalInfo.diagnosis).toBe('Hypertension');
  });

  it('should generate tamper-proof audit trails', async () => {
    const auditData = 'Critical patient data access event';
    
    const hash = await encryptionService.generateHash(auditData);
    
    expect(hash.hash).toBeDefined();
    expect(hash.algorithm).toBe('SHA-256');
    expect(hash.salt).toBeDefined();

    // Verify data integrity
    const isValid = await encryptionService.verifyHash(auditData, hash);
    expect(isValid).toBe(true);

    // Verify tamper detection
    const isTampered = await encryptionService.verifyHash(
      'Tampered audit data',
      hash
    );
    expect(isTampered).toBe(false);
  });
});
