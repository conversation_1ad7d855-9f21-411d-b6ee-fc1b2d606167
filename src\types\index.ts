/**
 * TYPE DEFINITIONS INDEX
 * 
 * Central export point for all TypeScript type definitions
 * used throughout the VoiceHealth AI application.
 */

// Medical data types
export * from './medical';

// Authentication and RBAC types
export * from './auth';

// API and error handling types - explicit exports to avoid conflicts
export type {
  ApiResponse,
  PaginatedResponse,
  ErrorSeverity,
  ErrorCategory,
  ApiError,
  HttpStatusCode,
  ApiRequestConfig,
  RateLimitInfo,
  RateLimitedResponse,
  NetworkStatus,
  SyncStatus,
  SyncOperation,
  MedicalDataApiResponse,
  ValidationError,
  ValidationErrorResponse,
  AuthenticationError,
  AuthorizationError,
  NetworkError,
  ErrorRecoveryStrategy,
  ErrorRecoveryPlan,
  ApiClient,
  WebhookPayload,
  MedicalDataWebhook,
  ApiPerformanceMetrics,
  HealthCheckResponse
} from './api';

// Enhancement types
export * from './enhancements';

// Re-export commonly used types for convenience
export type {
  // Medical data
  MedicalCondition,
  Medication,
  Symptom,
  PatientProfile,
  Consultation,
  MedicalDataResponse,
  EncryptedMedicalData,
  StoredMedicalData,
  MedicalSeverity,
  UserRole,

  // Services
  MedicalDataService,
  EncryptionService,
  AuditLogger
} from './medical';

export type {
  // Authentication
  User,
  Session,
  UserMetadata,
  AppMetadata,
  AuthResponse,
  RoleDefinition,
  ResourceType,
  Action,
  Permission,
  PermissionCondition,
  AuthContext,
  EmergencyAccessRequest,
  AuthService,
  RBACService
} from './auth';

// API types are exported above in the explicit export block to avoid conflicts

// Constants
export { ROLE_DEFINITIONS } from './auth';
