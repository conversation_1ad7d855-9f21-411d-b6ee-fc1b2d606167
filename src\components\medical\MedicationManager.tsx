/**
 * Medication Manager Component
 * Manage patient medications and reminders
 */

import React from 'react';

interface MedicationManagerProps {
  className?: string;
}

const MedicationManager: React.FC<MedicationManagerProps> = ({ className = '' }) => {
  return (
    <div className={`p-6 bg-white ${className}`}>
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-800 mb-2">Medication Manager</h2>
        <p className="text-gray-600">Manage your medications and reminders</p>
      </div>
      
      <div className="space-y-4">
        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
          <h3 className="font-semibold text-purple-800 mb-2">Current Medications</h3>
          <p className="text-purple-700 text-sm">View and manage your current medications</p>
          <button className="mt-3 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
            Add Medication
          </button>
        </div>
        
        <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
          <h3 className="font-semibold text-orange-800 mb-2">Reminders</h3>
          <p className="text-orange-700 text-sm">Set up medication reminders and alerts</p>
          <button className="mt-3 px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors">
            Set Reminder
          </button>
        </div>
      </div>
      
      <div className="mt-6 bg-yellow-50 p-3 rounded border border-yellow-200">
        <p className="text-yellow-800 text-sm">
          ⚠️ This is a placeholder component. Full medication management functionality will be implemented.
        </p>
      </div>
    </div>
  );
};

export default MedicationManager;
