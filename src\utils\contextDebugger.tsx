/**
 * CONTEXT DEBUGGING AND MONITORING TOOL
 * 
 * This tool provides comprehensive context debugging and monitoring with:
 * - Real-time context performance monitoring
 * - Re-render tracking and optimization suggestions
 * - Context state visualization and debugging
 * - Performance bottleneck identification
 * - Medical data context compliance checking
 * - Context usage analytics and reporting
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Debugging must not expose sensitive medical data
 * - Performance monitoring must not impact system performance
 * - Context debugging must maintain audit compliance
 * - Monitoring must prioritize emergency data contexts
 * - Debug tools must be disabled in production
 */

import React, { 
  createContext, 
  useContext, 
  useEffect, 
  useState, 
  useCallback, 
  useRef,
  memo
} from 'react';

interface ContextPerformanceMetrics {
  readonly contextName: string;
  renderCount: number; // Mutable for tracking
  lastRenderTime: number; // Mutable for tracking
  averageRenderTime: number; // Mutable for tracking
  stateChanges: number; // Mutable for tracking
  subscriberCount: number; // Mutable for tracking
  memoryUsage: number; // Mutable for tracking
  cacheHitRate: number; // Mutable for tracking
  errorCount: number; // Mutable for tracking
  warningCount: number; // Mutable for tracking
}

interface ContextDebugInfo {
  readonly contextName: string;
  currentState: any; // Mutable for state updates
  stateHistory: Array<{ // Mutable for push/splice operations
    timestamp: number;
    state: any;
    trigger: string;
  }>;
  renderHistory: Array<{ // Mutable for push/splice operations
    timestamp: number;
    duration: number;
    reason: string;
  }>;
  subscribers: string[]; // Mutable for push/splice operations
  performance: ContextPerformanceMetrics; // Mutable for metric updates
  recommendations: string[]; // Mutable for updates
  alerts: ContextAlert[]; // Mutable for push/splice operations
}

interface ContextAlert {
  readonly id: string;
  readonly severity: 'info' | 'warning' | 'error' | 'critical';
  readonly message: string;
  readonly timestamp: number;
  readonly contextName: string;
  readonly category: 'performance' | 'memory' | 'security' | 'compliance';
  readonly medicalDataAffected: boolean;
}

interface ContextMonitorConfig {
  readonly enabled: boolean;
  readonly trackRenders: boolean;
  readonly trackStateChanges: boolean;
  readonly trackPerformance: boolean;
  readonly alertThresholds: {
    readonly maxRenderTime: number;
    readonly maxRenderCount: number;
    readonly maxMemoryUsage: number;
    readonly minCacheHitRate: number;
  };
  readonly medicalDataContexts: string[];
  readonly sensitiveDataFields: string[];
}

class ContextDebugger {
  private readonly config: ContextMonitorConfig;
  private readonly contextRegistry: Map<string, ContextDebugInfo>;
  private readonly performanceObserver: PerformanceObserver | null;
  private readonly alertHandlers: Array<(alert: ContextAlert) => void>;
  private monitoringActive: boolean;

  constructor(config: Partial<ContextMonitorConfig> = {}) {
    this.config = {
      enabled: process.env.NODE_ENV === 'development',
      trackRenders: true,
      trackStateChanges: true,
      trackPerformance: true,
      alertThresholds: {
        maxRenderTime: 16, // 16ms for 60fps
        maxRenderCount: 100,
        maxMemoryUsage: 50 * 1024 * 1024, // 50MB
        minCacheHitRate: 0.8 // 80%
      },
      medicalDataContexts: [
        'OptimizedAuthContext',
        'OptimizedMedicalDataContext',
        'MedicalConditionsContext',
        'MedicationsContext',
        'SymptomsContext'
      ],
      sensitiveDataFields: [
        'user',
        'userProfile',
        'conditions',
        'medications',
        'symptoms',
        'patientId',
        'medicalData'
      ],
      ...config
    };

    this.contextRegistry = new Map();
    this.alertHandlers = [];
    this.monitoringActive = false;

    // Initialize performance observer if available
    this.performanceObserver = typeof PerformanceObserver !== 'undefined' 
      ? new PerformanceObserver(this.handlePerformanceEntries.bind(this))
      : null;

    if (this.config.enabled) {
      this.startMonitoring();
    }
  }

  /**
   * Start context monitoring
   */
  startMonitoring(): void {
    if (!this.config.enabled || this.monitoringActive) return;

    this.monitoringActive = true;

    // Start performance monitoring
    if (this.performanceObserver && this.config.trackPerformance) {
      this.performanceObserver.observe({ 
        entryTypes: ['measure', 'navigation', 'resource'] 
      });
    }

    console.log('🔍 Context Debugger: Monitoring started');
  }

  /**
   * Stop context monitoring
   */
  stopMonitoring(): void {
    if (!this.monitoringActive) return;

    this.monitoringActive = false;

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }

    console.log('🔍 Context Debugger: Monitoring stopped');
  }

  /**
   * Register a context for monitoring
   */
  registerContext(
    contextName: string, 
    initialState: any,
    options: {
      isMedicalContext?: boolean;
      sensitiveFields?: string[];
    } = {}
  ): void {
    if (!this.config.enabled) return;

    const debugInfo: ContextDebugInfo = {
      contextName,
      currentState: this.sanitizeState(initialState, options.sensitiveFields),
      stateHistory: [{
        timestamp: Date.now(),
        state: this.sanitizeState(initialState, options.sensitiveFields),
        trigger: 'initial'
      }],
      renderHistory: [],
      subscribers: [],
      performance: {
        contextName,
        renderCount: 0,
        lastRenderTime: Date.now(),
        averageRenderTime: 0,
        stateChanges: 0,
        subscriberCount: 0,
        memoryUsage: 0,
        cacheHitRate: 1,
        errorCount: 0,
        warningCount: 0
      },
      recommendations: [],
      alerts: []
    };

    this.contextRegistry.set(contextName, debugInfo);

    // Check if this is a medical context
    if (options.isMedicalContext || this.config.medicalDataContexts.includes(contextName)) {
      this.createAlert(
        'medical-context-registered',
        'info',
        `Medical context ${contextName} registered for monitoring`,
        'compliance',
        contextName,
        true
      );
    }
  }

  /**
   * Track context state change
   */
  trackStateChange(
    contextName: string, 
    newState: any, 
    trigger: string,
    sensitiveFields?: string[]
  ): void {
    if (!this.config.enabled || !this.config.trackStateChanges) return;

    const debugInfo = this.contextRegistry.get(contextName);
    if (!debugInfo) return;

    const sanitizedState = this.sanitizeState(newState, sensitiveFields);
    
    // Update state history
    debugInfo.stateHistory.push({
      timestamp: Date.now(),
      state: sanitizedState,
      trigger
    });

    // Keep only last 100 state changes
    if (debugInfo.stateHistory.length > 100) {
      debugInfo.stateHistory.splice(0, debugInfo.stateHistory.length - 100);
    }

    // Update current state
    debugInfo.currentState = sanitizedState;
    debugInfo.performance.stateChanges++;

    // Check for performance issues
    this.checkStateChangePerformance(contextName, debugInfo);

    this.contextRegistry.set(contextName, debugInfo);
  }

  /**
   * Track context render
   */
  trackRender(
    contextName: string, 
    duration: number, 
    reason: string = 'unknown'
  ): void {
    if (!this.config.enabled || !this.config.trackRenders) return;

    const debugInfo = this.contextRegistry.get(contextName);
    if (!debugInfo) return;

    // Update render history
    debugInfo.renderHistory.push({
      timestamp: Date.now(),
      duration,
      reason
    });

    // Keep only last 100 renders
    if (debugInfo.renderHistory.length > 100) {
      debugInfo.renderHistory.splice(0, debugInfo.renderHistory.length - 100);
    }

    // Update performance metrics
    debugInfo.performance.renderCount++;
    debugInfo.performance.lastRenderTime = Date.now();
    
    const totalRenderTime = debugInfo.renderHistory.reduce((sum, render) => sum + render.duration, 0);
    debugInfo.performance.averageRenderTime = totalRenderTime / debugInfo.renderHistory.length;

    // Check for performance issues
    this.checkRenderPerformance(contextName, debugInfo, duration);

    this.contextRegistry.set(contextName, debugInfo);
  }

  /**
   * Track context subscriber
   */
  trackSubscriber(contextName: string, subscriberName: string, action: 'add' | 'remove'): void {
    if (!this.config.enabled) return;

    const debugInfo = this.contextRegistry.get(contextName);
    if (!debugInfo) return;

    if (action === 'add') {
      if (!debugInfo.subscribers.includes(subscriberName)) {
        debugInfo.subscribers.push(subscriberName);
      }
    } else {
      const index = debugInfo.subscribers.indexOf(subscriberName);
      if (index > -1) {
        debugInfo.subscribers.splice(index, 1);
      }
    }

    debugInfo.performance.subscriberCount = debugInfo.subscribers.length;

    // Check for too many subscribers
    if (debugInfo.subscribers.length > 20) {
      this.createAlert(
        'high-subscriber-count',
        'warning',
        `Context ${contextName} has ${debugInfo.subscribers.length} subscribers - consider context splitting`,
        'performance',
        contextName,
        this.config.medicalDataContexts.includes(contextName)
      );
    }

    this.contextRegistry.set(contextName, debugInfo);
  }

  /**
   * Get debug information for a context
   */
  getContextDebugInfo(contextName: string): ContextDebugInfo | null {
    if (!this.config.enabled) return null;
    return this.contextRegistry.get(contextName) || null;
  }

  /**
   * Get all context debug information
   */
  getAllContextDebugInfo(): ContextDebugInfo[] {
    if (!this.config.enabled) return [];
    return Array.from(this.contextRegistry.values());
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    totalContexts: number;
    medicalContexts: number;
    averageRenderTime: number;
    totalRenders: number;
    totalStateChanges: number;
    activeAlerts: number;
    recommendations: string[];
  } {
    if (!this.config.enabled) {
      return {
        totalContexts: 0,
        medicalContexts: 0,
        averageRenderTime: 0,
        totalRenders: 0,
        totalStateChanges: 0,
        activeAlerts: 0,
        recommendations: []
      };
    }

    const allContexts = this.getAllContextDebugInfo();
    const medicalContexts = allContexts.filter(ctx => 
      this.config.medicalDataContexts.includes(ctx.contextName)
    );

    const totalRenders = allContexts.reduce((sum, ctx) => sum + ctx.performance.renderCount, 0);
    const totalStateChanges = allContexts.reduce((sum, ctx) => sum + ctx.performance.stateChanges, 0);
    const averageRenderTime = allContexts.reduce((sum, ctx) => sum + ctx.performance.averageRenderTime, 0) / allContexts.length;
    const activeAlerts = allContexts.reduce((sum, ctx) => sum + ctx.alerts.length, 0);

    const recommendations = this.generateGlobalRecommendations(allContexts);

    return {
      totalContexts: allContexts.length,
      medicalContexts: medicalContexts.length,
      averageRenderTime,
      totalRenders,
      totalStateChanges,
      activeAlerts,
      recommendations
    };
  }

  /**
   * Add alert handler
   */
  onAlert(handler: (alert: ContextAlert) => void): void {
    this.alertHandlers.push(handler);
  }

  /**
   * Export debug data for analysis
   */
  exportDebugData(): any {
    if (!this.config.enabled) return null;

    return {
      timestamp: Date.now(),
      config: this.config,
      contexts: this.getAllContextDebugInfo(),
      summary: this.getPerformanceSummary()
    };
  }

  // Private helper methods
  private sanitizeState(state: any, sensitiveFields?: string[]): any {
    if (!state || typeof state !== 'object') return state;

    const fieldsToSanitize = [
      ...this.config.sensitiveDataFields,
      ...(sensitiveFields || [])
    ];

    const sanitized = { ...state };

    for (const field of fieldsToSanitize) {
      if (field in sanitized) {
        if (Array.isArray(sanitized[field])) {
          sanitized[field] = `[Array(${sanitized[field].length})]`;
        } else if (typeof sanitized[field] === 'object') {
          sanitized[field] = '[Object]';
        } else if (typeof sanitized[field] === 'string') {
          sanitized[field] = `[String(${sanitized[field].length})]`;
        } else {
          sanitized[field] = '[Sanitized]';
        }
      }
    }

    return sanitized;
  }

  private checkStateChangePerformance(contextName: string, debugInfo: ContextDebugInfo): void {
    const recentChanges = debugInfo.stateHistory.slice(-10);
    const changeFrequency = recentChanges.length / 10; // Changes per second over last 10 seconds

    if (changeFrequency > 5) {
      this.createAlert(
        'high-state-change-frequency',
        'warning',
        `Context ${contextName} has high state change frequency: ${changeFrequency.toFixed(2)}/sec`,
        'performance',
        contextName,
        this.config.medicalDataContexts.includes(contextName)
      );
    }
  }

  private checkRenderPerformance(contextName: string, debugInfo: ContextDebugInfo, duration: number): void {
    if (duration > this.config.alertThresholds.maxRenderTime) {
      this.createAlert(
        'slow-render',
        'warning',
        `Context ${contextName} render took ${duration.toFixed(2)}ms (threshold: ${this.config.alertThresholds.maxRenderTime}ms)`,
        'performance',
        contextName,
        this.config.medicalDataContexts.includes(contextName)
      );
    }

    if (debugInfo.performance.renderCount > this.config.alertThresholds.maxRenderCount) {
      this.createAlert(
        'excessive-renders',
        'error',
        `Context ${contextName} has rendered ${debugInfo.performance.renderCount} times`,
        'performance',
        contextName,
        this.config.medicalDataContexts.includes(contextName)
      );
    }
  }

  private createAlert(
    id: string,
    severity: 'info' | 'warning' | 'error' | 'critical',
    message: string,
    category: 'performance' | 'memory' | 'security' | 'compliance',
    contextName: string,
    medicalDataAffected: boolean = false
  ): void {
    const alert: ContextAlert = {
      id: `${id}-${Date.now()}`,
      severity,
      message,
      timestamp: Date.now(),
      contextName,
      category,
      medicalDataAffected
    };

    // Add to context alerts
    const debugInfo = this.contextRegistry.get(contextName);
    if (debugInfo) {
      debugInfo.alerts.push(alert);
      
      // Keep only last 50 alerts per context
      if (debugInfo.alerts.length > 50) {
        debugInfo.alerts.splice(0, debugInfo.alerts.length - 50);
      }

      this.contextRegistry.set(contextName, debugInfo);
    }

    // Notify alert handlers
    this.alertHandlers.forEach(handler => {
      try {
        handler(alert);
      } catch (error) {
        console.error('Context Debugger: Alert handler error:', error);
      }
    });

    // Log critical alerts
    if (severity === 'critical' || medicalDataAffected) {
      console.error('🚨 Context Debugger Critical Alert:', alert);
    } else if (severity === 'error') {
      console.error('❌ Context Debugger Error:', alert);
    } else if (severity === 'warning') {
      console.warn('⚠️ Context Debugger Warning:', alert);
    }
  }

  private generateGlobalRecommendations(contexts: ContextDebugInfo[]): string[] {
    const recommendations: string[] = [];

    const slowContexts = contexts.filter(ctx => ctx.performance.averageRenderTime > 10);
    if (slowContexts.length > 0) {
      recommendations.push(`Consider optimizing slow contexts: ${slowContexts.map(ctx => ctx.contextName).join(', ')}`);
    }

    const highRenderContexts = contexts.filter(ctx => ctx.performance.renderCount > 50);
    if (highRenderContexts.length > 0) {
      recommendations.push(`Consider memoization for frequently rendering contexts: ${highRenderContexts.map(ctx => ctx.contextName).join(', ')}`);
    }

    const medicalContextsWithIssues = contexts.filter(ctx => 
      this.config.medicalDataContexts.includes(ctx.contextName) && 
      (ctx.alerts.length > 0 || ctx.performance.averageRenderTime > 5)
    );
    if (medicalContextsWithIssues.length > 0) {
      recommendations.push(`Medical contexts need attention: ${medicalContextsWithIssues.map(ctx => ctx.contextName).join(', ')}`);
    }

    return recommendations;
  }

  private handlePerformanceEntries(list: PerformanceObserverEntryList): void {
    const entries = list.getEntries();
    
    for (const entry of entries) {
      if (entry.entryType === 'measure' && entry.name.includes('context')) {
        // Handle context-related performance measures
        console.log('📊 Context Performance:', entry.name, entry.duration);
      }
    }
  }
}

// Create singleton instance
const contextDebugger = new ContextDebugger();

// React hook for using context debugger
export const useContextDebugger = (
  contextName: string,
  currentState: any,
  options: {
    isMedicalContext?: boolean;
    sensitiveFields?: string[];
  } = {}
) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());

  useEffect(() => {
    // Register context on first render
    if (renderCount.current === 0) {
      contextDebugger.registerContext(contextName, currentState, options);
    }

    // Track render
    const renderStart = performance.now();
    renderCount.current++;
    
    const renderDuration = performance.now() - renderStart;
    contextDebugger.trackRender(contextName, renderDuration, 'state-change');

    // Track state change
    if (renderCount.current > 1) {
      contextDebugger.trackStateChange(
        contextName, 
        currentState, 
        'render-triggered',
        options.sensitiveFields
      );
    }

    lastRenderTime.current = Date.now();
  });

  return {
    trackSubscriber: (subscriberName: string, action: 'add' | 'remove') => 
      contextDebugger.trackSubscriber(contextName, subscriberName, action),
    getDebugInfo: () => contextDebugger.getContextDebugInfo(contextName),
    renderCount: renderCount.current
  };
};

// React component for displaying debug information
export const ContextDebugPanel: React.FC = memo(() => {
  const [debugData, setDebugData] = useState<ContextDebugInfo[]>([]);
  const [summary, setSummary] = useState<any>(null);

  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    const updateDebugData = () => {
      setDebugData(contextDebugger.getAllContextDebugInfo());
      setSummary(contextDebugger.getPerformanceSummary());
    };

    updateDebugData();
    const interval = setInterval(updateDebugData, 1000);

    return () => clearInterval(interval);
  }, []);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      width: 400,
      maxHeight: 600,
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      color: 'white',
      padding: 16,
      borderRadius: 8,
      fontSize: 12,
      fontFamily: 'monospace',
      overflow: 'auto',
      zIndex: 9999
    }}>
      <h3>🔍 Context Debugger</h3>
      
      {summary && (
        <div style={{ marginBottom: 16 }}>
          <div>Total Contexts: {summary.totalContexts}</div>
          <div>Medical Contexts: {summary.medicalContexts}</div>
          <div>Avg Render Time: {summary.averageRenderTime.toFixed(2)}ms</div>
          <div>Total Renders: {summary.totalRenders}</div>
          <div>Active Alerts: {summary.activeAlerts}</div>
        </div>
      )}

      {debugData.map(context => (
        <div key={context.contextName} style={{ 
          marginBottom: 12, 
          padding: 8, 
          border: '1px solid #333',
          borderRadius: 4
        }}>
          <div style={{ fontWeight: 'bold', color: '#4CAF50' }}>
            {context.contextName}
          </div>
          <div>Renders: {context.performance.renderCount}</div>
          <div>Avg Time: {context.performance.averageRenderTime.toFixed(2)}ms</div>
          <div>Subscribers: {context.performance.subscriberCount}</div>
          <div>State Changes: {context.performance.stateChanges}</div>
          {context.alerts.length > 0 && (
            <div style={{ color: '#f44336' }}>
              Alerts: {context.alerts.length}
            </div>
          )}
        </div>
      ))}
    </div>
  );
});

ContextDebugPanel.displayName = 'ContextDebugPanel';

export default contextDebugger;
export { ContextDebugger, type ContextDebugInfo, type ContextAlert };
