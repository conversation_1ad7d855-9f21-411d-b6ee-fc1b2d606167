/**
 * AUTH TYPES AND INTERFACES
 * 
 * Extracted from OptimizedAuthContext for better maintainability.
 * Contains all type definitions for authentication management.
 */

import type { User, UserProfile, AuthError, UserRole } from '../../types/auth';

export interface AuthState {
  user: User | null | undefined;
  profile: UserProfile | null | undefined;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: AuthError | null | undefined;
  lastActivity: number;
  sessionExpiry: number;
  permissions: string[];
  emergencyOverride: boolean;
}

export interface AuthActions {
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  clearError: () => void;
  setEmergencyOverride: (enabled: boolean) => void;
  checkPermission: (permission: string) => boolean;
  hasRole: (role: UserRole) => boolean;
}

export interface AuthMetrics {
  loginAttempts: number;
  lastLoginTime: number;
  sessionDuration: number;
  tokenRefreshCount: number;
  emergencyOverrideCount: number;
  permissionChecks: number;
}

export interface AuthContext {
  state: AuthState;
  actions: AuthActions;
  metrics: AuthMetrics;
  selectors: {
    getUser: () => User | null | undefined;
    getProfile: () => UserProfile | null | undefined;
    getPermissions: () => string[];
    isLoading: () => boolean;
    isAuthenticated: () => boolean;
    getError: () => AuthError | null;
    hasPermission: (permission: string) => boolean;
    hasRole: (role: UserRole) => boolean;
    getSessionTimeRemaining: () => number;
  };
}

export interface TokenInfo {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  tokenType: string;
}

export interface AuthConfig {
  tokenRefreshThreshold: number; // milliseconds before expiry to refresh
  sessionTimeout: number; // milliseconds
  maxLoginAttempts: number;
  lockoutDuration: number; // milliseconds
  enableEmergencyOverride: boolean;
  auditEnabled: boolean;
}

export const defaultAuthConfig: AuthConfig = {
  tokenRefreshThreshold: 5 * 60 * 1000, // 5 minutes
  sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours
  maxLoginAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15 minutes
  enableEmergencyOverride: true,
  auditEnabled: true
};

export const initialAuthState: AuthState = {
  user: null,
  profile: null,
  isLoading: false,
  isAuthenticated: false,
  error: null,
  lastActivity: Date.now(),
  sessionExpiry: 0,
  permissions: [],
  emergencyOverride: false
};

export const initialAuthMetrics: AuthMetrics = {
  loginAttempts: 0,
  lastLoginTime: 0,
  sessionDuration: 0,
  tokenRefreshCount: 0,
  emergencyOverrideCount: 0,
  permissionChecks: 0
};
