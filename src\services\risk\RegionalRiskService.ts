/**
 * REGIONAL RISK SERVICE
 * 
 * Handles regional disease patterns, endemic risks, environmental factors,
 * and seasonal variations specific to African healthcare contexts.
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type {
  PatientDemographics,
  EnvironmentalFactors,
  RegionalRiskFactor,
  RegionalRiskModel
} from '../../types/riskAssessment';

export class RegionalRiskService {
  private supabase: SupabaseClient;

  constructor() {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing for regional risk service');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  /**
   * Load regional risk models for a specific country
   */
  async loadRegionalRiskModels(country: string): Promise<RegionalRiskModel> {
    try {
      console.log(`🌍 Loading regional risk models for ${country}...`);

      const { data: models, error } = await this.supabase
        .from('regional_risk_models')
        .select('*')
        .eq('country', country.toLowerCase())
        .single();

      if (error) {
        console.warn(`⚠️ No specific models found for ${country}, using defaults`);
        return this.getDefaultRiskModels(country);
      }

      const modelData = models || this.getDefaultRiskModels(country);
      console.log(`✅ Regional risk models loaded for ${country}`);
      return modelData;

    } catch (error) {
      console.error('❌ Error loading regional risk models:', error);
      return this.getDefaultRiskModels(country);
    }
  }

  /**
   * Assess regional risk factors including endemic diseases and environmental risks
   */
  async assessRegionalRiskFactors(
    demographics: PatientDemographics,
    environmentalFactors?: EnvironmentalFactors
  ): Promise<RegionalRiskFactor[]> {
    const regionalFactors: RegionalRiskFactor[] = [];

    // Endemic disease risks
    const endemicRisks = this.getEndemicDiseaseRisks(demographics.country, demographics.region);
    regionalFactors.push(...endemicRisks);

    // Environmental risks
    if (environmentalFactors) {
      const environmentalRisks = this.getEnvironmentalRisks(environmentalFactors, demographics);
      regionalFactors.push(...environmentalRisks);

      // Seasonal risks
      const seasonalRisks = this.getSeasonalRisks(
        environmentalFactors.season,
        demographics.country
      );
      regionalFactors.push(...seasonalRisks);
    }

    return regionalFactors;
  }

  /**
   * Load emergency risk models for rapid assessment
   */
  async loadEmergencyRiskModels(country: string): Promise<RegionalRiskModel> {
    try {
      console.log(`🚨 Loading emergency risk models for ${country}...`);

      const { data: models, error } = await this.supabase
        .from('emergency_risk_models')
        .select('*')
        .eq('country', country.toLowerCase())
        .single();

      if (error) {
        console.warn(`⚠️ No emergency models found for ${country}, using defaults`);
        return this.getDefaultEmergencyModels(country);
      }

      return models || this.getDefaultEmergencyModels(country);

    } catch (error) {
      console.error('❌ Error loading emergency risk models:', error);
      return this.getDefaultEmergencyModels(country);
    }
  }

  /**
   * Get endemic disease risks for a specific region
   */
  private getEndemicDiseaseRisks(country: string, region: string): RegionalRiskFactor[] {
    const endemicDiseases: { [key: string]: RegionalRiskFactor[] } = {
      'nigeria': [
        {
          type: 'endemic_disease',
          factor: 'Malaria',
          riskScore: 60,
          prevalence: 27,
          seasonality: true,
          mitigation: ['Bed nets', 'Antimalarial prophylaxis', 'Vector control']
        },
        {
          type: 'endemic_disease',
          factor: 'Tuberculosis',
          riskScore: 45,
          prevalence: 219,
          seasonality: false,
          mitigation: ['Early detection', 'DOT therapy', 'Contact tracing']
        },
        {
          type: 'endemic_disease',
          factor: 'Meningitis',
          riskScore: 30,
          prevalence: 10,
          seasonality: true,
          mitigation: ['Vaccination', 'Early treatment', 'Isolation']
        }
      ],
      'kenya': [
        {
          type: 'endemic_disease',
          factor: 'Malaria',
          riskScore: 45,
          prevalence: 35,
          seasonality: true,
          mitigation: ['Bed nets', 'Indoor spraying', 'Case management']
        },
        {
          type: 'endemic_disease',
          factor: 'Tuberculosis',
          riskScore: 50,
          prevalence: 233,
          seasonality: false,
          mitigation: ['Active case finding', 'Treatment adherence', 'Nutrition support']
        },
        {
          type: 'endemic_disease',
          factor: 'Rift Valley Fever',
          riskScore: 25,
          prevalence: 5,
          seasonality: true,
          mitigation: ['Animal vaccination', 'Vector control', 'Surveillance']
        }
      ],
      'south_africa': [
        {
          type: 'endemic_disease',
          factor: 'Tuberculosis',
          riskScore: 70,
          prevalence: 520,
          seasonality: false,
          mitigation: ['HIV co-treatment', 'Drug resistance monitoring', 'Social support']
        },
        {
          type: 'endemic_disease',
          factor: 'HIV/AIDS',
          riskScore: 65,
          prevalence: 20.4,
          seasonality: false,
          mitigation: ['Antiretroviral therapy', 'Prevention programs', 'Testing']
        }
      ]
    };

    return endemicDiseases[country.toLowerCase()] || [];
  }

  /**
   * Get environmental risk factors
   */
  private getEnvironmentalRisks(
    environmentalFactors: EnvironmentalFactors,
    demographics: PatientDemographics
  ): RegionalRiskFactor[] {
    const risks: RegionalRiskFactor[] = [];

    // Air quality risks
    if (environmentalFactors.airQuality === 'poor' || environmentalFactors.airQuality === 'hazardous') {
      risks.push({
        type: 'environmental',
        factor: 'Poor Air Quality',
        riskScore: environmentalFactors.airQuality === 'hazardous' ? 40 : 25,
        prevalence: demographics.urbanRural === 'urban' ? 60 : 30,
        seasonality: true,
        mitigation: ['Indoor air filtration', 'Mask wearing', 'Reduced outdoor activity']
      });
    }

    // Water quality risks
    if (environmentalFactors.waterQuality === 'unsafe' || environmentalFactors.waterQuality === 'questionable') {
      risks.push({
        type: 'environmental',
        factor: 'Unsafe Water',
        riskScore: environmentalFactors.waterQuality === 'unsafe' ? 35 : 20,
        prevalence: demographics.urbanRural === 'rural' ? 70 : 25,
        seasonality: false,
        mitigation: ['Water purification', 'Boiling water', 'Safe storage']
      });
    }

    // Vector exposure risks
    if (environmentalFactors.vectorExposure) {
      risks.push({
        type: 'environmental',
        factor: 'Vector Exposure',
        riskScore: 30,
        prevalence: 50,
        seasonality: true,
        mitigation: ['Bed nets', 'Repellents', 'Environmental management']
      });
    }

    // Occupational hazards
    if (environmentalFactors.occupationalHazards?.length > 0) {
      risks.push({
        type: 'environmental',
        factor: 'Occupational Hazards',
        riskScore: 25,
        prevalence: 40,
        seasonality: false,
        mitigation: ['Personal protective equipment', 'Safety training', 'Health monitoring']
      });
    }

    return risks;
  }

  /**
   * Get seasonal risk factors
   */
  private getSeasonalRisks(season: string, country: string): RegionalRiskFactor[] {
    const seasonalRisks: { [key: string]: { [key: string]: RegionalRiskFactor[] } } = {
      'nigeria': {
        'wet': [
          {
            type: 'seasonal',
            factor: 'Malaria Peak Season',
            riskScore: 40,
            prevalence: 80,
            seasonality: true,
            mitigation: ['Increased bed net use', 'Prophylaxis', 'Vector control']
          },
          {
            type: 'seasonal',
            factor: 'Cholera Risk',
            riskScore: 25,
            prevalence: 15,
            seasonality: true,
            mitigation: ['Safe water', 'Sanitation', 'Hygiene']
          }
        ],
        'dry': [
          {
            type: 'seasonal',
            factor: 'Meningitis Season',
            riskScore: 35,
            prevalence: 25,
            seasonality: true,
            mitigation: ['Vaccination', 'Dust protection', 'Hydration']
          }
        ],
        'harmattan': [
          {
            type: 'seasonal',
            factor: 'Respiratory Infections',
            riskScore: 30,
            prevalence: 45,
            seasonality: true,
            mitigation: ['Mask wearing', 'Indoor air quality', 'Hydration']
          }
        ]
      },
      'kenya': {
        'wet': [
          {
            type: 'seasonal',
            factor: 'Malaria Transmission',
            riskScore: 35,
            prevalence: 60,
            seasonality: true,
            mitigation: ['Bed nets', 'Case management', 'Vector control']
          },
          {
            type: 'seasonal',
            factor: 'Rift Valley Fever',
            riskScore: 20,
            prevalence: 10,
            seasonality: true,
            mitigation: ['Animal vaccination', 'Vector control', 'Surveillance']
          }
        ],
        'dry': [
          {
            type: 'seasonal',
            factor: 'Drought-related Malnutrition',
            riskScore: 25,
            prevalence: 30,
            seasonality: true,
            mitigation: ['Nutrition support', 'Food security', 'Water access']
          }
        ]
      }
    };

    return seasonalRisks[country.toLowerCase()]?.[season] || [];
  }

  /**
   * Get default risk models when specific regional data is not available
   */
  private getDefaultRiskModels(country: string): RegionalRiskModel {
    return {
      country: country,
      models: {
        cardiovascular: { accuracy: 0.75, lastUpdated: '2024-01-01', prevalenceData: 25, riskFactors: ['age', 'gender', 'hypertension'] },
        diabetes: { accuracy: 0.80, lastUpdated: '2024-01-01', prevalenceData: 5, riskFactors: ['age', 'obesity', 'family_history'] },
        infectious: { accuracy: 0.70, lastUpdated: '2024-01-01', prevalenceData: 15, riskFactors: ['season', 'environment', 'immunity'] },
        respiratory: { accuracy: 0.65, lastUpdated: '2024-01-01', prevalenceData: 20, riskFactors: ['air_quality', 'smoking', 'occupation'] }
      },
      isDefault: true
    };
  }

  /**
   * Get default emergency risk models
   */
  private getDefaultEmergencyModels(country: string): RegionalRiskModel {
    return {
      country: country,
      models: {
        emergency_cardiovascular: { accuracy: 0.85, lastUpdated: '2024-01-01', prevalenceData: 15, riskFactors: ['chest_pain', 'age', 'risk_factors'] },
        emergency_infectious: { accuracy: 0.80, lastUpdated: '2024-01-01', prevalenceData: 25, riskFactors: ['fever', 'symptoms', 'season'] },
        emergency_respiratory: { accuracy: 0.75, lastUpdated: '2024-01-01', prevalenceData: 20, riskFactors: ['breathing', 'oxygen', 'history'] },
        emergency_neurological: { accuracy: 0.70, lastUpdated: '2024-01-01', prevalenceData: 10, riskFactors: ['consciousness', 'symptoms', 'age'] }
      },
      isDefault: true
    };
  }
}

export const regionalRiskService = new RegionalRiskService();
