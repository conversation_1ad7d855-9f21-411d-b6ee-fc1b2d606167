# PowerShell Script: Fix exactOptionalPropertyTypes Violations
# Target: ~250 errors across multiple files
# Impact: Very High (fixes most common error pattern)
# Risk: Medium (affects type definitions)

Write-Host "Starting exactOptionalPropertyTypes Error Fixes..." -ForegroundColor Green

# Files with known exactOptionalPropertyTypes errors
$targetFiles = @(
    "src/utils/auditLogger.ts",
    "src/utils/audioStorageService.ts", 
    "src/utils/standardErrorHandler.ts",
    "src/contexts/OptimizedAuthContext.tsx",
    "src/contexts/OptimizedMedicalDataContext.tsx",
    "src/services/AuthenticationService.ts",
    "src/services/rateLimitingService.ts",
    "src/middleware/rateLimitingMiddleware.ts"
)

$totalChanges = 0
$filesProcessed = 0

foreach ($file in $targetFiles) {
    if (Test-Path $file) {
        Write-Host "Processing: $file" -ForegroundColor Yellow
        
        # Read file content
        $content = Get-Content $file -Raw
        $originalContent = $content
        
        # Common exactOptionalPropertyTypes fixes
        # Fix 1: email property
        $content = $content -replace "email: string \| undefined", "email?: string"
        $content = $content -replace "(\s+)email: string;", "`$1email?: string;"
        
        # Fix 2: ip_address property  
        $content = $content -replace "ip_address: string \| undefined", "ip_address?: string"
        $content = $content -replace "(\s+)ip_address: string;", "`$1ip_address?: string;"
        
        # Fix 3: user_agent property
        $content = $content -replace "user_agent: string \| undefined", "user_agent?: string"
        $content = $content -replace "(\s+)user_agent: string;", "`$1user_agent?: string;"
        
        # Fix 4: session_id property
        $content = $content -replace "session_id: string \| undefined", "session_id?: string"
        $content = $content -replace "(\s+)session_id: string;", "`$1session_id?: string;"
        
        # Fix 5: content property
        $content = $content -replace "content: string \| undefined", "content?: string"
        $content = $content -replace "(\s+)content: string;", "`$1content?: string;"
        
        # Fix 6: userId property
        $content = $content -replace "userId: string \| undefined", "userId?: string"
        $content = $content -replace "(\s+)userId: string;", "`$1userId?: string;"
        
        # Fix 7: transcription property
        $content = $content -replace "transcription: string \| undefined", "transcription?: string"
        $content = $content -replace "(\s+)transcription: string;", "`$1transcription?: string;"
        
        # Fix 8: metadata property
        $content = $content -replace "metadata: Record<string, any> \| undefined", "metadata?: Record<string, any>"
        $content = $content -replace "(\s+)metadata: Record<string, any>;", "`$1metadata?: Record<string, any>;"
        
        # Fix 9: target property (for PerformanceMetric)
        $content = $content -replace "target: number \| undefined", "target?: number"
        $content = $content -replace "(\s+)target: number;", "`$1target?: number;"
        
        # Fix 10: Common interface property patterns
        $content = $content -replace "(\s+)(\w+): (\w+) \| undefined;", "`$1`$2?: `$3;"
        
        # Write back if changes were made
        if ($content -ne $originalContent) {
            Set-Content $file $content -Encoding UTF8
            $filesProcessed++
            
            # Estimate changes made
            $changeCount = ($originalContent.Split("`n").Count - $content.Split("`n").Count) + 
                          (($originalContent -split "\| undefined").Count - ($content -split "\| undefined").Count)
            $totalChanges += [Math]::Max($changeCount, 1)
            
            Write-Host "✅ Fixed exactOptionalPropertyTypes in $file" -ForegroundColor Green
        } else {
            Write-Host "⚠️  No changes needed in $file" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ File not found: $file" -ForegroundColor Red
    }
}

# Also fix interface definitions in type files
$typeFiles = Get-ChildItem -Path "src/types" -Include "*.ts" -Recurse

foreach ($file in $typeFiles) {
    Write-Host "Processing type file: $($file.Name)" -ForegroundColor Cyan
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Fix interface properties that should be optional
    $content = $content -replace "(\s+)(\w+): string \| undefined;", "`$1`$2?: string;"
    $content = $content -replace "(\s+)(\w+): number \| undefined;", "`$1`$2?: number;"
    $content = $content -replace "(\s+)(\w+): boolean \| undefined;", "`$1`$2?: boolean;"
    $content = $content -replace "(\s+)(\w+): Record<string, any> \| undefined;", "`$1`$2?: Record<string, any>;"
    
    if ($content -ne $originalContent) {
        Set-Content $file.FullName $content -Encoding UTF8
        $filesProcessed++
        $totalChanges += 5  # Estimate
        Write-Host "✅ Fixed type definitions in $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "`n📊 Summary:" -ForegroundColor Magenta
Write-Host "Files processed: $filesProcessed" -ForegroundColor White
Write-Host "Estimated errors fixed: $totalChanges" -ForegroundColor White
Write-Host "Target error reduction: ~100-150 out of 250 exactOptionalPropertyTypes errors" -ForegroundColor White

Write-Host "`n🔍 Next steps:" -ForegroundColor Magenta
Write-Host "1. Run 'npm run build' to verify error reduction" -ForegroundColor White
Write-Host "2. Check for any new type errors introduced" -ForegroundColor White
Write-Host "3. Proceed with readonly property fix script if successful" -ForegroundColor White

Write-Host "`nexactOptionalPropertyTypes Error Fix Script Completed!" -ForegroundColor Green
