/**
 * ADAPTIVE RATE LIMITING ENGINE
 * 
 * Handles dynamic rate limit adjustments based on system load and user behavior.
 * Extracted from rateLimitingService.ts for better maintainability.
 */

import type { UserRole } from '../../types/auth';
import type { 
  RateLimitConfig, 
  SystemLoadMetrics, 
  AdaptiveConfig, 
  RequestMetrics,
  RateLimitInfo 
} from './RateLimitConfig';
import { defaultAdaptiveConfig, roleMultipliers } from './RateLimitConfig';

export class AdaptiveRateLimiter {
  private requestMetrics = new Map<string, RequestMetrics>();
  private systemLoad: SystemLoadMetrics = {
    cpuUsage: 0,
    memoryUsage: 0,
    activeConnections: 0,
    emergencyRequestsActive: 0,
    lastUpdated: Date.now()
  };

  constructor(
    private config: RateLimitConfig,
    private adaptiveConfig: AdaptiveConfig = defaultAdaptiveConfig
  ) {}

  /**
   * Check if request should be rate limited with adaptive adjustments
   */
  checkRateLimit(key: string, userRole?: UserRole): RateLimitInfo {
    const now = Date.now();
    const windowStart = Math.floor(now / this.config.windowMs) * this.config.windowMs;
    
    // Get or create request metrics
    let metrics = this.requestMetrics.get(key);
    if (!metrics || metrics.windowStart !== windowStart) {
      const newMetrics: RequestMetrics = {
        count: 0,
        windowStart,
        emergencyCount: 0,
        lastRequest: now,
        userRole: userRole,
        isBlocked: false
      };
      metrics = newMetrics;
      this.requestMetrics.set(key, metrics);
    }

    // Calculate adaptive limit
    const adaptiveLimit = this.calculateAdaptiveLimit(userRole);
    
    // Check if limit exceeded
    if (metrics.count >= adaptiveLimit) {
      metrics.isBlocked = true;
      return {
        limit: adaptiveLimit,
        remaining: 0,
        resetTime: windowStart + this.config.windowMs,
        retryAfter: Math.ceil((windowStart + this.config.windowMs - now) / 1000)
      };
    }

    // Increment count
    metrics.count++;
    metrics.lastRequest = now;

    return {
      limit: adaptiveLimit,
      remaining: adaptiveLimit - metrics.count,
      resetTime: windowStart + this.config.windowMs,
      retryAfter: 0
    };
  }

  /**
   * Calculate adaptive rate limit based on system load and user role
   */
  private calculateAdaptiveLimit(userRole?: UserRole): number {
    let baseLimit = this.config.maxRequests;

    // Apply role-based multiplier
    if (userRole && roleMultipliers[userRole]) {
      baseLimit *= roleMultipliers[userRole];
    }

    // Apply adaptive multiplier based on system load
    if (this.adaptiveConfig.enableAdaptive) {
      const loadMultiplier = this.getLoadMultiplier();
      baseLimit *= loadMultiplier;
    }

    return Math.max(1, Math.floor(baseLimit));
  }

  /**
   * Get load-based multiplier for rate limits
   */
  private getLoadMultiplier(): number {
    const avgLoad = (this.systemLoad.cpuUsage + this.systemLoad.memoryUsage) / 2;
    
    if (avgLoad >= this.adaptiveConfig.loadThresholds.critical) {
      return this.adaptiveConfig.adaptiveMultipliers.critical;
    } else if (avgLoad >= this.adaptiveConfig.loadThresholds.high) {
      return this.adaptiveConfig.adaptiveMultipliers.high;
    } else if (avgLoad >= this.adaptiveConfig.loadThresholds.medium) {
      return this.adaptiveConfig.adaptiveMultipliers.medium;
    } else {
      return this.adaptiveConfig.adaptiveMultipliers.low;
    }
  }

  /**
   * Update system load metrics
   */
  updateSystemLoad(metrics: Partial<SystemLoadMetrics>): void {
    this.systemLoad = {
      ...this.systemLoad,
      ...metrics,
      lastUpdated: Date.now()
    };
  }

  /**
   * Get current system load
   */
  getSystemLoad(): SystemLoadMetrics {
    return { ...this.systemLoad };
  }

  /**
   * Get request metrics for a key
   */
  getRequestMetrics(key: string): RequestMetrics | undefined {
    return this.requestMetrics.get(key);
  }

  /**
   * Get all active request metrics
   */
  getAllRequestMetrics(): Map<string, RequestMetrics> {
    return new Map(this.requestMetrics);
  }

  /**
   * Check if a key is currently blocked
   */
  isBlocked(key: string): boolean {
    const metrics = this.requestMetrics.get(key);
    return metrics?.isBlocked || false;
  }

  /**
   * Manually block a key (for security purposes)
   */
  blockKey(key: string, reason: string): void {
    const metrics = this.requestMetrics.get(key) || {
      count: this.config.maxRequests,
      windowStart: Date.now(),
      emergencyCount: 0,
      lastRequest: Date.now(),
      isBlocked: true
    };
    
    metrics.isBlocked = true;
    this.requestMetrics.set(key, metrics);
  }

  /**
   * Unblock a key
   */
  unblockKey(key: string): void {
    const metrics = this.requestMetrics.get(key);
    if (metrics) {
      metrics.isBlocked = false;
      this.requestMetrics.set(key, metrics);
    }
  }

  /**
   * Clean up old metrics to prevent memory leaks
   */
  cleanup(): void {
    const now = Date.now();
    const cutoff = now - (this.config.windowMs * 2);

    for (const [key, metrics] of this.requestMetrics.entries()) {
      if (metrics.windowStart < cutoff) {
        this.requestMetrics.delete(key);
      }
    }
  }

  /**
   * Get statistics for monitoring
   */
  getStatistics() {
    const totalRequests = Array.from(this.requestMetrics.values())
      .reduce((sum, metrics) => sum + metrics.count, 0);
    
    const blockedKeys = Array.from(this.requestMetrics.values())
      .filter(metrics => metrics.isBlocked).length;

    const emergencyRequests = Array.from(this.requestMetrics.values())
      .reduce((sum, metrics) => sum + metrics.emergencyCount, 0);

    return {
      totalRequests,
      activeKeys: this.requestMetrics.size,
      blockedKeys,
      emergencyRequests,
      systemLoad: this.systemLoad,
      adaptiveMultiplier: this.getLoadMultiplier()
    };
  }

  /**
   * Reset all metrics (for testing or emergency situations)
   */
  reset(): void {
    this.requestMetrics.clear();
    this.systemLoad = {
      cpuUsage: 0,
      memoryUsage: 0,
      activeConnections: 0,
      emergencyRequestsActive: 0,
      lastUpdated: Date.now()
    };
  }
}
