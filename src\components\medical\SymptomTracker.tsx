/**
 * Symptom Tracker Component
 * Track and log patient symptoms
 */

import React from 'react';

interface SymptomTrackerProps {
  className?: string;
}

const SymptomTracker: React.FC<SymptomTrackerProps> = ({ className = '' }) => {
  return (
    <div className={`p-6 bg-white ${className}`}>
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-800 mb-2">Symptom Tracker</h2>
        <p className="text-gray-600">Log and monitor your symptoms</p>
      </div>
      
      <div className="space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h3 className="font-semibold text-blue-800 mb-2">Current Symptoms</h3>
          <p className="text-blue-700 text-sm">Track your current symptoms and severity</p>
          <button className="mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
            Log Symptom
          </button>
        </div>
        
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <h3 className="font-semibold text-green-800 mb-2">Symptom History</h3>
          <p className="text-green-700 text-sm">View your symptom patterns and trends</p>
          <button className="mt-3 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
            View History
          </button>
        </div>
      </div>
      
      <div className="mt-6 bg-yellow-50 p-3 rounded border border-yellow-200">
        <p className="text-yellow-800 text-sm">
          ⚠️ This is a placeholder component. Full symptom tracking functionality will be implemented.
        </p>
      </div>
    </div>
  );
};

export default SymptomTracker;
