# PowerShell Script: Fix Readonly Property Assignment Errors
# Target: ~150 errors across multiple files
# Impact: High (fixes common readonly violations)
# Risk: Medium (changes assignment patterns)

Write-Host "Starting Readonly Property Assignment Error Fixes..." -ForegroundColor Green

# Files with known readonly property assignment errors
$targetFiles = @(
    "src/utils/audioBackupService.ts",
    "src/utils/cacheAnalyticsService.ts",
    "src/utils/clientRateLimiting.ts"
)

$totalChanges = 0
$filesProcessed = 0

foreach ($file in $targetFiles) {
    if (Test-Path $file) {
        Write-Host "Processing: $file" -ForegroundColor Yellow
        
        # Read file content
        $content = Get-Content $file -Raw
        $originalContent = $content
        
        # Fix 1: result.errors assignments (most common pattern)
        $content = $content -replace "(\s+)result\.errors = \[\.\.\.result\.errors, ([^\]]+)\];", 
            "`$1result = { ...result, errors: [...result.errors, `$2] };"
        
        # Fix 2: result.warnings assignments
        $content = $content -replace "(\s+)result\.warnings = \[\.\.\.result\.warnings, ([^\]]+)\];",
            "`$1result = { ...result, warnings: [...result.warnings, `$2] };"
        
        # Fix 3: suggestions.cacheSize assignments (cacheAnalyticsService.ts specific)
        $content = $content -replace "(\s+)suggestions\.cacheSize = \[\.\.\.suggestions\.cacheSize, ([^\]]+)\];",
            "`$1suggestions = { ...suggestions, cacheSize: [...suggestions.cacheSize, `$2] };"
        
        # Fix 4: suggestions.evictionPolicy assignments
        $content = $content -replace "(\s+)suggestions\.evictionPolicy = \[\.\.\.suggestions\.evictionPolicy, ([^\]]+)\];",
            "`$1suggestions = { ...suggestions, evictionPolicy: [...suggestions.evictionPolicy, `$2] };"
        
        # Fix 5: suggestions.compressionSettings assignments
        $content = $content -replace "(\s+)suggestions\.compressionSettings = \[\.\.\.suggestions\.compressionSettings, ([^\]]+)\];",
            "`$1suggestions = { ...suggestions, compressionSettings: [...suggestions.compressionSettings, `$2] };"
        
        # Fix 6: suggestions.emergencyDataHandling assignments
        $content = $content -replace "(\s+)suggestions\.emergencyDataHandling = \[\.\.\.suggestions\.emergencyDataHandling, ([^\]]+)\];",
            "`$1suggestions = { ...suggestions, emergencyDataHandling: [...suggestions.emergencyDataHandling, `$2] };"
        
        # Fix 7: request assignments (clientRateLimiting.ts specific)
        $content = $content -replace "(\s+)request = \{ \.\.\.request, retryCount: request\.retryCount \+ 1 \};",
            "`$1const updatedRequest = { ...request, retryCount: request.retryCount + 1 }; request = updatedRequest;"
        
        # Write back if changes were made
        if ($content -ne $originalContent) {
            Set-Content $file $content -Encoding UTF8
            $filesProcessed++
            
            # Count actual changes made
            $errorChanges = ($originalContent -split "result\.errors =").Count - 1
            $warningChanges = ($originalContent -split "result\.warnings =").Count - 1
            $suggestionChanges = ($originalContent -split "suggestions\.\w+ =").Count - 1
            $requestChanges = ($originalContent -split "request =").Count - 1
            
            $fileChanges = $errorChanges + $warningChanges + $suggestionChanges + $requestChanges
            $totalChanges += $fileChanges
            
            Write-Host "✅ Fixed $fileChanges readonly assignments in $file" -ForegroundColor Green
        } else {
            Write-Host "⚠️  No readonly assignment patterns found in $file" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ File not found: $file" -ForegroundColor Red
    }
}

# Search for additional files with readonly assignment patterns
Write-Host "`nSearching for additional files with readonly assignment patterns..." -ForegroundColor Cyan

$allTsFiles = Get-ChildItem -Path "src" -Include "*.ts","*.tsx" -Recurse
$additionalFiles = @()

foreach ($file in $allTsFiles) {
    $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
    if ($content -match "\.errors = \[\.\.\..*\.errors," -or 
        $content -match "\.warnings = \[\.\.\..*\.warnings," -or
        $content -match "= \{ \.\.\..*retryCount.*\+ 1") {
        $additionalFiles += $file.FullName
    }
}

if ($additionalFiles.Count -gt 0) {
    Write-Host "Found additional files with readonly assignment patterns:" -ForegroundColor Yellow
    foreach ($file in $additionalFiles) {
        if ($targetFiles -notcontains $file) {
            Write-Host "  - $file" -ForegroundColor White
            
            # Apply same fixes to additional files
            $content = Get-Content $file -Raw
            $originalContent = $content
            
            $content = $content -replace "(\s+)result\.errors = \[\.\.\.result\.errors, ([^\]]+)\];", 
                "`$1result = { ...result, errors: [...result.errors, `$2] };"
            $content = $content -replace "(\s+)result\.warnings = \[\.\.\.result\.warnings, ([^\]]+)\];",
                "`$1result = { ...result, warnings: [...result.warnings, `$2] };"
            
            if ($content -ne $originalContent) {
                Set-Content $file $content -Encoding UTF8
                $filesProcessed++
                $totalChanges += 2
                Write-Host "    ✅ Fixed readonly assignments" -ForegroundColor Green
            }
        }
    }
}

Write-Host "`n📊 Summary:" -ForegroundColor Magenta
Write-Host "Files processed: $filesProcessed" -ForegroundColor White
Write-Host "Readonly assignments fixed: $totalChanges" -ForegroundColor White
Write-Host "Target error reduction: ~50-80 out of 150 readonly assignment errors" -ForegroundColor White

Write-Host "`n🔍 Next steps:" -ForegroundColor Magenta
Write-Host "1. Run 'npm run build' to verify error reduction" -ForegroundColor White
Write-Host "2. Check for any logic errors in assignment changes" -ForegroundColor White
Write-Host "3. Test affected functionality to ensure correctness" -ForegroundColor White
Write-Host "4. Proceed with array mutation fix script if successful" -ForegroundColor White

Write-Host "`nReadonly Property Assignment Error Fix Script Completed!" -ForegroundColor Green
