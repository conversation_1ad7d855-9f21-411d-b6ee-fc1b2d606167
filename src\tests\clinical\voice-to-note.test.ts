/**
 * VOICE-TO-NOTE CONVERSION TESTS
 * 
 * Focused tests for voice transcription to clinical note conversion functionality
 * including basic conversion, error handling, and edge cases.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { clinicalDocumentationService } from '../../services/ClinicalDocumentationService';

describe('Voice-to-Note Conversion', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    clinicalDocumentationService.clearCaches();
  });

  describe('generateVoiceToNote - Basic Functionality', () => {
    it('should generate clinical note from voice transcription', async () => {
      const request = {
        audioTranscription: 'Pat<PERSON> is a 45-year-old female presenting with headache and fever for 3 days. She has a history of hypertension. Blood pressure is 150/90, temperature 38.5 degrees Celsius.',
        sessionId: 'session-123',
        patientId: 'patient-123',
        providerId: 'provider-456',
        noteType: 'soap',
        culturalContext: {
          cultureCode: 'akan',
          languagePreference: 'en',
          familyInvolvementLevel: 'high'
        }
      };

      const result = await clinicalDocumentationService.generateVoiceToNote(request);

      expect(result.structuredNote).toBeDefined();
      expect(result.structuredNote.chiefComplaint).toContain('headache');
      expect(result.structuredNote.chiefComplaint).toContain('fever');
      expect(result.structuredNote.physicalExamination.vitalSigns.bloodPressure).toBeDefined();
      expect(result.structuredNote.physicalExamination.vitalSigns.temperature).toBeDefined();
      expect(result.qualityAssessment).toBeDefined();
      expect(result.confidence).toBeGreaterThan(0);
      expect(result.culturalAdaptations).toBeDefined();
      expect(result.suggestedCodes).toBeDefined();
    });

    it('should apply cultural adaptations based on context', async () => {
      const request = {
        audioTranscription: 'Patient needs medication and follow-up care.',
        sessionId: 'session-123',
        patientId: 'patient-123',
        providerId: 'provider-456',
        noteType: 'soap',
        culturalContext: {
          cultureCode: 'yoruba',
          languagePreference: 'en',
          familyInvolvementLevel: 'high',
          traditionalMedicineOpenness: 4
        }
      };

      const result = await clinicalDocumentationService.generateVoiceToNote(request);

      expect(result.culturalAdaptations).toBeDefined();
      expect(result.culturalAdaptations.length).toBeGreaterThan(0);
      
      const familyAdaptation = result.culturalAdaptations.find(
        adaptation => adaptation.aspect === 'family_involvement'
      );
      expect(familyAdaptation).toBeDefined();
    });

    it('should generate appropriate ICD-10 codes', async () => {
      const request = {
        audioTranscription: 'Patient diagnosed with hypertension and diabetes.',
        sessionId: 'session-123',
        patientId: 'patient-123',
        providerId: 'provider-456',
        noteType: 'soap'
      };

      const result = await clinicalDocumentationService.generateVoiceToNote(request);

      expect(result.suggestedCodes).toBeDefined();
      expect(result.suggestedCodes.length).toBeGreaterThan(0);

      const hypertensionCode = result.suggestedCodes.find(
        code => code.code === 'I10'
      );
      expect(hypertensionCode).toBeDefined();
    });

    it('should assess clinical note quality', async () => {
      const request = {
        audioTranscription: 'Patient is a 45-year-old female with chief complaint of headache. History of present illness includes 3 days of severe headache with associated nausea. Past medical history significant for hypertension. Physical examination reveals blood pressure 150/90, temperature 37.2C. Assessment is tension headache possibly related to hypertension. Plan includes blood pressure medication adjustment and follow-up in 1 week.',
        sessionId: 'session-123',
        patientId: 'patient-123',
        providerId: 'provider-456',
        noteType: 'soap'
      };

      const result = await clinicalDocumentationService.generateVoiceToNote(request);

      expect(result.success).toBe(true);
      expect(result.qualityMetrics).toBeDefined();
      expect(result.qualityMetrics.completeness).toBeGreaterThan(70);
      expect(result.qualityMetrics.accuracy).toBeGreaterThan(70);
      expect(result.qualityMetrics.clarity).toBeGreaterThan(70);
      expect(result.qualityMetrics.culturalSensitivity).toBeGreaterThan(70);
      expect(result.qualityMetrics.complianceScore).toBeGreaterThan(70);
      expect(result.qualityMetrics.improvementSuggestions).toBeDefined();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty transcription gracefully', async () => {
      const request = {
        audioTranscription: '',
        sessionId: 'session-123',
        patientId: 'patient-123',
        providerId: 'provider-456',
        noteType: 'soap'
      };

      await expect(
        clinicalDocumentationService.generateVoiceToNote(request)
      ).rejects.toThrow('Audio transcription cannot be empty');
    });

    it('should handle malformed transcription data', async () => {
      const request = {
        audioTranscription: null as any,
        sessionId: 'session-123',
        patientId: 'patient-123',
        providerId: 'provider-456',
        noteType: 'soap'
      };

      await expect(
        clinicalDocumentationService.generateVoiceToNote(request)
      ).rejects.toThrow();
    });

    it('should handle missing patient ID', async () => {
      const request = {
        audioTranscription: 'Patient has headache',
        sessionId: 'session-123',
        patientId: '',
        providerId: 'provider-456',
        noteType: 'soap'
      };

      await expect(
        clinicalDocumentationService.generateVoiceToNote(request)
      ).rejects.toThrow('Patient ID is required');
    });

    it('should handle missing provider ID', async () => {
      const request = {
        audioTranscription: 'Patient has headache',
        sessionId: 'session-123',
        patientId: 'patient-123',
        providerId: '',
        noteType: 'soap'
      };

      await expect(
        clinicalDocumentationService.generateVoiceToNote(request)
      ).rejects.toThrow('Provider ID is required');
    });

    it('should handle unsupported note type', async () => {
      const request = {
        audioTranscription: 'Patient has headache',
        sessionId: 'session-123',
        patientId: 'patient-123',
        providerId: 'provider-456',
        noteType: 'unsupported_type'
      };

      const result = await clinicalDocumentationService.generateVoiceToNote(request);
      
      // Should default to SOAP format
      expect(result.success).toBe(true);
      expect(result.clinicalNote).toBeDefined();
    });
  });

  describe('Performance and Caching', () => {
    it('should cache template results for performance', async () => {
      const request = {
        audioTranscription: 'Patient has headache',
        sessionId: 'session-123',
        patientId: 'patient-123',
        providerId: 'provider-456',
        noteType: 'soap'
      };

      // First call
      const startTime1 = Date.now();
      const result1 = await clinicalDocumentationService.generateVoiceToNote(request);
      const time1 = Date.now() - startTime1;

      // Second call (should be faster due to caching)
      const startTime2 = Date.now();
      const result2 = await clinicalDocumentationService.generateVoiceToNote(request);
      const time2 = Date.now() - startTime2;

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      // Second call should be faster (cached)
      expect(time2).toBeLessThanOrEqual(time1);
    });

    it('should clear caches when requested', () => {
      // This test verifies the cache clearing functionality
      expect(() => {
        clinicalDocumentationService.clearCaches();
      }).not.toThrow();
    });
  });
});
