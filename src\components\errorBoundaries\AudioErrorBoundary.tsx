/**
 * AUDIO-SPECIFIC ERROR BOUNDARY
 * 
 * Dedicated error boundary for audio processing components with:
 * - Recovery mechanisms for audio recording failures
 * - Fallback UI components for consultation continuity
 * - Integration with medical error boundary system
 * - HIPAA-compliant error logging
 * - Patient safety prioritization
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, Mic, Mic<PERSON>ff, RefreshCw, Phone, AlertCircle } from 'lucide-react';
// Note: Using basic button elements instead of custom Button component
// import Button from '../ui/Button';
// import Icon from '../ui/Icon';

// Types for audio-specific errors
interface AudioError extends Error {
  code?: string;
  constraint?: string;
  audioContext?: {
    deviceId?: string;
    sampleRate?: number;
    channelCount?: number;
    state?: string;
  };
  severity?: 'low' | 'medium' | 'high' | 'critical';
  recoverable?: boolean;
  patientSafetyImpact?: boolean;
}

interface AudioErrorBoundaryState {
  hasError: boolean;
  error: AudioError | null;
  errorInfo: ErrorInfo | null;
  recoveryAttempts: number;
  isRecovering: boolean;
  fallbackMode: 'none' | 'text_only' | 'emergency_mode';
  lastErrorTime: number;
  sessionId?: string;
  patientId?: string;
}

interface AudioErrorBoundaryProps {
  children: ReactNode;
  sessionId?: string;
  patientId?: string;
  onError?: (error: AudioError, errorInfo: ErrorInfo) => void;
  onRecovery?: (successful: boolean) => void;
  onEmergencyStop?: (reason: string) => void;
  maxRecoveryAttempts?: number;
  enableFallbackMode?: boolean;
  criticalErrorThreshold?: number;
}

class AudioErrorBoundary extends Component<AudioErrorBoundaryProps, AudioErrorBoundaryState> {
  private recoveryTimer: NodeJS.Timeout | null = null;
  private errorLogTimer: NodeJS.Timeout | null = null;

  constructor(props: AudioErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      recoveryAttempts: 0,
      isRecovering: false,
      fallbackMode: 'none',
      lastErrorTime: 0,
      sessionId: props.sessionId,
      patientId: props.patientId
    };
  }

  static getDerivedStateFromError(error: Error): Partial<AudioErrorBoundaryState> {
    // Classify the audio error
    const audioError = error as AudioError;
    const severity = AudioErrorBoundary.classifyAudioError(audioError);
    const recoverable = AudioErrorBoundary.isRecoverableError(audioError);
    
    return {
      hasError: true,
      error: {
        ...audioError,
        severity,
        recoverable,
        patientSafetyImpact: severity === 'critical' || severity === 'high'
      },
      lastErrorTime: Date.now()
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const audioError = error as AudioError;
    
    // Log error immediately for audit trail
    this.logAudioError(audioError, errorInfo);
    
    // Notify parent component
    this.props.onError?.(audioError, errorInfo);
    
    // Set error info in state
    this.setState({ errorInfo });
    
    // Attempt automatic recovery for recoverable errors
    if (audioError.recoverable && this.state.recoveryAttempts < (this.props.maxRecoveryAttempts || 3)) {
      this.attemptRecovery();
    }
    
    // Trigger emergency protocols for critical errors
    if (audioError.severity === 'critical' && audioError.patientSafetyImpact) {
      this.handleCriticalAudioError(audioError);
    }
  }

  /**
   * Classify audio error severity based on error type and context
   */
  static classifyAudioError(error: AudioError): 'low' | 'medium' | 'high' | 'critical' {
    const errorCode = error.code || error.name;
    
    // Critical errors that impact patient safety
    if (errorCode.includes('NotAllowedError') || 
        errorCode.includes('SecurityError') ||
        error.message.includes('emergency') ||
        error.message.includes('critical')) {
      return 'critical';
    }
    
    // High priority errors that affect consultation quality
    if (errorCode.includes('NotFoundError') ||
        errorCode.includes('OverconstrainedError') ||
        errorCode.includes('NetworkError') ||
        error.message.includes('recording') ||
        error.message.includes('microphone')) {
      return 'high';
    }
    
    // Medium priority errors that can be recovered
    if (errorCode.includes('AbortError') ||
        errorCode.includes('TimeoutError') ||
        error.message.includes('processing') ||
        error.message.includes('codec')) {
      return 'medium';
    }
    
    // Low priority errors
    return 'low';
  }

  /**
   * Determine if an audio error is recoverable
   */
  static isRecoverableError(error: AudioError): boolean {
    const nonRecoverableErrors = [
      'NotAllowedError',    // Permission denied
      'SecurityError',      // Security violation
      'NotSupportedError'   // Feature not supported
    ];
    
    return !nonRecoverableErrors.some(code => 
      error.code?.includes(code) || error.name?.includes(code)
    );
  }

  /**
   * Attempt automatic recovery from audio errors
   */
  attemptRecovery = async () => {
    if (this.state.isRecovering) return;
    
    this.setState({ 
      isRecovering: true, 
      recoveryAttempts: this.state.recoveryAttempts + 1 
    });
    
    try {
      console.log(`🔄 Attempting audio recovery (attempt ${this.state.recoveryAttempts + 1})`);
      
      // Wait before recovery attempt
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Attempt to reinitialize audio context
      await this.reinitializeAudioContext();
      
      // Clear error state if recovery successful
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        isRecovering: false,
        fallbackMode: 'none'
      });
      
      // Log successful recovery
      await this.logAudioRecovery(true);
      this.props.onRecovery?.(true);
      
      console.log('✅ Audio recovery successful');
      
    } catch (recoveryError) {
      console.error('❌ Audio recovery failed:', recoveryError);
      
      // Log failed recovery
      await this.logAudioRecovery(false);
      this.props.onRecovery?.(false);
      
      // Enable fallback mode if recovery fails
      if (this.props.enableFallbackMode) {
        this.enableFallbackMode();
      }
      
      this.setState({ isRecovering: false });
    }
  };

  /**
   * Reinitialize audio context and media devices
   */
  reinitializeAudioContext = async (): Promise<void> => {
    try {
      // Check if audio context exists and close it
      if (window.AudioContext || (window as any).webkitAudioContext) {
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        const audioContext = new AudioContextClass();
        
        if (audioContext.state === 'suspended') {
          await audioContext.resume();
        }
        
        // Test audio context functionality
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        gainNode.gain.value = 0; // Silent test
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.1);
        
        await audioContext.close();
      }
      
      // Test microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      });
      
      // Stop the test stream
      stream.getTracks().forEach(track => track.stop());
      
    } catch (error) {
      throw new Error(`Audio context reinitialization failed: ${error.message}`);
    }
  };

  /**
   * Enable fallback mode for consultation continuity
   */
  enableFallbackMode = () => {
    const fallbackMode = this.state.error?.severity === 'critical' ? 'emergency_mode' : 'text_only';
    
    this.setState({ 
      fallbackMode,
      isRecovering: false 
    });
    
    console.log(`🔄 Enabled fallback mode: ${fallbackMode}`);
  };

  /**
   * Handle critical audio errors that impact patient safety
   */
  handleCriticalAudioError = async (error: AudioError) => {
    try {
      console.log('🚨 Critical audio error detected, triggering emergency protocols');
      
      // Log critical error immediately
      await this.logCriticalAudioError(error);
      
      // Trigger emergency stop if callback provided
      this.props.onEmergencyStop?.('audio_critical_error');
      
      // Enable emergency mode
      this.setState({ fallbackMode: 'emergency_mode' });
      
    } catch (emergencyError) {
      console.error('Failed to handle critical audio error:', emergencyError);
    }
  };

  /**
   * Log audio error for audit trail
   */
  logAudioError = async (error: AudioError, errorInfo: ErrorInfo) => {
    try {
      const { default: auditLogger } = await import('../../utils/auditLogger');
      
      await auditLogger.logSecurityEvent('audio_error', error.severity || 'medium', {
        error_type: 'audio_processing_error',
        error_code: error.code || error.name,
        error_message: error.message,
        session_id: this.state.sessionId,
        patient_id: this.state.patientId,
        component_stack: errorInfo.componentStack,
        audio_context: error.audioContext,
        recoverable: error.recoverable,
        patient_safety_impact: error.patientSafetyImpact,
        timestamp: new Date().toISOString()
      });
    } catch (logError) {
      console.error('Failed to log audio error:', logError);
    }
  };

  /**
   * Log audio recovery attempt
   */
  logAudioRecovery = async (successful: boolean) => {
    try {
      const { default: auditLogger } = await import('../../utils/auditLogger');
      
      await auditLogger.logSecurityEvent('audio_recovery', successful ? 'low' : 'medium', {
        event_type: 'audio_recovery_attempt',
        successful,
        recovery_attempt: this.state.recoveryAttempts,
        session_id: this.state.sessionId,
        patient_id: this.state.patientId,
        timestamp: new Date().toISOString()
      });
    } catch (logError) {
      console.error('Failed to log audio recovery:', logError);
    }
  };

  /**
   * Log critical audio error
   */
  logCriticalAudioError = async (error: AudioError) => {
    try {
      const { default: auditLogger } = await import('../../utils/auditLogger');
      
      await auditLogger.logEmergencyAccess(
        this.state.patientId || 'system',
        this.state.patientId || 'system',
        `Critical audio error: ${error.message}`,
        {
          error_code: error.code || error.name,
          session_id: this.state.sessionId,
          audio_context: error.audioContext,
          emergency_protocols_triggered: true,
          timestamp: new Date().toISOString()
        }
      );
    } catch (logError) {
      console.error('Failed to log critical audio error:', logError);
    }
  };

  /**
   * Manual retry handler
   */
  handleRetry = () => {
    if (this.state.recoveryAttempts < (this.props.maxRecoveryAttempts || 3)) {
      this.attemptRecovery();
    } else {
      // Reset recovery attempts and try again
      this.setState({ recoveryAttempts: 0 }, () => {
        this.attemptRecovery();
      });
    }
  };

  /**
   * Reset error boundary state
   */
  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      recoveryAttempts: 0,
      isRecovering: false,
      fallbackMode: 'none'
    });
  };

  componentWillUnmount() {
    if (this.recoveryTimer) {
      clearTimeout(this.recoveryTimer);
    }
    if (this.errorLogTimer) {
      clearTimeout(this.errorLogTimer);
    }
  }

  render() {
    if (this.state.hasError && this.state.error) {
      return this.renderErrorUI();
    }

    return this.props.children;
  }

  /**
   * Render error UI based on error severity and fallback mode
   */
  renderErrorUI() {
    const { error, fallbackMode, isRecovering, recoveryAttempts } = this.state;
    const maxAttempts = this.props.maxRecoveryAttempts || 3;

    // Emergency mode UI
    if (fallbackMode === 'emergency_mode') {
      return this.renderEmergencyModeUI();
    }

    // Text-only fallback mode UI
    if (fallbackMode === 'text_only') {
      return this.renderTextOnlyFallbackUI();
    }

    // Standard error UI
    return this.renderStandardErrorUI();
  }

  renderEmergencyModeUI() {
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 border-l-4 border-red-500">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle className="w-8 h-8 text-red-600" />
            <div>
              <h2 className="text-lg font-semibold text-red-900">
                Critical Audio Error
              </h2>
              <p className="text-sm text-red-700">
                Emergency protocols activated
              </p>
            </div>
          </div>

          <div className="bg-red-100 border border-red-200 rounded-lg p-4 mb-4">
            <p className="text-sm text-red-800 font-medium">
              ⚠️ Audio consultation temporarily unavailable
            </p>
            <p className="text-xs text-red-700 mt-1">
              Patient safety protocols have been triggered. Healthcare providers have been notified.
            </p>
          </div>

          <div className="space-y-3">
            <button
              onClick={() => this.props.onEmergencyStop?.('audio_critical_error')}
              className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded font-medium transition-colors flex items-center justify-center space-x-2"
            >
              <Phone className="w-4 h-4" />
              <span>Contact Emergency Services</span>
            </button>

            <button
              onClick={this.handleRetry}
              className="w-full border border-gray-300 hover:bg-gray-50 text-gray-700 px-4 py-2 rounded font-medium transition-colors"
              disabled={this.state.isRecovering}
            >
              {this.state.isRecovering ? 'Attempting Recovery...' : 'Retry Audio Connection'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  renderTextOnlyFallbackUI() {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
        <div className="flex items-center space-x-3 mb-3">
          <MicOff className="w-6 h-6 text-yellow-600" />
          <div>
            <h3 className="text-lg font-semibold text-yellow-900">
              Audio Temporarily Unavailable
            </h3>
            <p className="text-sm text-yellow-700">
              Consultation continuing in text mode
            </p>
          </div>
        </div>

        <div className="bg-yellow-100 border border-yellow-200 rounded-lg p-3 mb-3">
          <p className="text-sm text-yellow-800">
            <strong>Error:</strong> {this.state.error?.message}
          </p>
          <p className="text-xs text-yellow-700 mt-1">
            Recovery attempts: {this.state.recoveryAttempts}/{this.props.maxRecoveryAttempts || 3}
          </p>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={this.handleRetry}
            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded font-medium transition-colors flex items-center space-x-1"
            disabled={this.state.isRecovering || this.state.recoveryAttempts >= (this.props.maxRecoveryAttempts || 3)}
          >
            <RefreshCw className="w-3 h-3" />
            <span>{this.state.isRecovering ? 'Recovering...' : 'Retry Audio'}</span>
          </button>

          <button
            onClick={this.handleReset}
            className="px-3 py-1 border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm rounded font-medium transition-colors"
          >
            Continue Text Only
          </button>
        </div>

        {/* Render children in text-only mode */}
        <div className="mt-4 opacity-75">
          {this.props.children}
        </div>
      </div>
    );
  }

  renderStandardErrorUI() {
    const { error, isRecovering, recoveryAttempts } = this.state;
    const maxAttempts = this.props.maxRecoveryAttempts || 3;

    return (
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
        <div className="flex items-center space-x-3 mb-3">
          <AlertCircle className="w-6 h-6 text-orange-600" />
          <div>
            <h3 className="text-lg font-semibold text-orange-900">
              Audio Processing Error
            </h3>
            <p className="text-sm text-orange-700">
              {error?.severity === 'high' ? 'High priority error detected' : 'Audio error occurred'}
            </p>
          </div>
        </div>

        <div className="bg-orange-100 border border-orange-200 rounded-lg p-3 mb-3">
          <p className="text-sm text-orange-800">
            <strong>Error:</strong> {error?.message}
          </p>
          {error?.code && (
            <p className="text-xs text-orange-700 mt-1">
              <strong>Code:</strong> {error.code}
            </p>
          )}
          <p className="text-xs text-orange-700 mt-1">
            Recovery attempts: {recoveryAttempts}/{maxAttempts}
          </p>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={this.handleRetry}
            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded font-medium transition-colors flex items-center space-x-1"
            disabled={isRecovering}
          >
            <RefreshCw className="w-3 h-3" />
            <span>{isRecovering ? 'Recovering...' : 'Retry'}</span>
          </button>

          <button
            onClick={this.handleReset}
            className="px-3 py-1 border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm rounded font-medium transition-colors"
          >
            Reset
          </button>

          {this.props.enableFallbackMode && (
            <button
              onClick={this.enableFallbackMode}
              className="px-3 py-1 border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm rounded font-medium transition-colors"
            >
              Continue Without Audio
            </button>
          )}
        </div>
      </div>
    );
  }
}

export default AudioErrorBoundary;
