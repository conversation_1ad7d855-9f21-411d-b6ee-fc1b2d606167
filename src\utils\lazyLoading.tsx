/**
 * LAZY LOADING UTILITIES FOR MEDICAL COMPONENTS
 * 
 * This utility provides intelligent lazy loading for medical components
 * with emergency system performance prioritization and fallback mechanisms.
 * 
 * PERFORMANCE FEATURES:
 * - Route-based code splitting
 * - Role-based component loading
 * - Emergency system preloading
 * - Progressive loading with fallbacks
 * - Bundle size optimization
 * 
 * MEDICAL REQUIREMENTS:
 * - Emergency components must load immediately
 * - Critical medical functions have priority loading
 * - Offline fallbacks for essential components
 * - Loading states that don't block medical operations
 * - Error boundaries for failed lazy loads
 */

import React, { Suspense, ComponentType, LazyExoticComponent } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import type { UserRole } from '../types/auth';

interface LazyLoadOptions {
  readonly fallback?: React.ComponentType;
  readonly errorFallback?: React.ComponentType<{ error: Error; resetErrorBoundary: () => void }>;
  readonly preload?: boolean;
  readonly priority?: 'low' | 'normal' | 'high' | 'emergency';
  readonly requiredRole?: UserRole;
  readonly offlineFallback?: React.ComponentType;
}

interface LoadingFallbackProps {
  readonly componentName?: string;
  readonly priority?: 'low' | 'normal' | 'high' | 'emergency';
}

/**
 * Default loading fallback component
 */
const DefaultLoadingFallback: React.FC<LoadingFallbackProps> = ({ 
  componentName = 'Component', 
  priority = 'normal' 
}) => {
  const isEmergency = priority === 'emergency';
  
  return (
    <div className={`flex items-center justify-center p-8 ${isEmergency ? 'bg-red-50' : 'bg-gray-50'}`}>
      <div className="text-center">
        <div className={`animate-spin rounded-full h-8 w-8 border-b-2 mx-auto ${
          isEmergency ? 'border-red-600' : 'border-blue-600'
        }`}></div>
        <p className={`mt-4 text-sm ${isEmergency ? 'text-red-700' : 'text-gray-600'}`}>
          {isEmergency ? '🚨 Loading Emergency System...' : `Loading ${componentName}...`}
        </p>
        {isEmergency && (
          <p className="mt-2 text-xs text-red-600">
            Critical medical component loading
          </p>
        )}
      </div>
    </div>
  );
};

/**
 * Default error fallback component
 */
const DefaultErrorFallback: React.FC<{ error: Error; resetErrorBoundary: () => void }> = ({ 
  error, 
  resetErrorBoundary 
}) => (
  <div className="flex items-center justify-center p-8 bg-red-50">
    <div className="text-center max-w-md">
      <div className="text-red-600 mb-4">
        <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-red-800 mb-2">
        Component Loading Failed
      </h3>
      <p className="text-sm text-red-600 mb-4">
        {error.message}
      </p>
      <button
        onClick={resetErrorBoundary}
        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
      >
        Retry Loading
      </button>
    </div>
  </div>
);

/**
 * Create a lazy-loaded component with medical-specific optimizations
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): LazyExoticComponent<T> {
  const {
    fallback: CustomFallback,
    errorFallback: CustomErrorFallback,
    preload = false,
    priority = 'normal'
  } = options;

  // Create the lazy component
  const LazyComponent = React.lazy(importFn);

  // Preload if requested or if emergency priority
  if (preload || priority === 'emergency') {
    importFn().catch(error => {
      console.error('Failed to preload component:', error);
    });
  }

  return LazyComponent;
}

/**
 * Wrapper component that adds error boundaries and loading states
 */
export function withLazyWrapper<T extends ComponentType<any>>(
  LazyComponent: LazyExoticComponent<T>,
  options: LazyLoadOptions = {}
): React.FC<React.ComponentProps<T>> {
  const {
    fallback: CustomFallback,
    errorFallback: CustomErrorFallback,
    priority = 'normal',
    offlineFallback: OfflineFallback
  } = options;

  return (props: React.ComponentProps<T>) => {
    // Check if offline and offline fallback is available
    if (!navigator.onLine && OfflineFallback) {
      return <OfflineFallback {...props} />;
    }

    return (
      <ErrorBoundary
        FallbackComponent={CustomErrorFallback || DefaultErrorFallback}
        onError={(error, errorInfo) => {
          console.error('Lazy component error:', error, errorInfo);
          
          // Log error for medical review if it's a critical component
          if (priority === 'emergency' || priority === 'high') {
            // This would integrate with your audit logging system
            console.error('Critical medical component failed to load:', error);
          }
        }}
      >
        <Suspense 
          fallback={
            CustomFallback ? 
              <CustomFallback /> : 
              <DefaultLoadingFallback priority={priority} />
          }
        >
          <LazyComponent {...props} />
        </Suspense>
      </ErrorBoundary>
    );
  };
}

/**
 * Role-based lazy loading for medical components
 */
export function createRoleBasedLazyComponent<T extends ComponentType<any>>(
  roleImports: Partial<Record<UserRole, () => Promise<{ default: T }>>>,
  defaultImport: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): React.FC<React.ComponentProps<T> & { userRole?: UserRole }> {
  return ({ userRole = 'patient', ...props }: React.ComponentProps<T> & { userRole?: UserRole }) => {
    const importFn = roleImports[userRole as UserRole] || defaultImport;
    const LazyComponent = createLazyComponent(importFn, options);
    const WrappedComponent = withLazyWrapper(LazyComponent, options);
    
    return <WrappedComponent {...props} />;
  };
}

/**
 * Preload critical medical components
 */
export function preloadCriticalComponents(): void {
  // Preload emergency components
  const emergencyComponents = [
    () => import('../components/emergency/EmergencyProtocols'),
    () => import('../components/medical/CriticalVitals'),
    () => import('../components/consultation/EmergencyConsultation')
  ];

  emergencyComponents.forEach(importFn => {
    importFn().catch(error => {
      console.error('Failed to preload emergency component:', error);
    });
  });
}

/**
 * Progressive loading manager for medical components
 */
export class MedicalComponentLoader {
  private loadedComponents: Set<string> = new Set();
  private loadingComponents: Map<string, Promise<any>> = new Map();
  private priorityQueue: Array<{ name: string; importFn: () => Promise<any>; priority: number }> = [];

  /**
   * Add component to loading queue with priority
   */
  queueComponent(
    name: string, 
    importFn: () => Promise<any>, 
    priority: 'low' | 'normal' | 'high' | 'emergency' = 'normal'
  ): void {
    if (this.loadedComponents.has(name) || this.loadingComponents.has(name)) {
      return;
    }

    const priorityValue = {
      'emergency': 4,
      'high': 3,
      'normal': 2,
      'low': 1
    }[priority];

    this.priorityQueue.push({ name, importFn, priority: priorityValue });
    this.priorityQueue.sort((a, b) => b.priority - a.priority);

    // Start loading immediately for emergency components
    if (priority === 'emergency') {
      this.loadComponent(name, importFn);
    }
  }

  /**
   * Load component with caching
   */
  async loadComponent(name: string, importFn: () => Promise<any>): Promise<any> {
    if (this.loadedComponents.has(name)) {
      return;
    }

    if (this.loadingComponents.has(name)) {
      return this.loadingComponents.get(name);
    }

    const loadingPromise = importFn()
      .then(module => {
        this.loadedComponents.add(name);
        this.loadingComponents.delete(name);
        return module;
      })
      .catch(error => {
        this.loadingComponents.delete(name);
        console.error(`Failed to load component ${name}:`, error);
        throw error;
      });

    this.loadingComponents.set(name, loadingPromise);
    return loadingPromise;
  }

  /**
   * Process loading queue
   */
  async processQueue(maxConcurrent: number = 3): Promise<void> {
    const loading: Promise<any>[] = [];

    while (this.priorityQueue.length > 0 && loading.length < maxConcurrent) {
      const { name, importFn } = this.priorityQueue.shift()!;
      loading.push(this.loadComponent(name, importFn));
    }

    if (loading.length > 0) {
      await Promise.allSettled(loading);
      
      // Continue processing if there are more components
      if (this.priorityQueue.length > 0) {
        await this.processQueue(maxConcurrent);
      }
    }
  }

  /**
   * Get loading statistics
   */
  getStats(): { loaded: number; loading: number; queued: number } {
    return {
      loaded: this.loadedComponents.size,
      loading: this.loadingComponents.size,
      queued: this.priorityQueue.length
    };
  }
}

// Global component loader instance
export const medicalComponentLoader = new MedicalComponentLoader();

/**
 * Hook for component loading status
 */
export function useComponentLoadingStatus(componentName: string): {
  isLoaded: boolean;
  isLoading: boolean;
  error: Error | null;
} {
  const [status, setStatus] = React.useState({
    isLoaded: false,
    isLoading: false,
    error: null as Error | null
  });

  React.useEffect(() => {
    const checkStatus = () => {
      const stats = medicalComponentLoader.getStats();
      setStatus({
        isLoaded: stats.loaded > 0,
        isLoading: stats.loading > 0,
        error: null
      });
    };

    const interval = setInterval(checkStatus, 1000);
    return () => clearInterval(interval);
  }, [componentName]);

  return status;
}
